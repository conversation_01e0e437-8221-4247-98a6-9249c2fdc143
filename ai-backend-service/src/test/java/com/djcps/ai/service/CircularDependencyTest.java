package com.djcps.ai.service;

import com.djcps.ai.service.system.SystemConfigService;
import com.djcps.ai.service.system.functionFilter.WhiteListFunctionFilterImpl;
import com.djcps.ai.service.strategy.SQLResponseProcessor;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * 测试循环依赖是否已经解决
 */
@SpringBootTest
@ActiveProfiles("test")
public class CircularDependencyTest {

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private WhiteListFunctionFilterImpl whiteListFunctionFilter;

    @Autowired
    private SQLResponseProcessor sqlResponseProcessor;

    @Test
    public void testCircularDependencyResolved() {
        // 验证所有Bean都能正常注入
        assertNotNull(systemConfigService, "SystemConfigService should be injected");
        assertNotNull(whiteListFunctionFilter, "WhiteListFunctionFilterImpl should be injected");
        assertNotNull(sqlResponseProcessor, "SQLResponseProcessor should be injected");
        
        System.out.println("循环依赖已成功解决！所有Bean都能正常注入。");
    }
}
