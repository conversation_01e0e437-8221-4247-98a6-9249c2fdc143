package com.djcps.ai.service.system.functionFilter;

import cn.hutool.core.util.StrUtil;
import com.djcps.ai.service.report.param.SourceParam;
import com.djcps.ai.service.system.FunctionInfoFilter;
import org.springframework.stereotype.Service;

@Service("filter_role")
public class RoleFunctionFilterImpl implements FunctionFilter{

    @Override
    public boolean apply(FunctionInfoFilter filter, SourceParam target) {
        if (filter == null || filter.getFilter() == null) {
            return true;
        }
        String filterValue = filter.getFilter();
        if (StrUtil.isBlankIfStr(filterValue)) {
            return true;
        }
        if (target == null) {
            return false;
        }
        return StrUtil.equalsIgnoreCase(filterValue, target.getRole());
    }
}
