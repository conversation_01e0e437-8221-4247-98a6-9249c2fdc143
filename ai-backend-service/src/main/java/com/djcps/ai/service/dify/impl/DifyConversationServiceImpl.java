package com.djcps.ai.service.dify.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.ttl.TtlRunnable;
import com.djcps.ai.common.threadlocal.AgentThreadLocalUtil;
import com.djcps.ai.core.client.DifyFeignClient;
import com.djcps.ai.core.param.dify.SendMsgParam;
import com.djcps.ai.core.vo.SendMsgVo;
import com.djcps.ai.core.vo.in.*;
import com.djcps.ai.core.vo.out.DataVo;
import com.djcps.ai.core.vo.out.DifyCoversationPageOutDto;
import com.djcps.ai.core.vo.out.DifyEditOutDto;
import com.djcps.ai.core.vo.out.DifyMessagePageOutDto;
import com.djcps.ai.dao.entity.UserFavorite;
import com.djcps.ai.service.UserFavoriteService;
import com.djcps.ai.service.dify.DifyConversationService;
import com.djcps.ai.service.report.response.SubmitQueryResponse;
import com.djcps.ai.service.strategy.ResponseProcessBaseProcessor;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;
import java.util.concurrent.CompletableFuture;


/**
 * 类 DifyConversactionServiceImpl.java 描述: dify会话实现
 *
 * @author: yeb 2024/7/15 下午3:54
 **/
@Service
@Slf4j
@AllArgsConstructor
public class DifyConversationServiceImpl implements DifyConversationService {

    private DifyFeignClient difyFeignClient;
    private UserFavoriteService favoriteService;
    private ResponseProcessBaseProcessor responseProcessBaseProcessor;
    private Environment environment;


    @Override
    public SendMsgVo sendMessage(SendMsgParam param) {
        SendMsgVo outVo = difyFeignClient.sendMessage(param);
        return outVo;
    }

    @Override
    public SubmitQueryResponse sendMessageAndExecute(SendMsgParam param) {
        SendMsgVo sendMsgVo = sendMessage(param);
        return responseProcessBaseProcessor.process(param, sendMsgVo);
    }
    public static ObjectMapper mapper = new ObjectMapper();
    public static String buildStreamParam(SendMsgParam param) {
        if (param.getInputs() == null) {
            param.setInputs(new HashMap<>());
        }
        param.getInputs().put("userId", param.getUser());
        param.getInputs().put("token", AgentThreadLocalUtil.getToken());
        param.setResponseMode("streaming");
        try {
            return  mapper.writeValueAsString(param);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }
    public static final String URL = "{}/v1/chat-messages";
    public String buildUrl() {
        String property = environment.getProperty("dify.url");
        return StrUtil.format(URL, property);
    }

    public String getDifyAppKey() {
        return environment.getProperty("dify.key." + AgentThreadLocalUtil.getFunctionName());
    }

    @Override
    public SseEmitter sendMessageAndExecuteStream(SendMsgParam param) {
        log.debug("开始处理流式会话请求, param: {}", param);
        SseEmitter emitter = new SseEmitter(-1L);

        CompletableFuture.runAsync(TtlRunnable.get(() -> {
            String jsonStr = buildStreamParam(param);
            log.debug("构建的请求参数: {}", jsonStr);
            SseEmitter.SseEventBuilder msgEvent;
            try (
                    InputStream responseStream = HttpRequest.post(buildUrl())
                            .bearerAuth(getDifyAppKey())
                            .body(jsonStr)
                            .executeAsync()
                            .bodyStream()
            ) {

                log.debug("成功建立流式连接,传输中,请等待...");
                final DifyAgentConverter agentConverter = new DifyAgentConverter(emitter, AgentThreadLocalUtil.getFunctionName());
                BufferedReader reader = new BufferedReader(new InputStreamReader(responseStream));
                String line;
                while ((line = reader.readLine()) != null) {
                    log.debug(line);
                    if (line.startsWith("data:")) {
                        String responseData = line.substring(5).trim();
                        if (JSONUtil.isJson(responseData)) {
                            JSONObject json = JSONUtil.parseObj(responseData);
                            String event = json.getStr("event");

                            switch (event) {
                                case "message":
                                    String answer = json.getStr("answer");
                                    if (answer != null) {
//                                        log.debug("发送消息事件, answer: {}", answer);
                                        // 修改：使用MediaType指定内容类型
                                        msgEvent = SseEmitter.event()
                                                .name("send")
                                                .data(answer, MediaType.TEXT_PLAIN);
                                        emitter.send(msgEvent);
                                    }
                                    break;
                                case "agent_message":
                                case "agent_thought":
                                    agentConverter.message(json);
                                    break;
                                case "workflow_started":
                                    String messageId = json.getStr("message_id");
                                    String conversationId = json.getStr("conversation_id");
                                    if (StrUtil.isNotBlank(messageId) && StrUtil.isNotBlank(conversationId)) {
//                                        log.debug("发送消息事件, answer: {}", answer);
                                        // 修改：使用MediaType指定内容类型
                                        msgEvent = SseEmitter.event()
                                                .name("conversationId")
                                                .data(conversationId, MediaType.TEXT_PLAIN);
                                        emitter.send(msgEvent);
                                        msgEvent = SseEmitter.event()
                                                .name("messageId")
                                                .data(messageId, MediaType.TEXT_PLAIN);
                                        emitter.send(msgEvent);
                                    }else {
                                        log.debug("会话id或者消息id为空,conversationId:{},messageId:{}", conversationId,messageId);
                                    }
                                    break;
                                case "workflow_finished":
                                    Object finalData = json.getByPath("data.outputs.answer");
                                    if (finalData != null) {
//                                        log.debug("发送完成事件, finalData: {}", finalData);
                                        msgEvent = SseEmitter.event()
                                                .name("complete")
                                                .data(finalData, MediaType.TEXT_PLAIN);
                                        emitter.send(msgEvent);
                                    }
                                    break;
                                case "message_end":
                                    agentConverter.complete(json);
                                    break;
                            }
                        } else {
                            log.warn("收到非JSON格式数据: {}", responseData);
                        }
                    }
                }
                log.debug("数据流读取完成，关闭连接");
                emitter.complete();

            } catch (Exception e) {
                log.error("流处理发生错误: ", e);
                emitter.completeWithError(e);
            }
        }));

        return emitter;
    }

    @Override
    public DifyEditOutDto renameConversations(String conversation_id, DifyConversationInDto inVo) {
        DifyEditOutDto outVo = difyFeignClient.renameConversations(conversation_id,inVo);
        return outVo;
    }

    @Override
    public DifyCoversationPageOutDto getAllConversations(DifyConversationsInDto dto) {
        DifyCoversationPageOutDto resultDto = new DifyCoversationPageOutDto();
        String roleFilter = dto.getRole();
        if (!StrUtil.equalsIgnoreCase(AgentThreadLocalUtil.getFunctionName(),"agent")||StrUtil.isBlank(roleFilter)) {
            return difyFeignClient.getAllConversations(dto);
        }

        int limit = dto.getLimit();
        String lastId = null;
        List<DifyCoversationPageOutDto.DataVo> resultList = new ArrayList<>();
        while (resultList.size() < limit) {
            dto.setLast_id(lastId);
            DifyCoversationPageOutDto currentPage = difyFeignClient.getAllConversations(dto);
            List<DifyCoversationPageOutDto.DataVo> dataList = currentPage.getData();

            if (CollUtil.isEmpty(dataList)) {
                break; // 没有更多数据，终止查询
            }

            for (DifyCoversationPageOutDto.DataVo vo : dataList) {
                Map<String, Object> inputs = vo.getInputs();
                if (StrUtil.isBlank(roleFilter) || (inputs != null && roleFilter.equals(inputs.get("role")))) {
                    resultList.add(vo);
                    if (resultList.size() >= limit) {
                        break; // 达到指定条数，终止查询
                    }
                }
            }

            if (!currentPage.isHas_more() || resultList.size() >= limit) {
                break; // 没有更多数据或达到指定条数，终止查询
            }

            lastId = dataList.get(dataList.size() - 1).getId(); // 更新 lastId 进行下一页查询
        }

        resultDto.setData(resultList);
        resultDto.setHas_more(resultList.size() < limit ? false : true);
        resultDto.setLimit(limit);
        return resultDto;
    }

    @Override
    public DifyEditOutDto messageFeedbacks(String messageId , DifyFeedbackInDto inVo) {
        DifyEditOutDto outDto = difyFeignClient.messageFeedbacks(messageId,inVo);
        return outDto;
    }

    @Override
    public DifyMessagePageOutDto getAllMessage(DifyMessageInDto inVo) {
        DifyMessagePageOutDto outDto = difyFeignClient.getAllMessage(inVo);
        List<DataVo> dataList = outDto.getData();
        if (CollUtil.isEmpty(dataList)) {
            return outDto;
        }
        for (DataVo vo : dataList) {
            try {
                SendMsgParam param = new SendMsgParam();
                param.setQuery(vo.getQuery());
                param.setUser(inVo.getUser());
                SendMsgVo msgVo = new SendMsgVo();
                msgVo.setAnswer(vo.getAnswer());
                msgVo.setConversationId(inVo.getConversation_id());
                msgVo.setMessageId(vo.getId());
                SubmitQueryResponse process = responseProcessBaseProcessor.process(param, msgVo);
                vo.setResponse(process);
                Optional<UserFavorite> favorite = favoriteService.getFavorite(inVo.getUser(), vo.getConversation_id(), vo.getId());
                favorite.ifPresent(userFavorite -> {
                    vo.setFavorite(true);
                    vo.setFavoriteId(userFavorite.getId());
                });
            } catch (Exception e) {
                log.error("getAllMessage error", e);
            }
        }
        return outDto;
    }

    @Override
    public DifyEditOutDto deleteConversation(String conversationId, DifyUserInDto inVo) {
        DifyEditOutDto outDto = difyFeignClient.deleteConversation(conversationId, inVo);
        return outDto;
    }

}
