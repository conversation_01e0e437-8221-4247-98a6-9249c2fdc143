package com.djcps.ai.service.dify.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
public class DifyAgentConverter implements DifyConverertExtra{
//    private static final String PLAN_TIPS = "<plan>查询相关资料，分析汇总进行中...</plan>";
    private static final String PLAN_TIPS = "";

//    private static String TASK_TAG_PREFIX = "<analysis>";
    private static String TASK_TAG_PREFIX = "";
//    private static String TASK_TAG_POSTFIX = "</analysis>";
    private static String TASK_TAG_POSTFIX = "";
    private AtomicBoolean initedAck = new AtomicBoolean(Boolean.FALSE);

    /**
     * 流式输出通道
     */
    private SseEmitter emitter;
    /**
     * 消息模式下最后一条消息，用于agent message_end时，里面没有全部的情况
     */
    private String lastestResponse;

    private String paramFunctionName;

    public DifyAgentConverter(SseEmitter emitter, String paramFunctionName) {
        this.emitter = emitter;
        this.paramFunctionName = paramFunctionName;
    }


    /**
     * 消息事件
     *
     * @param json
     * @throws IOException
     */
    public void message(JSONObject json) throws IOException {
        if(!filter()) {
            return;
        }

        if (initedAck.compareAndSet(false, true)) {
            start(json);
            send(PLAN_TIPS);
            send(TASK_TAG_PREFIX);
        }
        String answer = getWithMultiKey(json, "answer", "thought");
        send(answer);
        this.lastestResponse = answer;
    }

    /**
     * 结束事件
     *
     * @param json
     */
    public void complete(JSONObject json) throws IOException {
        if(!filter()) {
            return;
        }
        //taskdata end
        send(TASK_TAG_POSTFIX);

        //完整数据
        //先判断是否有metadata，如果没有使用lastestmessage
        String fullContent = getFullContentOrLasestMessage(json);
        end(TASK_TAG_PREFIX + fullContent + TASK_TAG_POSTFIX);
    }

    public boolean filter() {
        return "faq_yzx".equals(this.paramFunctionName);
    }


    /**
     * 回复返回前，先把messageid和conversationid返回
     *
     * @param json
     * @throws IOException
     */
    private void start(JSONObject json) throws IOException {
        SseEmitter.SseEventBuilder msgEvent;
        String messageId = json.getStr("message_id");
        String conversationId = json.getStr("conversation_id");
        if (StrUtil.isNotBlank(messageId) && StrUtil.isNotBlank(conversationId)) {
            // 修改：使用MediaType指定内容类型
            msgEvent = SseEmitter.event()
                    .name("conversationId")
                    .data(conversationId, MediaType.TEXT_PLAIN);
            emitter.send(msgEvent);
            msgEvent = SseEmitter.event()
                    .name("messageId")
                    .data(messageId, MediaType.TEXT_PLAIN);
            emitter.send(msgEvent);
        } else {
            log.debug("会话id或者消息id为空,conversationId:{},messageId:{}", conversationId, messageId);
        }
    }

    /**
     * 从json中取value，支持多个key
     *
     * @param json
     * @param keys
     * @return
     */
    private String getWithMultiKey(JSONObject json, String... keys) {
        for (String key : keys) {
            String value = json.getStr(key);
            if (StringUtils.isNotEmpty(value)) {
                return value;
            }
        }
        return "";
    }

    /**
     * agent添加Plan部分
     */
    private void send(String info) throws IOException {
        if (StringUtils.isEmpty(info)) {
            return;
        }

        sse("send", info);
    }


    /**
     * 回复结束
     *
     * @param finalData
     * @throws IOException
     */
    private void end(String finalData) throws IOException {
        sse("complete", finalData);
    }


    /**
     * 完成时，取出全部的消息
     *
     * @param json
     * @return
     */
    private String getFullContentOrLasestMessage(JSONObject json) {
        if(StringUtils.isNotEmpty(this.lastestResponse)) {
            return this.lastestResponse;
        }
        //下面的大概率不会执行到，因为agent模式，最后一条就是全部
        Object allResources = JSONUtil.getByPath(json, "$.metadata.retriever_resources");
        if (allResources == null) {
            return lastestResponse;
        }

        if (!(allResources instanceof JSONArray)) {
            return lastestResponse;
        }
        StringBuilder fullBuilder = new StringBuilder();
        JSONArray resouresArray = (JSONArray) allResources;
        for (int i = 0; i < resouresArray.size(); i++) {
            Object ele = resouresArray.get(i);
            if (!(ele instanceof JSONObject)) {
                fullBuilder.append(String.valueOf(ele));
                continue;
            }

            JSONObject segment = (JSONObject) ele;
            fullBuilder.append(segment.getStr("content"));
        }

        return fullBuilder.toString();
    }

    private void sse(String eventName, String data) throws IOException {
        SseEmitter.SseEventBuilder msgEvent = SseEmitter.event()
                .name(eventName)
                .data(data, MediaType.TEXT_PLAIN);
        emitter.send(msgEvent);
    }
}
