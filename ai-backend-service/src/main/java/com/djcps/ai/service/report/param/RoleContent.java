package com.djcps.ai.service.report.param;

import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RoleContent {
    /**
     * 提交的问题
     */
    private String role;
    /**
     * 会话id,前端随机生成uuid
     */
    private String content;

    public static void main(String args[]) {
        PromptMsg msg = new PromptMsg();
        List<RoleContent> list = new ArrayList<>();
        list.add(new RoleContent("system", "The user provides a question and you provide SQL. You will only respond with SQL code and not with any explanations.\n\nRespond with only SQL code. Do not answer with any explanations -- just the code.\n\nYou may use the following DDL statements as a reference for what tables might be available. Use responses to past questions also to guide you:\n\nCREATE TABLE `crm_area_market` (\n  `id` char(32) NOT NULL COMMENT '唯一id',\n  `rowid` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',\n  `isdel` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除(0-未删除,1-已删除)',\n  `isdel_per` char(32) DEFAULT '' COMMENT '删除人',\n  `skey` char(32) NOT NULL COMMENT '拆分键',\n  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',\n  `area_code` int(10) NOT NULL COMMENT '区域编号',\n  `area_name` varchar(20) NOT NULL COMMENT '区域名称',\n  `total_available_market` int(7) DEFAULT NULL COMMENT '产品市场总量(日均)',\n  PRIMARY KEY (`rowid`),\n  UNIQUE KEY `uidx_id` (`id`),\n  KEY `idx_area_code` (`area_code`)\n) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COMMENT='区域市场总量表'\n\n\n\n"));
        list.add(new RoleContent("user", "西湖区销售总量"));
        msg.setMessages(list);
        msg.setTemperature("0.3");
        msg.setModel("gpt-3.5-turbo");
        System.out.println(JSONUtil.toJsonStr(msg));
    }
}
