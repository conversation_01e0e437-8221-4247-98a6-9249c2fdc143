package com.djcps.ai.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.djcps.ai.common.threadlocal.CommonParam;
import com.djcps.ai.common.threadlocal.ThreadLocalUser;
import com.djcps.ai.core.vo.in.UserReportPoliceDto;
import com.djcps.ai.dao.entity.UserReportPolice;
import com.djcps.ai.dao.mapper.UserReportPoliceMapper;
import org.springframework.stereotype.Service;

@Service
public class UserReportPoliceService extends ServiceImpl<UserReportPoliceMapper, UserReportPolice> {


    public int saveReportPolice(UserReportPoliceDto dto) {
        UserReportPolice police = new UserReportPolice();
        BeanUtil.copyProperties(dto, police);
        CommonParam commonParam = ThreadLocalUser.getCommonParam();
        if (commonParam != null) {
            police.setPhone(commonParam.getFphone());
        }
        police.setUserId(ThreadLocalUser.getUserId());
        police.setSource(commonParam.getSource());
        police.setStatus(0);
        return baseMapper.insert(police);
    }

    public IPage<UserReportPolice> pageQuery(long current, long size, String userId, String status, String itemName, String functionName) {
        Page<UserReportPolice> page = new Page<>(current, size);

        LambdaQueryWrapper<UserReportPolice> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper.eq(StrUtil.isNotBlank(userId), UserReportPolice::getUserId, userId);
        queryWrapper.eq(StrUtil.isNotBlank(status), UserReportPolice::getStatus, status);
        queryWrapper.like(StrUtil.isNotBlank(itemName), UserReportPolice::getItemName, itemName);
        queryWrapper.like(StrUtil.isNotBlank(functionName), UserReportPolice::getFunctionName, functionName);

        // 按创建时间倒序排列
        queryWrapper.orderByDesc(UserReportPolice::getCreateTime);

        return page(page, queryWrapper);
    }
}
