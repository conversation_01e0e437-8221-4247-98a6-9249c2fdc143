package com.djcps.ai.service.system.functionFilter;

import cn.hutool.core.collection.CollUtil;
import com.djcps.ai.common.threadlocal.ThreadLocalUser;
import com.djcps.ai.service.report.param.SourceParam;
import com.djcps.ai.service.report.response.SystemConfigResponse;
import com.djcps.ai.service.system.FunctionInfoFilter;
import com.djcps.ai.service.system.SystemConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("filter_white_list")
public class WhiteListFunctionFilterImpl implements FunctionFilter{

    @Autowired
    @Lazy
    private SystemConfigService configService;

    @Override
    public boolean apply(FunctionInfoFilter filter, SourceParam target) {
        //白名单过滤
        String filterValue = filter.getFilter();
        SystemConfigResponse byKeyAndParse = configService.findByKeyAndParse(filterValue);
        if (byKeyAndParse == null) {
            return true;
        }
        Object object = byKeyAndParse.getValue();
        if (!(object instanceof List)) {
            return true;
        }
        List whiteList = (List) object;
        if (CollUtil.isEmpty(whiteList)) {
            return true;
        }
        //如果白名单不包含当前用户,则过滤掉
        return whiteList.contains(ThreadLocalUser.getUserId());
    }
}
