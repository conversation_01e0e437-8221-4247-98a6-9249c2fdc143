package com.djcps.ai.service.system;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.djcps.ai.common.threadlocal.ThreadLocalUser;
import com.djcps.ai.dao.entity.SystemConfig;
import com.djcps.ai.dao.mapper.SystemConfigMapper;
import com.djcps.ai.service.report.param.SourceParam;
import com.djcps.ai.service.report.param.SystemConfigParam;
import com.djcps.ai.service.report.response.SystemConfigResponse;
import com.djcps.ai.service.system.functionFilter.FunctionFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.djcps.ai.core.constants.CommonConstants.*;

@Service
@Slf4j
public class SystemConfigService extends ServiceImpl<SystemConfigMapper, SystemConfig> {

    @Autowired
    private Map<String, SystemConfigTypeParse> parseMap;
    @Autowired
    private Map<String, FunctionFilter> filterMap;

    public SystemConfigResponse findByKeyAndParse(SystemConfigParam param) {
        if (param == null || StrUtil.isBlankIfStr(param.getKey())) {
            return SystemConfigResponse.EMPTY;
        }
        return findByKeyAndParse(param.getKey());
    }

    public SystemConfigResponse findByKeyAndParse(String key) {
        SystemConfig config = findByKey(key);
        if (config == null || StrUtil.isBlank(config.getConfigKey()) || StrUtil.isBlank(config.getConfigValue())) {
            return SystemConfigResponse.EMPTY;
        }
        //根据配置类型进行解析
        SystemConfigTypeParse parse = parseMap.get(config.getConfigType());
        if (parse == null) {
            log.warn("未找到配置类型解析器,config:{}", config.getConfigType());
        }
        log.debug("使用解析器进行解析,parse:{}", parse.getClass());
        Object parseResult = parse.parse(config.getConfigValue());
        return new SystemConfigResponse(config.getConfigType(), parseResult);
    }

    public SystemConfig findByKey(String key) {
        return lambdaQuery().eq(SystemConfig::getConfigKey, key).one();
    }

    public String findValueByKeyDefault(String key, String defaultValue) {
        SystemConfig one = lambdaQuery().eq(SystemConfig::getConfigKey, key).one();
        if (one == null || StrUtil.isBlankIfStr(one.getConfigValue())) {
            return defaultValue;
        }
        return one.getConfigValue();
    }




    public SystemConfigResponse getAuthFunctionListV2(SourceParam sourceParam) {
        String source = sourceParam.getSource();
        String format = StrUtil.format(SYSTEM_CONFIG_KEY_ENABLE_FUNCTIONS_SOURCE, source);
        log.info("获取功能列表,source:{},format:{}", source, format);
        SystemConfigResponse response = findByKeyAndParse(format);
        if (response == null) {
            return SystemConfigResponse.EMPTY;
        }
        //如果不是easyorder则直接返回
        if (!StrUtil.equalsIgnoreCase(source, "easyorder")) {
            return response;
        }
        Object value = response.getValue();
        if (!(value instanceof JSONArray)) {
            return response;
        }
        JSONArray jsonArray = (JSONArray) value;
        if (CollUtil.isEmpty(jsonArray)) {
            return response;
        }
        List<FunctionInfo> list = jsonArray.toList(FunctionInfo.class);

        //role过滤
        list = list.stream()
                .filter(functionInfo -> {
                    List<FunctionInfoFilter> filters = functionInfo.getFilters();
                    if (CollUtil.isEmpty(filters)) {
                        return true;
                    }
                    log.debug("AiFunctionInfo: {}", functionInfo);
                    List<Boolean> resultSet = new ArrayList<>(filters.size());
                    for (FunctionInfoFilter filter : filters) {
                        if (filter == null || StrUtil.isBlankIfStr(filter.getType())|| StrUtil.isBlankIfStr(filter.getFilter())) {
                            resultSet.add(true);
                            continue;
                        }
                        String type = filter.getType();
                        if (!filterMap.containsKey(type)) {
                            resultSet.add(true);
                            continue;
                        }
                         try {
                             boolean apply =  filterMap.get(type).apply(filter, sourceParam);
                             log.debug("过滤器类型: {}, 过滤器参数: {}, 过滤器结果: {}", type, filter, apply);
                             resultSet.add(apply);
                        } catch (Exception exception) {
                            log.error("过滤器执行异常,过滤器类型: {}, 过滤器参数: {}, 异常信息: {}", type, filter, exception);
                             resultSet.add(false);
                        }
                    }

                    // 要求所有的过滤器都通过才返回true
                    return resultSet.stream().allMatch(result -> result);
                }).collect(Collectors.toList());
        response.setValue(list);



        Optional<FunctionInfo> first = list.stream().filter(info -> StrUtil.equalsIgnoreCase(info.getFunctionName(), SYSTEM_CONFIG_KEY_ENABLE_FUNCTIONS_AI_REPORT)).findFirst();
        //存在则进行白名单校验,不存在则直接返回
        if (!first.isPresent()) {
            return response;
        }
        //包含ai报表则需要进行白名单校验
        if (!checkCurrentUserInWhiteList(ThreadLocalUser.getUserId())) {
            CollUtil.removeAny(list, first.get());
        }
        return response;
    }

    /**
     * 判断用户userId是否在白名单中
     *
     * @return
     */
    public boolean checkCurrentUserInWhiteList(String userId) {
        SystemConfigResponse authList = findByKeyAndParse(SYSTEM_CONFIG_KEY_WHITE_LIST_AI_REPORT);
        //白名单为空则不进行校验,直接返回false表示不在白名单中,需要拦截.
        if (authList == null) {
            return false;
        }
        Object authValue = authList.getValue();
        if (!(authValue instanceof List)) {
            return false;
        }
        List<String> authListValue = (List<String>) authValue;
        if (CollUtil.isEmpty(authListValue)) {
            return false;
        }
        return authListValue.contains(userId);
    }
}
