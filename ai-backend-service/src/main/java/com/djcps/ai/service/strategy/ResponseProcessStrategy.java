package com.djcps.ai.service.strategy;

import com.djcps.ai.core.param.dify.SendMsgParam;
import com.djcps.ai.core.vo.SendMsgVo;
import com.djcps.ai.service.report.response.SubmitQueryResponse;

/**
 * 根据调用返回的消息内容,有不同的处理策略
 */
public interface ResponseProcessStrategy {
    /**
     * 是否由该处理器进行处理
     *
     * @param msgVo 消息
     * @return
     */
    boolean match(SendMsgParam param, SendMsgVo msgVo);

    /**
     * 处理响应
     *
     * @param response 响应
     *                 * @param msgVo    消息
     */
    void process(SubmitQueryResponse response, SendMsgVo msgVo);

}
