package com.djcps.ai.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.djcps.ai.common.vo.PageResult;
import com.djcps.ai.dao.entity.UserTaskRunRecord;
import com.djcps.ai.dao.mapper.UserTaskRunRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务执行记录服务类
 */
@Service
@Slf4j
public class UserTaskRunRecordService extends ServiceImpl<UserTaskRunRecordMapper, UserTaskRunRecord> {

    /**
     * 创建任务执行记录
     *
     * @param taskId   任务ID
     * @param batchNo  批次号
     * @param execType 执行类型
     * @return 执行记录ID
     */
    public Long createRunRecord(Long taskId, String batchNo, UserTaskRunRecord.ExecType execType) {
        UserTaskRunRecord record = new UserTaskRunRecord();
        record.setTaskId(taskId);
        record.setBatchNo(batchNo);
        record.setExecType(execType);
        record.setExecStatus(UserTaskRunRecord.ExecStatus.RUNNING);
        record.setExecStartTime(LocalDateTime.now());
        record.setResultCount(0);
        
        save(record);
        log.info("创建任务执行记录，taskId: {}, batchNo: {}, execType: {}, recordId: {}", 
                taskId, batchNo, execType, record.getId());
        return record.getId();
    }

    /**
     * 更新执行记录为成功状态
     *
     * @param recordId    记录ID
     * @param resultCount 处理的结果项数量
     */
    public void updateRecordSuccess(Long recordId, Integer resultCount) {
        UserTaskRunRecord record = new UserTaskRunRecord();
        record.setId(recordId);
        record.setExecStatus(UserTaskRunRecord.ExecStatus.SUCCESS);
        record.setExecEndTime(LocalDateTime.now());
        record.setResultCount(resultCount);
        
        updateById(record);
        log.info("更新任务执行记录为成功状态，recordId: {}, resultCount: {}", recordId, resultCount);
    }

    /**
     * 更新执行记录为失败状态
     *
     * @param recordId     记录ID
     * @param errorMessage 错误信息
     */
    public void updateRecordFailed(Long recordId, String errorMessage) {
        UserTaskRunRecord record = new UserTaskRunRecord();
        record.setId(recordId);
        record.setExecStatus(UserTaskRunRecord.ExecStatus.FAILED);
        record.setExecEndTime(LocalDateTime.now());
        record.setErrorMessage(errorMessage);
        
        updateById(record);
        log.info("更新任务执行记录为失败状态，recordId: {}, errorMessage: {}", recordId, errorMessage);
    }


    /**
     * 分页获取任务执行记录
     *
     * @param taskId   任务ID
     * @param pageNum  页码
     * @param pageSize 页大小
     * @return 分页结果
     */
    public PageResult<UserTaskRunRecord> getRunRecordsByPage(Long taskId, int pageNum, int pageSize) {
        Page<UserTaskRunRecord> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<UserTaskRunRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserTaskRunRecord::getTaskId, taskId)
                .eq(UserTaskRunRecord::getIsDel, 0)
                .orderByDesc(UserTaskRunRecord::getCreateTime);

        IPage<UserTaskRunRecord> result = page(page, queryWrapper);
        return new PageResult<>(result.getTotal(), result.getRecords());
    }
}
