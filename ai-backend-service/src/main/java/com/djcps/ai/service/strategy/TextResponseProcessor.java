package com.djcps.ai.service.strategy;

import com.djcps.ai.common.enums.ResponseMediaTypeEnum;
import com.djcps.ai.core.param.dify.SendMsgParam;
import com.djcps.ai.core.vo.SendMsgVo;
import com.djcps.ai.service.report.response.SubmitQueryResponse;
import org.springframework.stereotype.Service;

@Service
public class TextResponseProcessor implements ResponseProcessStrategy {
    /**
     * 是否由该处理器进行处理
     *
     * @param param
     * @param msgVo 消息
     * @return
     */
    @Override
    public boolean match(SendMsgParam param, SendMsgVo msgVo) {
        return !msgVo.getAnswer().contains("sql");
    }

    /**
     * 处理响应
     *
     * @param response 响应
     * @param msgVo    消息
     */
    @Override
    public void process(SubmitQueryResponse response, SendMsgVo msgVo) {
        response.setResultData(msgVo.getAnswer());
        response.setResultType(ResponseMediaTypeEnum.TEXT.getType());
    }

}
