package com.djcps.ai.service.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.djcps.ai.common.enums.ResponseMediaTypeEnum;
import com.djcps.ai.common.exception.SystemBizExcetion;
import com.djcps.ai.common.result.WebResultExt;
import com.djcps.ai.core.client.DataServerClient;
import com.djcps.ai.core.param.dify.SendMsgParam;
import com.djcps.ai.core.vo.ChartConfigResult;
import com.djcps.ai.core.vo.SendMsgVo;
import com.djcps.ai.core.vo.dataServer.ExecSQL;
import com.djcps.ai.common.vo.PageResult;
import com.djcps.ai.service.report.response.SubmitQueryResponse;
import com.djcps.ai.service.system.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;

import static com.djcps.ai.core.constants.CommonConstants.*;
import static com.djcps.ai.core.vo.yDivider.SmartEChartsAxisGrouper.processYAxis;

@Component
@Slf4j
public class SQLResponseProcessor implements ResponseProcessStrategy {
    @Autowired
    private DataServerClient dataServerClient;
    @Autowired
    private TextResponseProcessor textResponseProcessor;
    @Autowired
    private SystemConfigService configService;


    @Override
    public boolean match(SendMsgParam param, SendMsgVo msgVo) {
        return msgVo.getAnswer().contains("sql");
    }

    @Override
    public void process(SubmitQueryResponse response, SendMsgVo msgVo) {
        String answer = msgVo.getAnswer();
        //解析出来sql
        answer = StrUtil.removeAllLineBreaks(answer);
        ChartConfigResult configResult = JSONUtil.toBean(answer, ChartConfigResult.class);
        String sqlCommand = configResult.getSql();
        if (StrUtil.isBlank(sqlCommand)) {
            fallback(response, msgVo);
            return;
        }
        ResponseMediaTypeEnum typeEnum = ResponseMediaTypeEnum.getByType(configResult.getResultType());
        response.setResultType(typeEnum.getType());
        ExecSQL runSQLCommand = checkAndProcessSQL(sqlCommand);

        List resultData = executeSQL(runSQLCommand);
        if (CollUtil.isEmpty(resultData)) {
            fallbackNoData(response, msgVo);
            return;
        }
        processResultData(resultData);
        response.setResultData(resultData);
        response.setChartConfig(configResult);
        if (ResponseMediaTypeEnum.CHART.equals(typeEnum)) {
            Map<String, Integer> dimensionToGroupIndex = processYAxis(resultData);
            configResult.putYAxis(dimensionToGroupIndex);
        } else {
            configResult.setYAxis(Collections.emptyList());
            configResult.setType(Collections.emptyList());
            configResult.setX("");
        }
    }


    /**
     * 如果数据内容是数字,要进行四舍五入保留两位小数,要去掉小数点后的0
     *
     * @param resultData
     */
    private void processResultData(List resultData) {
        if (CollUtil.isEmpty(resultData)) {
            return;
        }
        for (Object data : resultData) {
            if (data instanceof Map) {
                Map<String, Object> dataMap = (Map<String, Object>) data;
                for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                    Object value = entry.getValue();
                    if (value instanceof Number) {
                        entry.setValue(roundAndFormatNumber(value));
                    }
                }
            }
        }
    }

    /**
     * 四舍五入保留两位小数,要去掉小数点后的0
     *
     * @param value
     * @return
     */
    private Object roundAndFormatNumber(Object value) {
        BigDecimal bigDecimal = new BigDecimal(value.toString());
        bigDecimal = bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP);
        return bigDecimal.stripTrailingZeros().toPlainString();
    }
    

    /**
     * 处理失败的情况
     *
     * @param response
     * @param msgVo
     */
    public void fallback(SubmitQueryResponse response, SendMsgVo msgVo) {
        String valueByKeyDefault = configService.findValueByKeyDefault(SYSTEM_CONFIG_KEY_SQL_EMPTY_MESSAGE, SQL_EMPTY_MESSAGE_DEFAULT);
        delegateToText(response, msgVo, valueByKeyDefault);
    }

    /**
     * 处理没有数据的情况
     *
     * @param response
     * @param msgVo
     */
    public void fallbackNoData(SubmitQueryResponse response, SendMsgVo msgVo) {
        String valueByKeyDefault = configService.findValueByKeyDefault(SYSTEM_CONFIG_KEY_DATA_EMPTY_MESSAGE, DATA_EMPTY_MESSAGE_DEFAULT);
        delegateToText(response, msgVo, valueByKeyDefault);
    }

    public void delegateToText(SubmitQueryResponse response, SendMsgVo msgVo, String message) {
        msgVo.setAnswer(message);
        textResponseProcessor.process(response, msgVo);
    }
    /**
     * 执行SQL
     *
     * @param sql dataServer入参
     * @return
     */
    public List executeSQL(ExecSQL sql) {
        WebResultExt<PageResult<Object>> pageResultWebResultExt = dataServerClient.execSQL(sql);
        PageResult<Object> data = pageResultWebResultExt.getData();
        return data.getList();
    }

    /**
     * 检查和处理SQL
     * @param sql
     * @return
     */
    private static ExecSQL checkAndProcessSQL(String sql) {
        if (StrUtil.isBlank(sql)) {
            throw new SystemBizExcetion("sql is blank");
        }
        if (!StrUtil.startWithIgnoreCase(sql, "with")
                && !StrUtil.startWithIgnoreCase(sql, "select")
        ) {
            throw new SystemBizExcetion("只能做查询,不能做修改或删除");
        }
        return processLimit(sql);
    }

    /**
     * 处理SQL的limit情况
     * @param sql
     * @return
     */
    private static ExecSQL processLimit(String sql) {
        sql = StrUtil.removeSuffix(sql, ";");
        int limitValue = RUN_SQL_LIMIT;
        Matcher matcher = LIMIT_PATTERN.matcher(sql);
        if (matcher.find()) {
            limitValue = Integer.parseInt(matcher.group(1));
            sql = sql.replaceAll("\\s+LIMIT\\s+\\d+\\s*$", "");
        }

        return new ExecSQL(sql, limitValue);
    }
}
