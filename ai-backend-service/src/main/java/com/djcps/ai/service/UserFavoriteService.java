package com.djcps.ai.service;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.djcps.ai.common.threadlocal.ThreadLocalUser;
import com.djcps.ai.core.vo.SendMsgVo;
import com.djcps.ai.core.vo.in.IdAndSingleFieldDto;
import com.djcps.ai.core.vo.in.favorite.AddFavoriteDto;
import com.djcps.ai.dao.entity.UserFavorite;
import com.djcps.ai.dao.mapper.UserFavoriteMapper;
import com.djcps.ai.service.report.response.SubmitQueryResponse;
import com.djcps.ai.service.strategy.SQLResponseProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class UserFavoriteService extends ServiceImpl<UserFavoriteMapper, UserFavorite> {

    @Autowired
    private SQLResponseProcessor sqlResponseProcessor;

    public Long addFavorite(AddFavoriteDto dto) {
        UserFavorite userFavorite = new UserFavorite();
        userFavorite.setUserId(ThreadLocalUser.getUserId());
        userFavorite.setTitleName(dto.getName());
        userFavorite.setConversationId(dto.getConversationId());
        userFavorite.setMessageId(dto.getMessageId());
        userFavorite.setChartConfig(JSONUtil.toJsonStr(dto.getChartConfig()));
        save(userFavorite);
        return userFavorite.getId();
    }

    public Optional<UserFavorite> getFavorite(String userId, String conversationId, String messageId) {
        return lambdaQuery()
                .eq(UserFavorite::getUserId, userId)
                .eq(UserFavorite::getConversationId, conversationId)
                .eq(UserFavorite::getMessageId, messageId)
                .oneOpt();
    }

    public List<UserFavorite> getAllFavorite(String userId) {
        return lambdaQuery().eq(UserFavorite::getUserId, userId).orderByDesc(UserFavorite::getCreateTime).list();
    }

    public SubmitQueryResponse getFavoriteById(Long id) {
        UserFavorite byId = getById(id);
        SubmitQueryResponse response = new SubmitQueryResponse();
        if (byId == null) {
            return response;
        }
        String chartConfig = byId.getChartConfig();
        SendMsgVo msgVo = new SendMsgVo();
        msgVo.setAnswer(chartConfig);
        sqlResponseProcessor.process(response, msgVo);
        response.setQuery(byId.getTitleName());
        response.setMessageId(byId.getMessageId());
        response.setConversationId(byId.getConversationId());
        return response;
    }

    public boolean renameById(IdAndSingleFieldDto dto) {
        UserFavorite byId = getById(dto.getId());
        if (byId == null) {
            return false;
        }
        lambdaUpdate().eq(UserFavorite::getId, dto.getId()).set(UserFavorite::getTitleName, dto.getValue()).update();
        return true;
    }
}
