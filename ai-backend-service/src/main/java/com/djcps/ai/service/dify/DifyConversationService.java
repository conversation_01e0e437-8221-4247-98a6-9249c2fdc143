package com.djcps.ai.service.dify;

import com.djcps.ai.core.param.dify.SendMsgParam;
import com.djcps.ai.core.vo.SendMsgVo;
import com.djcps.ai.core.vo.in.*;
import com.djcps.ai.core.vo.out.DifyCoversationPageOutDto;
import com.djcps.ai.core.vo.out.DifyEditOutDto;
import com.djcps.ai.core.vo.out.DifyMessagePageOutDto;
import com.djcps.ai.service.report.response.SubmitQueryResponse;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * 类DifyConversationService的实现描述：TODO 类实现描述
 *
 * <AUTHOR> 2024/7/15 下午3:52
 */
public interface DifyConversationService {

    /**
     * 发送消息
     *
     * @param param
     * @return
     */
    SendMsgVo sendMessage(SendMsgParam param);

    SubmitQueryResponse sendMessageAndExecute(SendMsgParam param);
    SseEmitter sendMessageAndExecuteStream(SendMsgParam param);

    /**
     * 重命名会话
     *
     * @param inVo
     * @return
     */
    DifyEditOutDto renameConversations(String conversation_id, DifyConversationInDto inVo);

    /**
     * 分页获取用户会话列表，默认一页20条
     *
     * <AUTHOR>
     * @return com.djcps.ai.core.vo.out.DifyCoversationPageDto
     * @date 2024/7/15
     */

    DifyCoversationPageOutDto getAllConversations(DifyConversationsInDto dto);

    /**
     * 消息反馈
     *
     * @param inVo
     * <AUTHOR>
     * @return com.djcps.ai.core.vo.out.DifyEditOutDto
     * @date 2024/7/15
     */
    DifyEditOutDto messageFeedbacks(String messageId, DifyFeedbackInDto inVo);

    /**
     * 获取会话历史消息,倒序
     * 
     * @param
     * <AUTHOR>
     * @return com.djcps.ai.core.vo.out.DifyEditOutDto
     * @date 2024/7/15
     */
    DifyMessagePageOutDto getAllMessage(DifyMessageInDto inVo);


    /**
     * 删除会话
     * @param
     * <AUTHOR>
     * @return com.djcps.ai.core.vo.out.DifyEditOutDto
     * @date 2024/7/15
     */
    DifyEditOutDto deleteConversation(String conversation_id, DifyUserInDto inVo);
}
