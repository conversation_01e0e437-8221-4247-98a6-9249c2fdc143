package com.djcps.ai.service.sql;

import cn.hutool.json.JSONUtil;
import org.springframework.jdbc.core.RowCallbackHandler;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CustomRowCallbackHandler implements RowCallbackHandler {
    private List<String> columns = new ArrayList<>();
    private List<List<Object>> data = new ArrayList<>();

    @Override
    public void processRow(ResultSet rs) throws SQLException {
        if (columns.isEmpty()) {
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();
            for (int i = 1; i <= columnCount; i++) {
                columns.add(metaData.getColumnName(i));
            }
        }

        List<Object> rowData = new ArrayList<>();
        for (String column : columns) {
            rowData.add(rs.getObject(column));
        }
        data.add(rowData);
    }

    public String getJsonResult() {
        Map<String, Object> result = new HashMap<>();
        result.put("columns", columns);
        result.put("data", data);

        return JSONUtil.toJsonStr(result);
    }
}