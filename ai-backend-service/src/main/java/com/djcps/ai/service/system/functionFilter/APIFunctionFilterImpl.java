package com.djcps.ai.service.system.functionFilter;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.djcps.ai.common.threadlocal.ThreadLocalUser;
import com.djcps.ai.service.report.param.SourceParam;
import com.djcps.ai.service.system.FunctionInfoFilter;
import com.djcps.ai.service.system.StrSystemConfigParse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service("filter_api")
public class APIFunctionFilterImpl implements FunctionFilter{

    private final StrSystemConfigParse str;

    public APIFunctionFilterImpl(StrSystemConfigParse str) {
        this.str = str;
    }

    @Data
    @AllArgsConstructor
    class UserRole{
        private String userId;
        private String role;
    }
    @Override
    public boolean apply(FunctionInfoFilter filter, SourceParam target) {
        String url = filter.getFilter();
        if (StrUtil.isBlankIfStr(url)) {
            return true;
        }
        if (target == null) {
            return false;
        }
        String role = target.getRole();
        UserRole userRole = new UserRole(ThreadLocalUser.getUserId(),role);

        String jsonStr = JSONUtil.toJsonStr(userRole);
        String cookie= "token=" + ThreadLocalUser.getToken();
        log.debug("请求url: {}, 请求参数: {},Cookie :{}", url, jsonStr,cookie);
        String body = HttpRequest.post(url)
                .header("Cookie", cookie)
                .body(jsonStr)
                .execute().body();
        log.debug("请求结果: {}", body);
        if (StrUtil.isBlankIfStr(body)) {
            return false;
        }
        JSONObject jsonObject = JSONUtil.parseObj(body);
        Long code = jsonObject.getLong("code");
        Boolean success = jsonObject.getBool("success");

        if (code == null || code != 200 || success == null || !success) {
            return false;
        }
        Object data = JSONUtil.getByPath(jsonObject, "data");

       return  data!=null && CollUtil.isNotEmpty((List<?>) data);
    }
}
