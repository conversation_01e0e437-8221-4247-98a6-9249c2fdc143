package com.djcps.ai.service.report.response;

import com.djcps.ai.core.vo.ChartConfigResult;
import lombok.Data;

@Data
public class SubmitQuestionResponse {
    /**
     * 结果集
     */
    private String resultData;
    /**
     * 当前问题id
     */
    private String questionId;
    private String sqlCommand;
    /**
     * 用户问题
     */
    private String question;
    /**
     * 当前会话id;
     */
    private String sessionId;
    private ChartConfigResult chartConfig;

}
