package com.djcps.ai.service;

import cn.hutool.core.util.StrUtil;
import com.djcps.ai.common.result.WebResultExt;
import com.djcps.ai.core.client.DjAuthClient;
import com.djcps.ai.core.constants.CommonConstants;
import com.djcps.ai.core.param.AuthParam;
import com.djcps.ai.core.param.AuthResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import template.utils.bean.CookiesToolUtil;
import template.utils.json.JsonToolUtil;

import jakarta.servlet.http.HttpServletRequest;

import static com.djcps.ai.core.constants.CommonConstants.HEADER_APP_NAME;

@Service
@Slf4j
public class AuthService {

    @Autowired
    private DjAuthClient authClient;

    /**
     * 先从header中获取fuserid,
     * 如果没有则调用authClient.getUserCache获取faccountId
     *
     * @param request
     * @return
     */
    public String getUserIdFromRequest(HttpServletRequest request) {
        String userId = request.getHeader(CommonConstants.HEADER_USER_ID);
        if (StrUtil.isNotBlank(userId)) {
            return userId;
        }
        String appName = request.getHeader(HEADER_APP_NAME);
        String token = CookiesToolUtil.getToken(request);
        if (StrUtil.isBlank(appName) || StrUtil.isBlank(token)) {
            log.error("appName :{} or token :{} is null", appName, token);
            return null;
        }
        AuthResponse userInfoFromRequest = getUserInfoFromRequest(token, appName);
        if (userInfoFromRequest != null) {
            return userInfoFromRequest.getFaccountId();
        }
        return null;
    }

    public AuthResponse getUserInfoFromRequest(String token, String appName) {
        WebResultExt<AuthResponse> userCache = authClient.getUserCache(new AuthParam(token, appName));
        log.debug("userCache:{}", JsonToolUtil.beanToJson(userCache));
        if (userCache != null) {
            if (userCache.isSuccess()) {
                return userCache.getData();
            }
        }
        return null;
    }

}
