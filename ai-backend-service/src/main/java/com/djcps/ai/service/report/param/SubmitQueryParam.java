package com.djcps.ai.service.report.param;

import com.djcps.ai.core.param.dify.SendMsgParam;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.Map;

@Data
public class SubmitQueryParam {
    @NotNull
    @NotEmpty
    private String query;
    /**
     * 会话id, 由后端生成,可以不传表示开启的新会话,旧会话必传
     */
    private String conversationId;
    private Map<String, Object> inputs;

    public SendMsgParam toMsg(String userId) {
        SendMsgParam param = new SendMsgParam();
        param.setConversationId(conversationId);
        param.setQuery(query);
        param.setUser(userId);
        param.setInputs(inputs);
        return param;
    }
}
