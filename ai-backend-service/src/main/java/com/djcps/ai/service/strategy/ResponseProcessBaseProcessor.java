package com.djcps.ai.service.strategy;

import com.djcps.ai.core.param.dify.SendMsgParam;
import com.djcps.ai.core.vo.SendMsgVo;
import com.djcps.ai.service.report.response.SubmitQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@Slf4j
public class ResponseProcessBaseProcessor {
    @Autowired
    private Map<String, ResponseProcessStrategy> responseProcessorMap;

    public SubmitQueryResponse process(SendMsgParam param, SendMsgVo msgVo) {
        SubmitQueryResponse response = new SubmitQueryResponse();
        response.setConversationId(msgVo.getConversationId());
        response.setQuery(param.getQuery());
        response.setMessageId(msgVo.getMessageId());
        msgVo.setQuery(param.getQuery());
        msgVo.setUser(param.getUser());
        log.info("responseProcessorMap:{}", responseProcessorMap);
        for (ResponseProcessStrategy processor : responseProcessorMap.values()) {
            log.info("current response processor:{}", processor.getClass().getName());
            if (processor.match(param, msgVo)) {
                log.info("response processor matched:{}", processor.getClass().getName());
                processor.process(response, msgVo);
                break;
            }
        }
        return response;
    }

}
