package com.djcps.ai.service.report.response;

import com.djcps.ai.common.enums.ResponseMediaTypeEnum;
import com.djcps.ai.core.vo.ChartConfigResult;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class SubmitQueryResponse {
    /**
     * 结果集
     */
    private Object resultData;
    /**
     * 展示类型,取值为小写的text,image,table,chart,other
     * TEXT("text","文本"),
     * IMAGE("image","图片"),
     * TABLE("table","表格"),
     * CHART("chart","图表"),
     * OTHER("other","其他"),
     * ;
     */
    private String resultType = ResponseMediaTypeEnum.TEXT.getType();
    /**
     * 当前问题id
     */
    private String messageId;
    /**
     * 用户问题
     */
    private String query;
    /**
     * 当前会话id;
     */
    private String conversationId;
    private ChartConfigResult chartConfig;

}
