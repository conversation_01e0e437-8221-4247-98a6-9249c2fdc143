package com.djcps.ai.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.djcps.ai.core.client.EasyOrderFeign;
import com.djcps.ai.core.vo.in.easyorder.TaskResultItemQueryBo;
import com.djcps.ai.core.vo.out.easyorder.TaskResultItemQueryVo;
import com.djcps.ai.dao.entity.TaskResultItem;
import com.djcps.ai.dao.mapper.TaskResultItemMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class TaskResultItemService extends ServiceImpl<TaskResultItemMapper, TaskResultItem> {

    private final EasyOrderFeign easyOrderFeign;


    /**
     * 根据任务ID和批次号查询结果项
     *
     * @param taskId  任务ID
     * @param batchNo 批次号
     * @return 结果项列表
     */
    public List<TaskResultItem> getByTaskIdAndBatchNo(Long taskId, String batchNo) {
        try {
            TaskResultItemQueryBo queryBo = new TaskResultItemQueryBo();
            queryBo.setTaskId(taskId);
            queryBo.setBatchNo(batchNo);

            List<TaskResultItemQueryVo> voList = easyOrderFeign.getTaskResultItems(queryBo).fetchPageDataList();
            if (voList == null || voList.isEmpty()) {
                log.info("远程查询任务结果项返回空列表，taskId: {}, batchNo: {}", taskId, batchNo);
                return List.of();
            }

            // 转换VO到Entity
            List<TaskResultItem> entityList = voList.stream()
                    .map(vo -> {
                        TaskResultItem entity = new TaskResultItem();
                        BeanUtil.copyProperties(vo, entity);
                        return entity;
                    })
                    .collect(Collectors.toList());

            log.info("远程查询任务结果项成功，返回 {} 条记录", entityList.size());
            return entityList;
        } catch (Exception e) {
            log.error("调用远程查询任务结果项接口失败，taskId: {}, batchNo: {}", taskId, batchNo, e);
            throw new RuntimeException("调用远程查询任务结果项接口失败: " + e.getMessage(), e);
        }
    }
}
