package com.djcps.ai.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.djcps.ai.dao.entity.TaskResultItem;
import com.djcps.ai.dao.mapper.TaskResultItemMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class TaskResultItemService extends ServiceImpl<TaskResultItemMapper, TaskResultItem> {

    /**
     * 根据任务ID查询所有相关的结果项
     *
     * @param taskId 任务ID
     * @return 结果项列表
     */
    public List<TaskResultItem> getByTaskId(Long taskId) {
        LambdaQueryWrapper<TaskResultItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskResultItem::getTaskId, taskId);
        return list(queryWrapper);
    }

    /**
     * 根据任务ID和批次号查询结果项
     *
     * @param taskId  任务ID
     * @param batchNo 批次号
     * @return 结果项列表
     */
    public List<TaskResultItem> getByTaskIdAndBatchNo(Long taskId, String batchNo) {
        LambdaQueryWrapper<TaskResultItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskResultItem::getTaskId, taskId)
                .eq(TaskResultItem::getBatchNo, batchNo);
        return list(queryWrapper);
    }
}
