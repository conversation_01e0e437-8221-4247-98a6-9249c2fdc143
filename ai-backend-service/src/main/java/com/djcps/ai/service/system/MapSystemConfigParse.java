package com.djcps.ai.service.system;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.Map;

@Component("map")
public class MapSystemConfigParse implements SystemConfigTypeParse {
    @Override
    public Object parse(String content) {
        Map<String, String> linkedHashMap = new LinkedHashMap<>();
        // 处理字符串，提取键值对添加到 LinkedHashMap 中
        String[] pairs = content.replace("{", "").replace("}", "").split(",");
        for (String pair : pairs) {
            String[] keyValue = pair.split("=");
            linkedHashMap.put(StrUtil.trim(keyValue[0]), StrUtil.trim(keyValue[1]));
        }
        // Convert the string to a map
        return linkedHashMap;
    }

    public static void main(String args[]) {
        Map<String, Object> map = new LinkedHashMap<>();
        map.put("ai_report", "AI报表");
        map.put("faq", "问答助手");
        map.put("feedback", "反馈");
        map.put("faq_yzx", "问答助手");
        map.put("feedback_yzx", "反馈");
        String string = map.toString();
        System.out.println(string);
        // Replace '=' with ':' to make it a valid JSON string
        String jsonString = string.replace("=", ":");
        // Convert the string to a map
        Map bean = JSONUtil.parseObj(jsonString).toBean(LinkedHashMap.class);
        System.out.println(bean);
    }
}
