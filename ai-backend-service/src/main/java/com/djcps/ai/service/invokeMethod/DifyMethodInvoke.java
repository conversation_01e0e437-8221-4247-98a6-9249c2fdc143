package com.djcps.ai.service.invokeMethod;

import cn.hutool.core.util.StrUtil;
import com.djcps.ai.core.client.DifyWorkflowFeignClient;
import com.djcps.ai.core.constants.CommonConstants;
import com.djcps.ai.core.param.dify.DifyWordflowRequestParam;
import com.djcps.ai.core.vo.DifyWorkflowResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service("dify")
@RequiredArgsConstructor
@Slf4j
public class DifyMethodInvoke implements InvokeService{
    private final DifyWorkflowFeignClient difyWorkflowFeignClient;
    private final static String USER_ID_TEMP = "{}_{}_{}_{}";

    @Override
    public Object invoke(InvokeParam invokeParam) {
        DifyWordflowRequestParam param = new DifyWordflowRequestParam();
        param.setUser(StrUtil.format(USER_ID_TEMP,invokeParam.getBatchNo(),invokeParam.getSkey(),invokeParam.getTaskId()));
        param.setInputs(invokeParam.getMergeParams());
        DifyWorkflowResponse run = difyWorkflowFeignClient.run(param, CommonConstants.buildHeaderAuthorizationBear(invokeParam.getConfig()));
        log.debug("Dify workflow run response {}", run);
        if (run.getData() != null && StrUtil.containsIgnoreCase( run.getData().getStatus(),"succeeded")) {
            return run.getData().getOutputs().getResult();
        }else {
            log.error("Dify workflow run error {}", run);
            return null;
        }
    }

}
