<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
	  <artifactId>ai-backend</artifactId>
    <groupId>com.dongjing</groupId>
    <version>1.0-SNAPSHOT</version>
  </parent>

	<artifactId>ai-backend-service</artifactId>
	<name>ai-backend-service</name>
  <packaging>jar</packaging>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.source>17</maven.compiler.source>
    <maven.compiler.target>17</maven.compiler.target>
  </properties>
  
  <dependencies>
		<dependency>
			<groupId>com.dongjing</groupId>
			<artifactId>ai-backend-dao</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.dongjing</groupId>
			<artifactId>ai-backend-config</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.dongjing</groupId>
			<artifactId>ai-backend-utils</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.dongjing</groupId>
			<artifactId>ai-backend-feign</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.dongjing</groupId>
			<artifactId>ai-backend-core</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>
	  <dependency>
		  <groupId>com.djcps.ai</groupId>
		  <artifactId>ai-backend-tools</artifactId>
		  <version>0.0.1-SNAPSHOT</version>
		  <scope>compile</scope>
	  </dependency>

  </dependencies>

</project>
