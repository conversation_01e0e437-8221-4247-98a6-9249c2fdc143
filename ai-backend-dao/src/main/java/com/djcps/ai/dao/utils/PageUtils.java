package com.djcps.ai.dao.utils;

import com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: PageUtils
 * @Description: 关乎分页查询一些操作
 * <AUTHOR>
 * @date 2016年3月6日 下午3:12:33
 */
public class PageUtils {

    // 进行parse
    public static List<QueryCondition<?>> parse(Map<String, String> params) {

        List<QueryCondition<?>> list = Lists.newArrayList();
        for (String key : params.keySet()) {
            String[] keys = key.split("_");
            if (keys != null && keys.length == 3) {
                QueryCondition<?> condition = new QueryCondition<>(keys[1], params.get(key), keys[2]);
                list.add(condition);
            }
        }
        return list;
    }

}
