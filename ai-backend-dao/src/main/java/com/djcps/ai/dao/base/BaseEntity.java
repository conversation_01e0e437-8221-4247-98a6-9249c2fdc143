package com.djcps.ai.dao.base;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

import java.util.Date;

@Data
public class BaseEntity {
    @TableId(type = com.baomidou.mybatisplus.annotation.IdType.AUTO)
    private Long id;
    private Date createTime;
    @TableLogic(value = "0", delval = "UNIX_TIMESTAMP()")
    private Long isDel;
}
