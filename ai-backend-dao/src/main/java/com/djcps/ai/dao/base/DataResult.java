package com.djcps.ai.dao.base;

import java.util.List;

/**
*类DataResult.java 的实现描述：返回封装
*
*@author: yeb 2021/9/15 下午6:05
**/
public class DataResult<T> {

    private List<T> data;
    private int     count;

    public DataResult(){
    }

    public DataResult(List<T> data, int count){
        super();
        this.data = data;
        this.count = count;
    }

    public List<T> getData() {
        return data;
    }

    public void setData(List<T> data) {
        this.data = data;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

}
