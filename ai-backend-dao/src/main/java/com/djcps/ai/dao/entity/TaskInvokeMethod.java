package com.djcps.ai.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.djcps.ai.dao.base.BaseEntity;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 方法注册实体类
 * 对应表：task_invoke_method
 */
@Data
@TableName("task_invoke_method")
public class TaskInvokeMethod extends BaseEntity {

    /**
     * 方法名称
     */
    @NotBlank(message = "方法名称不能为空")
    @Size(max = 60, message = "方法名称长度不能超过60个字符")
    private String name;

    /**
     * 获取类型:http,dify,tools,python,groovy
     */
    @NotBlank(message = "调用类型不能为空")
    private String invokeType;

    /**
     * 调用参数,json格式
     */
    @Size(max = 4000, message = "调用参数长度不能超过 4000 个字符")
    private String invokeParam;
    @Size(max = 3000, message = "调用参数长度不能超过 3000 个字符")
    private String inputScheme;
    @Size(max = 4000, message = "调用参数长度不能超过 4000 个字符")
    private String outputScheme;

    /**
     * 使用类型: parameter 参数, fetchData 获取数据, system 系统
     */
    @NotBlank(message = "使用类型不能为空")
    private String useType = "parameter";
}
