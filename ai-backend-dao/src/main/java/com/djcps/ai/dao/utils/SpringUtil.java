package com.djcps.ai.dao.utils;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

public class SpringUtil implements ApplicationContextAware {

    private ApplicationContext context;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        context = applicationContext;
    }

    public <T> T getBeansByType(Class<T> clazz) {
        return context.getBean(clazz);
    }

    public Object getBeansByName(String name) {
        return context.getBean(name);
    }

}
