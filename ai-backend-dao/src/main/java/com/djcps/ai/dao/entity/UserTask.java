package com.djcps.ai.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.djcps.ai.dao.base.BaseEntity;
import lombok.Data;

@Data
@TableName("user_task")
public class UserTask extends BaseEntity {
    private String userId;
    private String title;
    private String remark;
    private String templateId;
    private String cornExp;
    private String cycleType;
    private String bizType;
    private Long methodId;
    private Integer jobId;
    private Integer coverRepeat;
    /**
     * 是否保留同一周期的数据,0不保留,1保留
     */
    private Integer reten;
}
