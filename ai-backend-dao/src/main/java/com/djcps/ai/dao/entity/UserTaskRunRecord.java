package com.djcps.ai.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.djcps.ai.dao.base.BaseEntity;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 任务执行记录实体类
 */
@Data
@TableName("user_task_run_record")
public class UserTaskRunRecord extends BaseEntity {
    
    /**
     * 任务ID
     */
    private Long taskId;
    
    /**
     * 批次号
     */
    private String batchNo;
    
    /**
     * 执行类型：NORMAL-正常执行，DEBUG-调试执行，REANALYZE-重新分析
     */
    private ExecType execType;
    
    /**
     * 执行状态：RUNNING-运行中，SUCCESS-成功，FAILED-失败
     */
    private ExecStatus execStatus;
    
    /**
     * 执行开始时间
     */
    private LocalDateTime execStartTime;
    
    /**
     * 执行结束时间
     */
    private LocalDateTime execEndTime;
    
    /**
     * 处理的结果项数量
     */
    private Integer resultCount;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 执行类型枚举
     */
    public enum ExecType {
        NORMAL,     // 正常执行
        DEBUG,      // 调试执行
        REANALYZE   // 重新分析
    }
    
    /**
     * 执行状态枚举
     */
    public enum ExecStatus {
        RUNNING,    // 运行中
        SUCCESS,    // 成功
        FAILED      // 失败
    }
}
