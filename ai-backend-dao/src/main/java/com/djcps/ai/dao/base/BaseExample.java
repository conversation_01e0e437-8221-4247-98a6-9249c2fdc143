package com.djcps.ai.dao.base;

import com.djcps.ai.common.vo.Page;

/**
 * 类BaseExample.java的实现描述：查询
 * 
 * <AUTHOR> 2018年3月20日 下午2:45:29
 */
public class BaseExample {

	public static final String _YES = "y";
	public static final String _NO = "n";

	private String orderByClause;
	private boolean distinct;
	protected Page<?> page;

	/**
	 * @return the orderByClause
	 */
	public String getOrderByClause() {
		return orderByClause;
	}

	/**
	 * @param orderByClause the orderByClause to set
	 */
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	/**
	 * @return the distinct
	 */
	public boolean isDistinct() {
		return distinct;
	}

	/**
	 * @param distinct the distinct to set
	 */
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	/**
	 * @return the page
	 */
	public Page getPage() {
		return page;
	}

	/**
	 * @param page the page to set
	 */
	public void setPage(Page page) {
		this.page = page;
	}

}
