package com.djcps.ai.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.djcps.ai.dao.base.BaseEntity;
import lombok.Data;

import java.util.Date;

@Data
@TableName("task_result_item")
public class TaskResultItem extends BaseEntity {
    private String skey;
    private Long taskId;
    private Long resultId;
    private Long templateId;
    private String subBizType;
    private String batchNo;
    private String prompt;
    private String originData;
    private String itemResult;
    private Date updateTime;
}
