package com.djcps.ai.dao.base;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 类BaseMapper.java 的实现描述：基础mapper接口
 *
 * @author: yeb 2021/9/15 下午6:05
 **/
public interface BaseMapper<T, E> {

	int insertSelective(T record);

	int deleteByPrimaryKey(T record);

	int countByExample(E example);

	List<T> selectByExample(E example);

	int updateByExampleSelective(@Param("record") T record, @Param("example") E example);

	int updateByExampleSelective(Map<String, Object> map);

	int updateByExample(@Param("record") T record, @Param("example") E example);

	int updateByPrimaryKeySelective(T record);

	T selectByPrimaryKey(Long id);
}
