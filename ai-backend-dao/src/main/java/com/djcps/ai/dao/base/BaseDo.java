package com.djcps.ai.dao.base;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 类BaseDo.java 的实现描述：do基类
 *
 * @author: yeb 2021/9/15 下午6:03
 **/
@Data
public abstract class BaseDo implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long id;

	@JsonIgnore
	private Date createTime;

	@JsonIgnore
	private String creator;

	@JsonIgnore
	private Date updateTime;

	@JsonIgnore
	private String modifier;

	@JsonIgnore
	private String isDeleted;
	/**
	 * 乐观锁时间
	 */
	@JsonIgnore
	private Date lockTime;

	public void setIsDeleted(String isDeleted) {
		this.isDeleted = isDeleted == null ? null : isDeleted.trim();
	}

	@JsonIgnore
	@Override
	public String toString() {
		StringBuilder sb = new StringBuilder("[").append(getClass().getName()).append("]").append("PKID:")
				.append(getId() == null ? "Null" : getId());
		return sb.toString();
	}

	@JsonIgnore
	public abstract String getBoQualifiedIntfName();

	@JsonIgnore
	protected String getDoClassName() {
		return this.getClass().getCanonicalName();
	}
}
