package com.djcps.ai.dao.utils;

/**
 * @ClassName: QueryCondition
 * @Description: 查询条件类
 * <AUTHOR>
 * @date 2016年3月2日 下午3:14:37
 */

public class QueryCondition<T> {

    /**
     * @Fields conditionName : 条件名
     */
    private String        conditionName;

    /**
     * @Fields conditionValue : 条件值
     */
    private T             conditionValue;

    /**
     * @Fields operator : 运算符
     */
    private OperatorEnum  operator;

    /**
     * @Fields connector : 连接符
     */
    private ConnectorEnum connector;

    public QueryCondition(String conditionName, T conditionValue, OperatorEnum operator, ConnectorEnum connector){
        super();
        this.conditionName = conditionName;
        this.conditionValue = conditionValue;
        this.operator = operator;
    }

    public QueryCondition(String conditionName, T conditionValue, String operator){
        this(conditionName, conditionValue, OperatorEnum.getValue(operator), ConnectorEnum.and);
    }

    public QueryCondition(String conditionName, T conditionValue){
        super();
        this.conditionName = conditionName;
        this.conditionValue = conditionValue;
        this.operator = OperatorEnum.eq;
        this.connector = ConnectorEnum.and;
    }

    public QueryCondition(){
        super();
        this.operator = OperatorEnum.eq;
        this.connector = ConnectorEnum.and;
    }

    public String getConditionName() {
        return conditionName;
    }

    public void setConditionName(String conditionName) {
        this.conditionName = conditionName;
    }

    public OperatorEnum getOperator() {
        return operator;
    }

    public void setOperator(OperatorEnum operator) {
        this.operator = operator;
    }

    public T getConditionValue() {
        return conditionValue;
    }

    public void setConditionValue(T conditionValue) {
        this.conditionValue = conditionValue;
    }

    public enum OperatorEnum {
        eq("=", "eq"), unEq("!=", "uneq"), gt(">", "gt"), gtEq(">=", "gteq"), lt("<", "lt"), ltEq("<=", "lteq"),
        in("in", "in");

        private String code;
        private String name;

        private OperatorEnum(String code, String name){
            this.code = code;
            this.name = name;
        }

        public static OperatorEnum getValue(String operator) {
            if (operator != null) {
                for (OperatorEnum operatorEnum : OperatorEnum.values()) {
                    if (operatorEnum.getCode().equals(operator.trim().toLowerCase())) {
                        return operatorEnum;
                    }
                }
            }
            throw new RuntimeException("未找到此条件符号:" + operator);
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

    }

    public enum ConnectorEnum {
        and, or;
    }

    public ConnectorEnum getConnector() {
        return connector;
    }

    public void setConnector(ConnectorEnum connector) {
        this.connector = connector;
    }
}
