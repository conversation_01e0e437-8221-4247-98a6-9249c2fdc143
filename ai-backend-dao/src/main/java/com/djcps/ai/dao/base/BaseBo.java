package com.djcps.ai.dao.base;

import java.util.List;
import java.util.Map;

/**
*类BaseBo.java 的实现描述：基类接口
*
*@author: yeb 2021/9/15 下午6:02
**/
public interface BaseBo<D extends BaseDo, E extends BaseExample> {

    /*
     * 新增
     *
     * <AUTHOR>
     * @return 
     * @date 2021/9/18
     */
    int insert(D dataObject);

    int delete(Long id);

    int deleteByExample(E example);

    int update(D dataObject);

    int updateByExample(D record, E example);

    int updateByExample(Map<String, Object> map);

    D selectByPrimaryKey(Long id);

    List<D> selectByExample(E example);

    int countByExample(E example);

    DataResult<D> getPageByExample(E example);

    boolean isValidDo(D dataObject);
}
