package com.djcps.ai.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.djcps.ai.dao.base.BaseEntity;
import lombok.Data;

@Data
@TableName("user_report_police")
public class UserReportPolice extends BaseEntity {
    private String userId;
    private String phone;
    private String source;
    /**
     * 举报状态,0:待处理,100:已处理
     */
    private Integer status;
    /**
     * 举报类型
     */
    private String itemName;
    /**
     * 用户填写内容
     */
    private String remark;
    /**
     * aiFunction name,在header中的aiFunction
     */
    private String functionName;
    /**
     * 会话id
     */
    private String conversationId;
    /**
     * 消息id
     */
    private String messageId;
}
