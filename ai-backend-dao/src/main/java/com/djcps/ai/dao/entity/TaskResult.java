package com.djcps.ai.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.djcps.ai.dao.base.BaseEntity;
import lombok.Data;

import java.util.Date;

@Data
@TableName("task_result")
public class TaskResult extends BaseEntity {
    private String skey;
    private String supplierId;
    private Long taskId;
    private String cycleType;
    private String orgId;
    private String role;
    private String userId;
    private String bizType;
    private String batchNo;
    private String dataDate;
    private Date execStartTime;
    private Date execEndTime;
}
