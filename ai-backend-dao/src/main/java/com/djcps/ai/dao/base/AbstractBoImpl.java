package com.djcps.ai.dao.base;

import com.djcps.ai.common.threadlocal.ThreadLocalUser;
import com.djcps.ai.dao.exception.BizException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 类AbstractBoImpl.java 的实现描述：bo基类
 *
 * @author: yeb 2021/9/15 下午6:01
 **/
public abstract class AbstractBoImpl<D extends BaseDo, K extends BaseMapper<D, E>, E extends BaseExample> {

	protected K mapper;

	protected Logger logger = LoggerFactory.getLogger(this.getClass());

	@Autowired
	protected BizException bizException;

	public AbstractBoImpl() {
		logger = LoggerFactory.getLogger(this.getClass());
	}

	public void setMapper(K mapper) {
		this.mapper = mapper;
	}

	public int insert(D dataObject) {
		if (isValidDo(dataObject)) {
			dataObject.setCreateTime(new Date(System.currentTimeMillis()));
			dataObject.setUpdateTime(new Date(System.currentTimeMillis()));
			if (ThreadLocalUser.getUserId() != null) {
				dataObject.setCreator(ThreadLocalUser.getUserId());
				dataObject.setModifier(ThreadLocalUser.getUserId());
			}
			int v = mapper.insertSelective(dataObject);
			return v;
		} else {
			throw new InvalidDoException("Invalid do:" + dataObject.toString());
		}
	}

	public int delete(Long id) {
		D record = mapper.selectByPrimaryKey(id);
		record.setUpdateTime(new Date(System.currentTimeMillis()));
		if (ThreadLocalUser.getUserId() != null) {
			record.setModifier(ThreadLocalUser.getUserId());
		}
		return mapper.deleteByPrimaryKey(record);
	}

	public int deleteByExample(E example) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("example", example);
		Map<String, Object> record = new HashMap<String, Object>();
		record.put("updateTime", new Date(System.currentTimeMillis()));
		if (ThreadLocalUser.getUserId() != null) {
			record.put("modifier", ThreadLocalUser.getUserId());
		}
		record.put("isDeleted", "y");
		map.put("record", record);
		return mapper.updateByExampleSelective(map);
	}

	public List<D> selectByExample(E example) {
		return mapper.selectByExample(example);
	}

	public int countByExample(E example) {
		return mapper.countByExample(example);
	}

	public DataResult<D> getPageByExample(E example) {
		DataResult<D> dr = new DataResult<D>();
		dr.setData(selectByExample(example));
		example.setPage(null);
		dr.setCount(countByExample(example));
		return dr;
	}

	public D selectByPrimaryKey(Long id) {
		return mapper.selectByPrimaryKey(id);
	}

	public int update(D dataObject) {
		if (isValidDo(dataObject)) {
			dataObject.setUpdateTime(new Date(System.currentTimeMillis()));
			if (ThreadLocalUser.getUserId() != null) {
				dataObject.setModifier(ThreadLocalUser.getUserId());
			}
			int v = mapper.updateByPrimaryKeySelective(dataObject);
			return v;
		} else {
			throw new InvalidDoException("Invalid do:" + dataObject.toString());
		}
	}

	public int updateByExample(D record, E example) {
		return mapper.updateByExampleSelective(record, example);
	}

	public int updateByExample(Map<String, Object> map) {
		return mapper.updateByExampleSelective(map);
	}

	public boolean isValidDo(D dataObject) {
		return true;
	}

	public DataResult<D> findByPage(E example) {
		DataResult<D> dataResult = new DataResult<>();
		dataResult.setCount(mapper.countByExample(example));
		dataResult.setData(mapper.selectByExample(example));
		return dataResult;
	}

}
