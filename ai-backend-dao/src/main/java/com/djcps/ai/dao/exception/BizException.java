package com.djcps.ai.dao.exception;

import org.springframework.stereotype.Service;

/*
 * 业务异常类
 *
 * <AUTHOR>
 * @date 2021/9/13
 */
@Service
public class BizException extends RuntimeException {

    /**
     * 
     */
    private static final long serialVersionUID = 8934102860699517978L;

    /**
     * 异常编码
     */
    private int               code             = -1;

    /**
     * 提示信息
     */
    private String            message;

    public BizException(){

    }

    public BizException(int code, String message){
        super(message);
        this.code = code;
        this.message = message;

    }

    public BizException(String message){
        super(message);
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    /**
     * @return the message
     */
    public String getMessage() {
        return message;
    }

    /**
     * @param message the message to set
     */
    public void setMessage(String message) {
        this.message = message;
    }

}
