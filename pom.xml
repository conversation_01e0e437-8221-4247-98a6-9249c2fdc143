<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.4.7</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>

	<modelVersion>4.0.0</modelVersion>
	<groupId>com.dongjing</groupId>
	<artifactId>ai-backend</artifactId>
	<version>1.0-SNAPSHOT</version>
	<packaging>pom</packaging>

	<properties>
		<java.version>17</java.version>
		<maven.compiler.source>17</maven.compiler.source>
		<maven.compiler.target>17</maven.compiler.target>
		<fastjson.version>2.0.8</fastjson.version>
		<guava.version>32.1.3-jre</guava.version>
		<mysql.connector.java>8.4.0</mysql.connector.java>
		<druid.version>1.2.24</druid.version>
		<swagger.version>3.0.0</swagger.version>
		<springdoc.version>2.7.0</springdoc.version>
		<json-lib.version>2.4</json-lib.version>
		<hibernate.validator.version>8.0.1.Final</hibernate.validator.version>
		<spring.cloud.alibaba.dependencies>2023.0.3.2</spring.cloud.alibaba.dependencies>
		<spring.cloud.dependencies>2024.0.0</spring.cloud.dependencies>
		<spring.ai.version>1.0.0</spring.ai.version>
		<aliyun-log.version>0.6.64</aliyun-log.version>
		<commons-beanutils.version>1.9.4</commons-beanutils.version>

	</properties>
	<dependencies>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>transmittable-thread-local</artifactId>
			<version>2.14.5</version>
		</dependency>
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-spring-boot3-starter</artifactId>
			<version>3.5.7</version>
		</dependency>
		<dependency>
			<groupId>com.mysql</groupId>
			<artifactId>mysql-connector-j</artifactId>
			<version>${mysql.connector.java}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.apache.commons</groupId>
					<artifactId>commons-io</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-io</artifactId>
			<version>1.3.2</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-log4j2</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-pool2</artifactId>
		</dependency>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
			<version>${druid.version}</version>
		</dependency>

		<!-- Jakarta Servlet API for Spring Boot 3.x -->
		<dependency>
			<groupId>jakarta.servlet</groupId>
			<artifactId>jakarta.servlet-api</artifactId>
			<scope>provided</scope>
		</dependency>

		<!-- AspectJ for AOP -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
		</dependency>

		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-validator</artifactId>
			<version>${hibernate.validator.version}</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>
		<!-- 扩展json-lib，防止fastjson漏洞 -->
		<dependency>
			<groupId>net.sf.json-lib</groupId>
			<artifactId>json-lib</artifactId>
			<version>${json-lib.version}</version>
			<classifier>jdk15</classifier>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<scope>provided</scope>
		</dependency>

		<!--工具类 -->
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.8.39</version>
		</dependency>
		<!--工具类 end -->

		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>${guava.version}</version>
		</dependency>

		<!--监控 start -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>
		<!--监控 end -->

		<!-- beancopy,扩展工具类 -->
		<dependency>
			<groupId>commons-beanutils</groupId>
			<artifactId>commons-beanutils</artifactId>
			<version>${commons-beanutils.version}</version>
		</dependency>
		<!-- 上传文件 -->

		<dependency>
			<groupId>io.github.openfeign</groupId>
			<artifactId>feign-httpclient</artifactId>
		</dependency>
	</dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.ai</groupId>
				<artifactId>spring-ai-bom</artifactId>
				<version>${spring.ai.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>com.alibaba.cloud</groupId>
				<artifactId>spring-cloud-alibaba-dependencies</artifactId>
				<version>${spring.cloud.alibaba.dependencies}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>${spring.cloud.dependencies}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>

			<dependency>
				<groupId>com.ctrip.framework.apollo</groupId>
				<artifactId>apollo-client</artifactId>
				<version>2.3.0</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<distributionManagement>
		<snapshotRepository>
			<id>djcps-maven</id>
			<name>releases</name>
			<url>http://***********:8081/repository/djcps-maven/</url>
		</snapshotRepository>
		<repository>
			<id>djcps-maven</id>
			<name>releases</name>
			<url>http://***********:8081/repository/djcps-maven/</url>
		</repository>
	</distributionManagement>



	<build>
		<pluginManagement>
			<plugins>
				<!-- Maven Clean Plugin - 兼容Maven 3.1.1 -->
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-clean-plugin</artifactId>
					<version>3.1.0</version>
				</plugin>

				<!-- Maven Resources Plugin - 兼容Maven 3.1.1 -->
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-resources-plugin</artifactId>
					<version>3.1.0</version>
				</plugin>

				<!-- Maven Compiler Plugin - 兼容Maven 3.1.1 -->
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-compiler-plugin</artifactId>
					<version>3.8.1</version>
				</plugin>

				<!-- Maven Surefire Plugin - 兼容Maven 3.1.1 -->
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-surefire-plugin</artifactId>
					<version>2.22.2</version>
				</plugin>

				<!-- Maven Jar Plugin - 兼容Maven 3.1.1 -->
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-jar-plugin</artifactId>
					<version>3.1.2</version>
				</plugin>

				<!-- Maven Install Plugin - 兼容Maven 3.1.1 -->
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-install-plugin</artifactId>
					<version>2.5.2</version>
				</plugin>

				<!-- Maven Deploy Plugin - 兼容Maven 3.1.1 -->
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-deploy-plugin</artifactId>
					<version>2.8.2</version>
				</plugin>

				<!-- Maven Antrun Plugin - 兼容Maven 3.1.1 -->
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-antrun-plugin</artifactId>
					<version>1.8</version>
				</plugin>
			</plugins>
		</pluginManagement>

		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<release>17</release>
					<encoding>UTF-8</encoding>
					<annotationProcessorPaths>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
							<version>1.18.34</version>
						</path>
					</annotationProcessorPaths>
				</configuration>
			</plugin>
		</plugins>
	</build>

	<modules>
		<module>ai-backend-web</module>
		<module>ai-backend-service</module>
		<module>ai-backend-config</module>
		<module>ai-backend-dao</module>
		<module>ai-backend-core</module>
		<module>ai-backend-common</module>
		<module>ai-backend-tools</module>
		<module>ai-backend-utils</module>
		<module>ai-backend-feign</module>
  </modules>
</project>
