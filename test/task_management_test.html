<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务管理系统测试</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        // 模拟API配置
        const API_CONFIG = {
            TASK: {
                LIST: '/task/list',
                SAVE: '/task/save',
                DELETE: '/task/delete/{id}',
                RUN: '/task/run/{taskId}',
                DEBUG: '/task/debug/{taskId}/{count}',
                REANALYZE: '/task/reanalyze/{taskId}',
                REANALYZE_BY_BATCH: '/task/reanalyze/{taskId}/{batchNo}',
                RUN_RECORDS: '/task/runRecords/{taskId}',
                TASK_RESULTS: '/task/taskResults/{taskId}/{batchNo}',
                GENERATE_CRON: '/generate/corn',
                VALIDATE_CRON: '/task/validateCron'
            }
        };

        // 模拟API请求函数
        const apiRequest = async (url, options = {}, params = {}) => {
            console.log('API Request:', url, options, params);
            
            // 模拟延迟
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // 根据URL返回模拟数据
            if (url.includes('/task/list')) {
                return {
                    success: true,
                    data: [
                        {
                            id: 1,
                            title: '测试任务1',
                            remark: '这是一个测试任务',
                            templateId: '1,2',
                            cornExp: '0 0 9 * * ?',
                            cycleType: 'day',
                            paramList: '1',
                            reten: 0,
                            coverRepeat: 0,
                            createdAt: '2024-01-01 09:00:00',
                            updatedAt: '2024-01-01 09:00:00'
                        }
                    ]
                };
            }
            
            if (url.includes('/task/runRecords/')) {
                return {
                    success: true,
                    data: {
                        total: 25,
                        list: [
                            {
                                id: 1,
                                taskId: 1,
                                batchNo: 'batch_001_20240101',
                                execType: 'NORMAL',
                                execStatus: 'SUCCESS',
                                execStartTime: '2024-01-01T09:00:00',
                                execEndTime: '2024-01-01T09:05:00',
                                resultCount: 5,
                                createTime: '2024-01-01T09:00:00'
                            },
                            {
                                id: 2,
                                taskId: 1,
                                batchNo: 'batch_002_20240102',
                                execType: 'DEBUG',
                                execStatus: 'RUNNING',
                                execStartTime: '2024-01-02T09:00:00',
                                resultCount: 0,
                                createTime: '2024-01-02T09:00:00'
                            }
                        ]
                    }
                };
            }
            
            if (url.includes('/task/taskResults/')) {
                return {
                    success: true,
                    data: {
                        taskResults: [
                            {
                                id: 1,
                                taskId: 1,
                                skey: 'sales_analysis_001',
                                role: 'salesman',
                                dataDate: '2024-01-01',
                                createTime: '2024-01-01T09:00:00'
                            },
                            {
                                id: 2,
                                taskId: 1,
                                skey: 'sales_analysis_002',
                                role: 'manager',
                                dataDate: '2024-01-01',
                                createTime: '2024-01-01T09:05:00'
                            }
                        ],
                        taskResultItems: [
                            {
                                id: 1,
                                taskId: 1,
                                resultId: 1,
                                subBizType: 'summary',
                                itemResult: '销售额1000元，订单50个，表现良好',
                                createTime: '2024-01-01T09:00:00',
                                updateTime: '2024-01-01T09:05:00'
                            },
                            {
                                id: 2,
                                taskId: 1,
                                resultId: 1,
                                subBizType: 'product',
                                itemResult: '产品A销售600元，产品B销售400元',
                                createTime: '2024-01-01T09:01:00',
                                updateTime: '2024-01-01T09:06:00'
                            },
                            {
                                id: 3,
                                taskId: 1,
                                resultId: 2,
                                subBizType: 'summary',
                                itemResult: '总体销售情况良好，建议继续保持',
                                createTime: '2024-01-01T09:05:00',
                                updateTime: '2024-01-01T09:10:00'
                            }
                        ]
                    }
                };
            }
            
            return { success: true, data: 'Mock response' };
        };

        // 模拟消息显示函数
        window.showMessage = {
            success: (msg) => alert('成功: ' + msg),
            error: (msg) => alert('错误: ' + msg),
            warning: (msg) => alert('警告: ' + msg)
        };
        
        window.showConfirm = (msg) => confirm(msg);

        // 简化的TaskManagement组件用于测试
        function TaskManagementTest() {
            const [selectedTask, setSelectedTask] = React.useState({
                id: 1,
                title: '测试任务1',
                remark: '这是一个测试任务'
            });
            const [activeTab, setActiveTab] = React.useState('execRecords');
            const [runRecords, setRunRecords] = React.useState([]);
            const [selectedRecord, setSelectedRecord] = React.useState(null);
            const [taskResultDetails, setTaskResultDetails] = React.useState(null);
            const [loadingRunRecords, setLoadingRunRecords] = React.useState(false);
            const [loadingTaskResults, setLoadingTaskResults] = React.useState(false);
            const [selectedTaskResult, setSelectedTaskResult] = React.useState(null);

            React.useEffect(() => {
                if (selectedTask) {
                    fetchRunRecords();
                }
            }, [selectedTask]);

            const fetchRunRecords = async () => {
                setLoadingRunRecords(true);
                try {
                    const result = await apiRequest('/task/runRecords/1?pageNum=1&pageSize=20');
                    if (result.success) {
                        setRunRecords(result.data.list);
                    }
                } finally {
                    setLoadingRunRecords(false);
                }
            };

            const fetchTaskResultDetails = async (taskId, batchNo) => {
                setLoadingTaskResults(true);
                try {
                    const result = await apiRequest(`/task/taskResults/${taskId}/${batchNo}`);
                    if (result.success) {
                        setTaskResultDetails(result.data);
                        setSelectedTaskResult(null); // 重置选中的TaskResult
                    }
                } finally {
                    setLoadingTaskResults(false);
                }
            };

            const handleRecordClick = (record) => {
                setSelectedRecord(record);
                fetchTaskResultDetails(selectedTask.id, record.batchNo);
            };

            return (
                <div className="p-6 max-w-7xl mx-auto">
                    <h1 className="text-2xl font-bold mb-6">任务管理系统测试</h1>
                    
                    {/* Tab 导航 */}
                    <div className="flex border-b border-gray-200 mb-4">
                        <button
                            onClick={() => setActiveTab('execRecords')}
                            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                                activeTab === 'execRecords'
                                    ? 'border-blue-500 text-blue-600'
                                    : 'border-transparent text-gray-500 hover:text-gray-700'
                            }`}
                        >
                            执行记录
                        </button>
                        <button
                            onClick={() => setActiveTab('taskDetail')}
                            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                                activeTab === 'taskDetail'
                                    ? 'border-blue-500 text-blue-600'
                                    : 'border-transparent text-gray-500 hover:text-gray-700'
                            }`}
                        >
                            任务详情
                        </button>
                    </div>

                    {activeTab === 'execRecords' && (
                        <div className="flex space-x-4">
                            {/* 左侧：执行记录列表 - 进一步缩小 */}
                            <div className="w-1/5">
                                <h3 className="text-lg font-medium mb-4">执行记录</h3>
                                {loadingRunRecords ? (
                                    <div className="text-center py-8">加载中...</div>
                                ) : (
                                    <div className="space-y-3">
                                        {runRecords.map(record => (
                                            <div
                                                key={record.id}
                                                className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                                                    selectedRecord?.id === record.id ? 'bg-blue-50 border-blue-300' : 'hover:bg-gray-50 border-gray-200'
                                                }`}
                                                onClick={() => handleRecordClick(record)}
                                            >
                                                <div className="font-semibold text-gray-900 mb-2">{record.batchNo}</div>
                                                <div className="flex space-x-2 mb-2">
                                                    <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                                        {record.execType}
                                                    </span>
                                                    <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                                                        {record.execStatus}
                                                    </span>
                                                </div>
                                                <div className="text-xs text-gray-500">
                                                    开始: {record.execStartTime ? new Date(record.execStartTime).toLocaleString() : '未知'}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>

                            {/* 右侧：结果详情 - 进一步增加宽度 */}
                            <div className="w-4/5">
                                <h3 className="text-lg font-medium mb-4">结果详情</h3>
                                {loadingTaskResults ? (
                                    <div className="text-center py-8">加载结果详情中...</div>
                                ) : taskResultDetails ? (
                                    <div className="flex space-x-4 h-96">
                                        {/* TaskResult列表 - 进一步缩小 */}
                                        <div className="w-1/4">
                                            <h4 className="text-sm font-medium mb-3">任务结果</h4>
                                            <div className="space-y-2">
                                                {taskResultDetails.taskResults?.map((result, index) => (
                                                    <div
                                                        key={result.id}
                                                        className={`p-3 border rounded cursor-pointer transition-colors ${
                                                            selectedTaskResult?.id === result.id ? 'bg-blue-50 border-blue-200' : 'hover:bg-gray-50'
                                                        }`}
                                                        onClick={() => setSelectedTaskResult(result)}
                                                    >
                                                        <div className="font-medium text-sm">{result.skey}</div>
                                                        <div className="text-xs text-gray-500 mt-1">
                                                            {result.role} - {result.dataDate}
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>

                                        {/* TaskResultItem列表 */}
                                        <div className="w-2/3">
                                            <h4 className="text-sm font-medium mb-3">分析结果</h4>
                                            {selectedTaskResult ? (
                                                <div className="space-y-3 max-h-80 overflow-y-auto">
                                                    {taskResultDetails.taskResultItems
                                                        ?.filter(item => item.resultId === selectedTaskResult.id)
                                                        ?.map((item, index) => (
                                                            <div key={item.id} className="border rounded p-3">
                                                                <div className="flex items-center space-x-2 mb-2">
                                                                    <span className="text-sm font-medium">分析项 #{index + 1}</span>
                                                                    <span className="px-2 py-0.5 bg-gray-100 text-gray-800 text-xs rounded">
                                                                        {item.subBizType}
                                                                    </span>
                                                                </div>

                                                                {/* 时间信息 */}
                                                                <div className="mb-2 text-xs text-gray-500 space-y-1">
                                                                    <div>创建: {item.createTime ? new Date(item.createTime).toLocaleString() : '未知'}</div>
                                                                    {item.updateTime && (
                                                                        <div>更新: {new Date(item.updateTime).toLocaleString()}</div>
                                                                    )}
                                                                </div>

                                                                <div className="bg-blue-50 p-2 rounded text-sm">
                                                                    {item.itemResult}
                                                                </div>
                                                            </div>
                                                        ))}
                                                </div>
                                            ) : (
                                                <div className="text-center py-8 text-gray-500">
                                                    选择左侧任务结果查看详情
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                ) : (
                                    <div className="text-center py-8 text-gray-500">
                                        点击执行记录查看详情
                                    </div>
                                )}
                            </div>
                        </div>
                    )}

                    {activeTab === 'taskDetail' && (
                        <div>
                            <h3 className="text-lg font-medium mb-4">任务详情</h3>
                            <div className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">任务名称:</label>
                                    <p className="text-gray-600">{selectedTask.title}</p>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">任务描述:</label>
                                    <p className="text-gray-600">{selectedTask.remark}</p>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            );
        }

        ReactDOM.render(<TaskManagementTest />, document.getElementById('root'));
    </script>
</body>
</html>
