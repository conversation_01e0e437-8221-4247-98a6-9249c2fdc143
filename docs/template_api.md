# 用户模板API文档

## 概述

用户模板系统支持模板和子项的管理。每个模板可以包含多个子项，每个子项有独立的方法和提示词。模板本身有一个总的提示词，用于汇总子项产出的内容。

## 数据结构

### UserTemplate (模板)
```json
{
  "id": 1,
  "userId": "958",
  "title": "模板标题",
  "remark": "模板备注",
  "bizType": "业务类型",
  "prompt": "模板总提示词，用于汇总子项产出的内容",
  "apis": "API配置",
  "createTime": "2024-01-01T00:00:00",
  "isDel": 0
}
```

### UserTemplateItem (模板子项)
```json
{
  "id": 1,
  "templateId": 1,
  "methodId": 1,
  "prompt": "子项提示词",
  "createTime": "2024-01-01T00:00:00",
  "isDel": 0
}
```

### UserTemplateDto (模板DTO，包含子项)
```json
{
  "id": 1,
  "userId": "958",
  "title": "模板标题",
  "remark": "模板备注",
  "bizType": "业务类型",
  "prompt": "模板总提示词",
  "apis": "API配置",
  "items": [
    {
      "id": 1,
      "templateId": 1,
      "methodId": 1,
      "prompt": "子项提示词1"
    },
    {
      "id": 2,
      "templateId": 1,
      "methodId": 2,
      "prompt": "子项提示词2"
    }
  ]
}
```

## API接口

### 1. 获取模板列表
**GET** `/template/list`

**响应:**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "title": "模板1",
      "remark": "备注1"
    }
  ]
}
```

### 2. 获取模板详情（包含子项）
**GET** `/template/detail/{id}`

**响应:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "title": "模板标题",
    "prompt": "模板总提示词",
    "items": [
      {
        "id": 1,
        "methodId": 1,
        "prompt": "子项提示词1"
      }
    ]
  }
}
```

### 3. 保存模板（仅基本信息）
**POST** `/template/save`

**请求体:**
```json
{
  "title": "模板标题",
  "remark": "模板备注",
  "bizType": "ai_report",
  "prompt": "模板总提示词"
}
```

### 4. 保存模板及子项
**POST** `/template/saveWithItems`

**请求体:**
```json
{
  "title": "模板标题",
  "prompt": "模板总提示词",
  "items": [
    {
      "methodId": 1,
      "prompt": "子项提示词1"
    },
    {
      "methodId": 2,
      "prompt": "子项提示词2"
    }
  ]
}
```

### 5. 获取模板子项列表
**GET** `/template/items/{templateId}`

**响应:**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "templateId": 1,
      "methodId": 1,
      "prompt": "子项提示词1"
    }
  ]
}
```

### 6. 保存模板子项
**POST** `/template/items/{templateId}`

**请求体:**
```json
[
  {
    "methodId": 1,
    "prompt": "子项提示词1"
  },
  {
    "methodId": 2,
    "prompt": "子项提示词2"
  }
]
```

### 7. 删除模板（包含子项）
**POST** `/template/delete/{id}`

**响应:**
```json
{
  "code": 200,
  "message": "success",
  "data": true
}
```

## 业务逻辑说明

1. **模板结构**: 一个模板可以有多个子项，每个子项关联一个方法(method_id)和独立的提示词
2. **总提示词**: 模板有一个总的提示词，用于汇总所有子项产出的内容
3. **事务处理**: 保存和删除操作都使用事务确保数据一致性
4. **级联删除**: 删除模板时会自动删除所有关联的子项
5. **批量操作**: 支持批量保存子项，会先删除原有子项再保存新的子项

## 使用场景

1. **创建模板**: 先创建模板基本信息，再添加子项
2. **编辑模板**: 可以单独编辑模板信息或子项信息
3. **执行模板**: 按顺序执行各个子项的方法，最后用总提示词汇总结果

## 前端界面更新说明

### 新增功能

1. **子项管理界面**: 在模板详情页面添加了"管理子项"按钮，点击后可以查看和管理模板的子项
2. **子项列表显示**: 显示每个子项关联的方法名称和提示词
3. **子项编辑功能**: 支持在线编辑子项的方法和提示词
4. **子项增删功能**: 支持添加新子项和删除现有子项
5. **表单集成**: 在创建/编辑模板表单中集成了子项管理功能

### 界面变化

1. **模板详情页面**:
   - 添加了"管理子项"按钮
   - 显示子项数量统计
   - 提示词标签改为"模板总提示词（用于汇总子项结果）"
   - 新增子项管理折叠面板

2. **创建/编辑表单**:
   - 集成了子项管理界面
   - 支持在创建模板时同时添加子项
   - 提示词字段说明更加明确

3. **API配置更新**:
   - 新增了模板子项相关的API端点
   - 支持获取模板详情（包含子项）
   - 支持保存模板及其子项

### 使用流程

1. **创建带子项的模板**:
   - 点击"创建新模板"
   - 填写模板基本信息
   - 在子项管理区域添加子项（选择方法，填写提示词）
   - 填写模板总提示词
   - 点击保存

2. **管理现有模板的子项**:
   - 选择模板
   - 点击"管理子项"按钮
   - 在展开的界面中编辑、添加或删除子项
   - 修改会实时保存到本地状态
   - 需要点击"保存"按钮才会提交到后端

3. **编辑模板**:
   - 选择模板后点击"编辑模板"
   - 在编辑表单中可以同时修改模板信息和子项
   - 保存时会同时更新模板和子项信息
