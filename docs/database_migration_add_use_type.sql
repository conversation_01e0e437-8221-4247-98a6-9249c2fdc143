-- 为 task_invoke_method 表添加 use_type 字段
-- 执行时间：2025-07-31

-- 添加 use_type 字段
ALTER TABLE task_invoke_method
ADD COLUMN use_type VARCHAR(20) NOT NULL DEFAULT 'parameter'
COMMENT '使用类型: parameter 参数, fetchData 获取数据, system 系统';

-- 添加索引以提高查询性能
CREATE INDEX idx_task_invoke_method_use_type ON task_invoke_method(use_type);

-- 验证字段添加成功
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'task_invoke_method' 
AND COLUMN_NAME = 'use_type';
