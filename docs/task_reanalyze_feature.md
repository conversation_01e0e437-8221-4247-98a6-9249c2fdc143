# 任务管理系统功能说明

## 功能概述

任务重新分析功能允许用户对已执行的任务进行重新分析，而无需重新执行数据查询。该功能基于已存储的原始数据（origin_data）重新调用分析功能，更新分析结果（item_result）。

## 表设计优化

### 原始表设计问题
- 缺少执行时间信息
- 缺少执行状态
- 缺少执行类型区分
- 缺少索引优化

### 优化后的表结构
```sql
CREATE TABLE `ai-backend`.user_task_run_record
(
    id              BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_id         BIGINT                                NOT NULL COMMENT '任务ID',
    batch_no        VARCHAR(40)                           NOT NULL COMMENT '批次号',
    exec_type       ENUM('NORMAL', 'DEBUG', 'REANALYZE') NOT NULL DEFAULT 'NORMAL' COMMENT '执行类型',
    exec_status     ENUM('RUNNING', 'SUCCESS', 'FAILED') NOT NULL DEFAULT 'RUNNING' COMMENT '执行状态',
    exec_start_time TIMESTAMP                             NULL COMMENT '执行开始时间',
    exec_end_time   TIMESTAMP                             NULL COMMENT '执行结束时间',
    result_count    INT                                   NULL DEFAULT 0 COMMENT '处理的结果项数量',
    error_message   TEXT                                  NULL COMMENT '错误信息',
    create_time     TIMESTAMP                             NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time     TIMESTAMP                             NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_del          TINYINT                               NOT NULL DEFAULT 0 COMMENT '是否删除',
    
    INDEX idx_task_id_create_time (task_id, create_time DESC),
    INDEX idx_batch_no (batch_no),
    INDEX idx_exec_status (exec_status),
    INDEX idx_exec_type (exec_type)
);
```

## 功能实现

### 1. 执行记录管理
- **正常执行记录**：每次正常执行任务时自动记录批次号和执行信息
- **调试执行记录**：Debug执行时记录为DEBUG类型
- **重新分析记录**：重新分析时记录为REANALYZE类型

### 2. 重新分析逻辑
- 获取最近一次正常执行成功的批次号
- 根据批次号筛选要重新分析的数据
- 重新调用分析功能处理原始数据
- 更新分析结果到item_result字段

### 3. 前端界面
- **任务详情页面**：显示最近5次执行记录
- **精确的重新分析**：只在正常执行且成功的记录上显示重新分析按钮
- **批次级别控制**：每个执行记录都可以独立进行重新分析
- **执行状态显示**：实时显示执行状态和结果

## API接口

### 1. 重新分析任务（最新批次）
```
POST /task/reanalyze/{taskId}
```

### 2. 根据批次号重新分析任务
```
POST /task/reanalyze/{taskId}/{batchNo}
```

### 3. 获取执行记录
```
POST /task/runRecords/{taskId}
```

## 使用流程

1. **查看执行记录**：在任务详情页面底部查看最近5次执行记录
2. **选择要重新分析的批次**：在执行记录列表中，对于正常执行且成功的记录，会显示"重新分析"按钮
3. **确认操作**：点击重新分析按钮，系统弹出确认对话框
4. **执行分析**：系统根据选定的批次号重新分析该批次的数据
5. **查看结果**：分析完成后显示处理的结果项数量，并自动刷新执行记录

## 技术特点

- **数据安全**：只使用已存储的原始数据，不重新查询
- **精确控制**：基于批次号精确控制分析范围，可以选择任意成功的执行批次进行重新分析
- **状态跟踪**：完整的执行状态和错误信息记录
- **用户体验**：友好的界面交互和状态反馈，重新分析按钮直接集成在执行记录中
- **性能优化**：合理的索引设计提升查询性能
- **灵活性**：支持对历史任意成功批次的重新分析

## 界面优化

1. **移除了任务列表和详情页面的重新分析按钮**：避免界面混乱
2. **集成到执行记录中**：重新分析按钮直接显示在每个可重新分析的执行记录上
3. **智能显示**：只对正常执行且成功的记录显示重新分析按钮
4. **状态反馈**：重新分析过程中显示加载状态，完成后自动刷新记录

## 新增功能特性

### 1. Tab布局优化
- **任务详情Tab**：显示任务的基本信息、配置参数等
- **执行记录Tab**：显示任务的执行历史和结果详情
- **默认行为**：点击任务列表时默认打开执行记录Tab

### 2. 分页执行记录
- **分页显示**：执行记录支持分页，默认每页20条
- **实时加载**：点击任务时才加载执行记录，提升性能
- **状态筛选**：可以查看所有类型的执行记录（正常、调试、重新分析）

### 3. 结果详情查看
- **两层结构**：
  - 第一层：TaskResult（任务结果概览）
  - 第二层：TaskResultItem（具体结果项）
- **按业务类型分组**：根据sub_biz_type进行Tab切换展示
- **完整信息**：显示提示词、原始数据、分析结果等完整信息

### 4. 实时刷新机制
- **智能刷新**：当执行记录状态为RUNNING时，自动每10秒刷新一次
- **自动停止**：任务完成后自动停止刷新
- **手动控制**：用户可以随时切换查看其他记录

## API接口

### 1. 重新分析任务（最新批次）
```
POST /task/reanalyze/{taskId}
```

### 2. 根据批次号重新分析任务
```
POST /task/reanalyze/{taskId}/{batchNo}
```

### 3. 分页获取执行记录
```
POST /task/runRecords/{taskId}?pageNum=1&pageSize=20
```

### 4. 获取任务结果详情
```
POST /task/taskResults/{taskId}/{batchNo}
```

## 使用流程

### 基本操作流程
1. **选择任务**：在任务列表中点击任务，默认打开执行记录Tab
2. **查看执行历史**：在执行记录列表中浏览历史执行情况
3. **查看结果详情**：点击具体的执行记录查看详细结果
4. **切换业务类型**：在结果详情中点击不同的sub_biz_type Tab查看分类结果
5. **重新分析**：对成功的正常执行记录点击重新分析按钮

### 高级功能
1. **实时监控**：选择运行中的执行记录，系统自动刷新状态
2. **历史分析**：可以查看和重新分析任意历史成功批次
3. **分页浏览**：使用分页功能浏览大量执行记录

## 注意事项

1. 只有正常执行且成功的记录才能进行重新分析
2. 重新分析过程中会创建新的执行记录，类型为REANALYZE
3. 原始数据为空的结果项会被跳过
4. 可以对任意历史成功批次进行重新分析，不限于最近一次
5. 运行中的任务会自动刷新，完成后需要手动刷新查看最新状态
6. 结果详情按需加载，提升系统性能
