# TaskInvokeMethod 添加 use_type 字段功能实现

## 概述
为 `task_invoke_method` 表添加 `use_type` 字段，用于区分方法的使用类型：
- `parameter`: 参数类型（默认值）
- `fetchData`: 获取数据类型
- `system`: 系统类型

## 修改内容

### 1. 数据库层修改

#### 1.1 实体类修改
**文件**: `ai-backend-dao/src/main/java/com/djcps/ai/dao/entity/TaskInvokeMethod.java`
- 添加 `useType` 字段，默认值为 "parameter"
- 添加 `@NotBlank` 验证注解

#### 1.2 DTO修改
**文件**: `ai-backend-dao/src/main/java/com/djcps/ai/dao/dto/TaskInvokeMethodPageDto.java`
- 添加 `useType` 查询条件字段

#### 1.3 数据库迁移脚本
**文件**: `docs/database_migration_add_use_type.sql`
- 添加 `use_type` 字段到 `task_invoke_method` 表
- 设置默认值为 'parameter'
- 添加索引以提高查询性能

### 2. 后端服务层修改

#### 2.1 Service层修改
**文件**: `ai-backend-service/src/main/java/com/djcps/ai/service/TaskInvokeMethodService.java`
- 在 `getPageList` 方法中添加 `useType` 查询条件支持

### 3. 前端页面修改

#### 3.1 方法注册管理页面
**文件**: `ai-backend-web/src/main/resources/static/js/components/TaskInvokeMethodManagement.js`

**修改内容**:
- 添加 `useType` 字段到表单数据
- 添加使用类型选项配置
- 修改搜索表单，添加使用类型筛选
- 修改表格显示，添加使用类型列
- 修改编辑表单，添加使用类型选择

**新增功能**:
- 搜索表单支持按使用类型筛选
- 表格显示使用类型（参数/获取数据）
- 编辑时可选择使用类型

#### 3.2 任务管理页面
**文件**: `ai-backend-web/src/main/resources/static/js/components/TaskManagement.js`
- 修改 `fetchToolsData` 方法，只查询 `useType: 'parameter'` 的方法
- 修改所有相关称谓：将"选择方法"改为"选择参数"

#### 3.3 模板管理页面
**文件**: `ai-backend-web/src/main/resources/static/js/components/TemplateManagement.js`
- 修改 `fetchApiList` 方法，只查询 `useType: 'fetchData'` 的方法
- 修改所有相关称谓：将"关联方法"改为"数据源"

### 4. 业务逻辑分离

#### 4.1 任务管理
- 只显示和使用 `parameter` 类型的方法
- 用于任务执行时的参数配置
- 界面称谓统一为"参数"

#### 4.2 模板管理
- 只显示和使用 `fetchData` 类型的方法
- 用于模板关联的数据获取方法
- 界面称谓统一为"数据源"

## 使用说明

### 1. 数据库迁移
执行 `docs/database_migration_add_use_type.sql` 脚本来添加新字段。

### 2. 方法注册
在方法注册管理页面中：
- 新增方法时可选择使用类型
- 编辑现有方法时可修改使用类型
- 可按使用类型进行筛选查询

### 3. 任务管理
- 创建任务时只能选择 `parameter` 类型的方法
- 确保任务执行的方法都是参数类型
- 界面显示为"选择参数"

### 4. 模板管理
- 创建模板时只能选择 `fetchData` 类型的方法
- 确保模板关联的方法都是数据获取类型
- 界面显示为"数据源"

## 注意事项

1. 现有数据会自动设置为 `parameter` 类型（默认值）
2. 新增方法时默认为 `parameter` 类型
3. 任务管理和模板管理现在使用不同类型的方法，避免了混淆
4. 所有修改都保持向后兼容性
5. 新增了 `system` 类型，用于系统级方法
6. 界面称谓已统一：任务管理中为"参数"，模板管理中为"数据源"

## 测试建议

1. 验证数据库字段添加成功
2. 测试方法注册管理的增删改查功能
3. 验证任务管理只显示 parameter 类型方法
4. 验证模板管理只显示 fetchData 类型方法
5. 测试搜索和筛选功能
