package com.djcps.ai.core.vo.in;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DifyConversationDelInDto {

    /**
     * 名称, 如果auto_generate为true，可以传空字符，自动生成
     */
    @NotEmpty
    @NotBlank
    @NonNull
    private String conversationId;
}
