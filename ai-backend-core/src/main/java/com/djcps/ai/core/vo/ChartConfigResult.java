package com.djcps.ai.core.vo;

import cn.hutool.core.map.MapUtil;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
public class ChartConfigResult {
    @NotBlank(message = "sql不能为空")
    @NotNull
    private String sql;
    @NotEmpty(message = "type不能为空")
    @NotNull
    private List<String> type;
    @NotBlank(message = "x不能为空")
    @NotNull
    private String x;
    @NotNull(message = "yAxis不能为空")
    @NotEmpty
    private List<ChartYvo> yAxis = new ArrayList<>();
    private String resultType;

    public void putYAxis(Map<String, Integer> dimensionToGroupIndex) {
        if (MapUtil.isEmpty(dimensionToGroupIndex)) {
            return;
        }
        //抽取yAxis成为map
        yAxis.forEach(y -> dimensionToGroupIndex.put(y.getName(), y.getYAxisIndex()));
        yAxis.clear();
        for (Map.Entry<String, Integer> entry : dimensionToGroupIndex.entrySet()) {
            yAxis.add(new ChartYvo(entry.getKey(), entry.getValue()));
        }
    }
}
