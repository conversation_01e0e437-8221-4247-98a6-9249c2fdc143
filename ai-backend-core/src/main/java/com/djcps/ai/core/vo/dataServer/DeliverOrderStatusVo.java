package com.djcps.ai.core.vo.dataServer;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class DeliverOrderStatusVo {
    @JsonProperty("start_time")
    private String startTime;
    @JsonProperty("end_time")
    private String endTime;
    @JsonProperty("cycle_type")
    private String cycleType;
    @JsonProperty("supplier_list")
    private List<String> supplierList=new ArrayList<>();
    @JsonProperty("union_supplier_id")
    private String unionSupplierId;
    @JsonProperty("config_list")
    private List<DeliveryConfigJSON> configList=new ArrayList<>();
}
