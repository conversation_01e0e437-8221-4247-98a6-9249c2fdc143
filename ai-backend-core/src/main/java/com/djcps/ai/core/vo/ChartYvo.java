package com.djcps.ai.core.vo;

import lombok.Data;

@Data
public class ChartYvo {
    /**
     * y轴名称
     */
    private String name;
    /**
     * y轴类型
     */
    private String type;
    /**
     * y轴索引
     */
    private int yAxisIndex;

    public ChartYvo() {
    }

    public ChartYvo(String name, String type, int yAxisIndex) {
        this.name = name;
        this.type = type;
        this.yAxisIndex = yAxisIndex;
    }

    public ChartYvo(String name, int yAxisIndex) {
        this.name = name;
        this.yAxisIndex = yAxisIndex;
    }
}
