package com.djcps.ai.core.utils;

import com.djcps.ai.common.enums.SystemCodeEnum;
import com.djcps.ai.common.result.WebResultExt;
import lombok.Data;

/**
 * 类 CoreResultExt.java 描述: core层result
 *
 * @author: yeb 2024/7/15 上午10:43
 **/
@Data
public class CoreResultExt<T> {

    private boolean success = false;
    private String  code    = "";   // "0/-1"
    private String  msg     = "";
    private T       data;

    public CoreResultExt(boolean success){
        this.success = success;
        this.code = "1000";
    }

    public static <T> CoreResultExt<T> success() {
        return new CoreResultExt<>(true);
    }

    public CoreResultExt(T data) {
        this.code = "1000";
        this.msg = "请求成功！";
        this.success = true;
        this.data = data;
    }

}
