package com.djcps.ai.core.vo.in.easyorder;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 任务结果保存参数
 *
 * <AUTHOR> Assistant
 * @create 2025-08-05
 */
@Data
public class TaskResultSaveBo {
    
    private String skey;
    
    private Long taskId;
    
    private String dataDate;
    
    private String bizType;
    
    private String cycleType;
    
    private LocalDateTime execStartTime;
    
    private String batchNo;
    
    private LocalDateTime execEndTime;
    
    private String userId;
    
    private String orgId;
    
    private String supplierId;
    
    /**
     * 角色: 业务员 salesman, 团长 group, 总经理 manager
     */
    private String role;
}
