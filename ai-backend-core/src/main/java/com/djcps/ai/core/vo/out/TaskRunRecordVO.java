package com.djcps.ai.core.vo.out;

import lombok.Data;

/**
 * 任务执行记录VO
 */
@Data
public class TaskRunRecordVO {
    
    /**
     * 记录ID
     */
    private Long id;
    
    /**
     * 任务ID
     */
    private Long taskId;
    
    /**
     * 批次号
     */
    private String batchNo;
    
    /**
     * 执行类型：NORMAL-正常执行，DEBUG-调试执行，REANALYZE-重新分析
     */
    private String execType;
    
    /**
     * 执行状态：RUNNING-运行中，SUCCESS-成功，FAILED-失败
     */
    private String execStatus;
    
    /**
     * 执行开始时间
     */
    private String execStartTime;
    
    /**
     * 执行结束时间
     */
    private String execEndTime;
    
    /**
     * 处理的结果项数量
     */
    private Integer resultCount;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 创建时间
     */
    private String createTime;
}
