package com.djcps.ai.core.vo.yDivider;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

public class SmartEChartsAxisGrouper {

    /**
     * 处理Y轴,给出合理的Y轴索引
     *
     * @param configResult
     * @param resultData
     */
    public static Map<String, Integer> processYAxis(List<Map<String, Object>> resultData) {
        Map<String, BigDecimal> maxValues = new HashMap<>();
        Map<String, BigDecimal> minValues = new HashMap<>();

        // 计算每个维度的最大值和最小值
        for (Map<String, Object> data : resultData) {
            if (CollUtil.isEmpty(data)) {
                continue;
            }
            for (Map.Entry<String, Object> entry : data.entrySet()) {
                if (entry == null) {
                    continue;
                }
                String key = entry.getKey();
                Object value = entry.getValue();
                if (StrUtil.isEmpty(key) || ObjectUtil.isEmpty(value)) {
                    continue;
                }
                if (ObjectUtil.isValidIfNumber(value) && NumberUtil.isNumber(value.toString())) {
                    BigDecimal bigDecimal = new BigDecimal(value.toString());
                    maxValues.put(key, maxValues.getOrDefault(key, BigDecimal.ZERO).max(bigDecimal));
                    minValues.put(key, minValues.getOrDefault(key, BigDecimal.ZERO).min(bigDecimal));
                }
            }
        }

        // 计算每个维度的数值范围并排序
        List<Map.Entry<String, BigDecimal>> sortedRanges = new ArrayList<>();
        for (String key : maxValues.keySet()) {
            BigDecimal range = maxValues.get(key).subtract(minValues.get(key));
            if (range.compareTo(BigDecimal.ZERO) > 0) {
                sortedRanges.add(new AbstractMap.SimpleEntry<>(key, range));
            }
        }
        sortedRanges.sort((a, b) -> b.getValue().compareTo(a.getValue())); // 降序排列

        // 使用相对差异来分组
        List<List<String>> groups = groupByRelativeDifference(sortedRanges);

        // 为每个组分配连续的索引
        Map<String, Integer> dimensionToGroupIndex = new HashMap<>();
        int index = 0;
        for (List<String> group : groups) {
            if (!group.isEmpty()) {
                for (String dimension : group) {
                    dimensionToGroupIndex.put(dimension, index);
                }
                index++;
            }
        }

        return ensureIndicesStartFromZero(dimensionToGroupIndex);
    }

    private static List<List<String>> groupByRelativeDifference(List<Map.Entry<String, BigDecimal>> sortedRanges) {
        List<List<String>> groups = new ArrayList<>();
        if (sortedRanges.isEmpty()) {
            return groups;
        }

        // 初始化第一个组
        List<String> currentGroup = new ArrayList<>();
        currentGroup.add(sortedRanges.get(0).getKey());
        groups.add(currentGroup);

        // 相对差异阈值，可以根据需要调整
        final BigDecimal THRESHOLD = new BigDecimal("0.3"); // 30%的相对差异

        for (int i = 1; i < sortedRanges.size(); i++) {
            BigDecimal currentValue = sortedRanges.get(i).getValue();
            BigDecimal previousValue = sortedRanges.get(i - 1).getValue();

            // 计算相对差异
            BigDecimal relativeDiff = previousValue.subtract(currentValue)
                    .divide(previousValue, 4, RoundingMode.HALF_UP)
                    .abs();

            if (relativeDiff.compareTo(THRESHOLD) > 0 && groups.size() < 3) {
                // 如果相对差异大于阈值且组数小于3，创建新组
                currentGroup = new ArrayList<>();
                groups.add(currentGroup);
            }

            // 将当前维度添加到最后一个组
            groups.get(groups.size() - 1).add(sortedRanges.get(i).getKey());
        }

        return groups;
    }

    // 修改方法名和实现，确保索引从0开始
    private static Map<String, Integer> ensureIndicesStartFromZero(Map<String, Integer> originalMap) {
        Map<String, Integer> newMap = new HashMap<>();
        if (CollUtil.isEmpty(originalMap)) {
            return newMap;
        }
        int minIndex = Collections.min(originalMap.values());

        for (Map.Entry<String, Integer> entry : originalMap.entrySet()) {
            Integer value = entry.getValue();
            if (value == null) {
                continue;
            }
            int oldIndex = value;
            int newIndex = oldIndex - minIndex;
            newMap.put(entry.getKey(), newIndex);
        }

        return newMap;
    }

    public static void main(String args[]) {
        String data = "[{\"接单月份\":\"2024-01\",\"接单材质个数\":\"21\",\"接单面积\":\"125391.97\"},{\"接单月份\":\"2024-02\",\"接单材质个数\":\"11\",\"接单面积\":\"12740.63\"},{\"接单月份\":\"2024-03\",\"接单材质个数\":\"25\",\"接单面积\":\"304097.65\"},{\"接单月份\":\"2024-04\",\"接单材质个数\":\"11\",\"接单面积\":\"88081.97\"},{\"接单月份\":\"2024-05\",\"接单材质个数\":\"17\",\"接单面积\":\"1080270.53\"},{\"接单月份\":\"2024-06\",\"接单材质个数\":\"14\",\"接单面积\":\"223542.58\"},{\"接单月份\":\"2024-07\",\"接单材质个数\":\"23\",\"接单面积\":\"999805.84\"},{\"接单月份\":\"2024-08\",\"接单材质个数\":\"28\",\"接单面积\":\"3361196.01\"},{\"接单月份\":\"2024-09\",\"接单材质个数\":\"21\",\"接单面积\":\"707803.95\"}]";
        List resultData = JSONUtil.toList(JSONUtil.parseArray(data), LinkedHashMap.class);
        Map<String, Integer> processedYAxis = processYAxis(resultData);
        System.out.println(processedYAxis);

    }
}
