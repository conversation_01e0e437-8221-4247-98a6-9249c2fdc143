package com.djcps.ai.core.client;

import com.djcps.ai.common.result.WebResultExt;
import com.djcps.ai.common.vo.PageResult;
import com.djcps.ai.core.vo.GoalVo;
import com.djcps.ai.core.vo.SupplierCondition;
import com.djcps.ai.core.vo.SupplierInfoVo;
import com.djcps.ai.core.vo.UnionSupplierInfoVo;
import com.djcps.ai.core.vo.dataServer.DeliveryConfig;
import com.djcps.ai.core.vo.dataServer.DeliveryGoal;
import com.djcps.ai.core.vo.in.easyorder.TaskResultDeleteBo;
import com.djcps.ai.core.vo.in.easyorder.TaskResultItemSaveBo;
import com.djcps.ai.core.vo.in.easyorder.TaskResultItemUpdateBo;
import com.djcps.ai.core.vo.in.easyorder.TaskResultSaveBo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * crm feign地址
 *
 * <AUTHOR>
 * @date 2021/3/15 9:31
 */
@FeignClient(name = "easyOrder", url = "${client.url.easyOrder}",path = "/DJEASYORDER")
public interface EasyOrderFeign {

    /**
     * 获取所有联合供应商信息（外部调用）
     */
    @PostMapping("/union/getAllUnionSupplierInfoCommon.do")
    WebResultExt<PageResult<UnionSupplierInfoVo>> getAllUnionSupplierInfoCommon();

    /**
     * 获取所有开启了营销赋能的供应商
     * @param supplierCondition
     * @return
     */
    @PostMapping("/supplierAuth/common/getByEnergizeTypeCommon.do")
    WebResultExt<PageResult<SupplierInfoVo>> getByEnergizeTypeCommon(SupplierCondition supplierCondition);
    /**
     * 获取所有交付率配置信息
     */
    @PostMapping("/delivery/rate/common/getAllConfigCommon.do")
    WebResultExt<PageResult<DeliveryConfig>> getAllConfigCommon();

    @PostMapping("/delivery/rate/common/getAllDeliveryRateGoalCommon.do")
    WebResultExt<PageResult<DeliveryGoal>> getAllGoal(@RequestBody GoalVo yearMonth);

    /**
     * 删除任务结果
     */
    @PostMapping("/result/delete.do")
    WebResultExt<String> deleteTaskResult(@RequestBody TaskResultDeleteBo bo);

    /**
     * 保存task_result_item记录
     */
    @PostMapping("/result/saveItem.do")
    WebResultExt<Long> saveTaskResultItem(@RequestBody TaskResultItemSaveBo bo);

    /**
     * 更新task_result_item中指定id的item_result字段
     */
    @PostMapping("/result/updateItem.do")
    WebResultExt<String> updateTaskResultItem(@RequestBody TaskResultItemUpdateBo bo);

    /**
     * 保存任务结果
     */
    @PostMapping("/result/save.do")
    WebResultExt<Integer> saveTaskResult(@RequestBody TaskResultSaveBo bo);

}
