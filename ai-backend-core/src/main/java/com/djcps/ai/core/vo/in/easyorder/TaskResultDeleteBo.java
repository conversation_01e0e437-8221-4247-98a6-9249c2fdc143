package com.djcps.ai.core.vo.in.easyorder;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 任务结果删除参数
 *
 * <AUTHOR> Assistant
 * @create 2025-08-05
 */
@Data
public class TaskResultDeleteBo {
    
    @NotBlank(message = "skey不能为空")
    private String skey;
    
    @NotNull(message = "taskId不能为空")
    private Long taskId;
    
    @NotBlank(message = "dataDate不能为空")
    private String dataDate;
    
    @NotBlank(message = "cycleType不能为空")
    private String cycleType;
    
    @NotBlank(message = "supplierId不能为空")
    private String supplierId;
}
