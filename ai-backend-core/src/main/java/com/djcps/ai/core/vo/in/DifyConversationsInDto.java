 package com.djcps.ai.core.vo.in;

 import lombok.Data;
 import lombok.EqualsAndHashCode;

 import jakarta.validation.constraints.Max;
 import jakarta.validation.constraints.Min;

 /**
* 类 DifyConversationsDto.java 描述: TODO
*
* @author: yeb 2024/7/15 上午9:48
**/
@Data
@EqualsAndHashCode(callSuper = true)
public class DifyConversationsInDto extends DifyUserInDto {
    /** 当前页最后面一条记录的 ID，默认 null */
    private String last_id;
    /** 一次请求返回多少条记录 */
     private int limit = 20;
     private String role;
}
