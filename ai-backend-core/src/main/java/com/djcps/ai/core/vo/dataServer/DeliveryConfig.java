package com.djcps.ai.core.vo.dataServer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;

@Data
public class DeliveryConfig {
//    {"supplier_id":"563","type":2,"hour":0,"postpone_close":1,"allow_count":200}
    public static DeliveryConfig getDefaultConfig(String supplierId, String skey) {
        DeliveryConfig config = new DeliveryConfig();
        //dayType 2 ，hourNum 0 ，orderDelay 1
        config.setSupplierId(supplierId);
        config.setDayType(2);
        config.setSkey(skey);
        config.setHourNum(0);
        config.setOrderDelay(1);
        config.setAllowMissingNum(200);
        return config;
    }
    public static void main(String args[]) throws JsonProcessingException {
        DeliveryConfig defaultConfig = getDefaultConfig("11", "22");
        ObjectMapper mapper = new ObjectMapper();
        System.out.println(defaultConfig);
        System.out.println(mapper.writeValueAsString(defaultConfig));
    }
    /**
     * 联合/供应商id
     */
    private String supplierId;
    private String skey;


    /**
     * 交付时效统计-日类型：1-当日，2-次日，默认选中次日
     */
    private Integer dayType;

    /**
     * 交付时效统计-小时数，默认选中0
     */
    private Integer hourNum;

    /**
     * 特殊逻辑-延单交期修正：0-否,1-是
     */
    private Integer orderDelay;

    /**
     * 特殊逻辑-允许送达少数片数
     */
    private Integer allowMissingNum;
}
