package com.djcps.ai.core.vo.in;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类 DifyMessageDto.java 描述: 获取会话历史消息
 *
 * @author: yeb 2024/7/15 下午1:00
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class DifyMessageInDto extends DifyUserInDto {

    /** 会话id */
    private String conversation_id;

    /** 当前页第一条聊天记录的 ID，默认 null */
    private String first_id;
    /** 一次请求返回多少条聊天记录，默认 20 条。 */
    private int    limit = 20;
}
