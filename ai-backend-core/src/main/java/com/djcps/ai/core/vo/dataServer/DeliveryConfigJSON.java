package com.djcps.ai.core.vo.dataServer;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class DeliveryConfigJSON {
    /**
     * 联合/供应商id
     */
    @JsonProperty("supplier_id")
    private String supplier_id;
    @JsonProperty("skey")
    private String skey;


    /**
     * 交付时效统计-日类型：1-当日，2-次日，默认选中次日
     */
    @JsonProperty("type")
    private Integer type;

    /**
     * 交付时效统计-小时数，默认选中0
     */
    @JsonProperty("hour")
    private Integer hour;

    /**
     * 特殊逻辑-延单交期修正：0-否,1-是
     */
    @JsonProperty("postpone_close")
    private Integer postpone_close;

    /**
     * 特殊逻辑-允许送达少数片数
     */
    @JsonProperty("allow_count")
    private Integer allow_count;
}
