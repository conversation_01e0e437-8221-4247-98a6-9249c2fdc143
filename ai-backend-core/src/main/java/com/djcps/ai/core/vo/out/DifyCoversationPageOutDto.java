package com.djcps.ai.core.vo.out;

import lombok.Data;
import net.sf.json.JSONObject;

import java.util.List;
import java.util.Map;

/**
 * 类 DifyCoversationPageDto.java 描述: TODO
 *
 * @author: yeb 2024/7/15 上午9:51
 **/
@Data
public class DifyCoversationPageOutDto {

    /**
     * data (array[object]) 会话列表 id (string) 会话 ID name (string) 会话名称，默认由大语言模型生成。 inputs (array[object]) 用户输入参数。
     * introduction (string) 开场白 created_at (timestamp) 创建时间 has_more (bool) limit (int) 返回条数，若传入超过系统限制，返回系统限制数量
     */

    private int          limit;

    private boolean      has_more;

    private boolean      has_add = false;

    private List<DataVo> data;

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public boolean isHas_more() {
        return has_more;
    }

    public void setHas_more(boolean has_more) {
        this.has_more = has_more;
    }

    public List<DataVo> getData() {
        return data;
    }

    public void setData(List<DataVo> data) {
        this.data = data;
    }

    public boolean isHas_add() {
        if(this.getData()==null || data.isEmpty() || data.size()<20){
            has_add=true;
        }
        return has_add;
    }

    public void setHas_add(boolean has_add) {
        this.has_add = has_add;
    }

    public static class DataVo {

        /**
         * id : 10799fb8-64f7-4296-bbf7-b42bfbe0ae54 name : New chat inputs : {"book":"book","myName":"Lucy"} status :
         * normal created_at : 1679667915
         */

        private String              id;
        private String              name;
        private Map<String, Object> inputs;
        private String              status;
        private int                 created_at;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Map<String, Object> getInputs() {
            return inputs;
        }

        public void setInputs(Map<String, Object> inputs) {
            this.inputs = inputs;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public int getCreated_at() {
            return created_at;
        }

        public void setCreated_at(int created_at) {
            this.created_at = created_at;
        }
    }
}
