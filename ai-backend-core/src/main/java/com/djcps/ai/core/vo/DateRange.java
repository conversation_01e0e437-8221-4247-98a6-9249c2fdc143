package com.djcps.ai.core.vo;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;

@Data
@NoArgsConstructor
public class DateRange {
    private LocalDateTime start;
    private LocalDateTime end;

    private static final String WEEK_SHOW_FORMAT = "{}（{}~{}）";
    public static String getDateDate(String cycle, LocalDateTime start) {
        if (StrUtil.equalsIgnoreCase(cycle, "day")) {
            return start.toLocalDate().format(DateTimeFormatter.ISO_LOCAL_DATE);
        }else if (StrUtil.equalsIgnoreCase(cycle, "week")) {
            String week = start.toLocalDate().format(DateTimeFormatter.ofPattern("yyyy-ww"));
            LocalDateTime localDateTime = start.plusDays(6);
            return StrUtil.format(WEEK_SHOW_FORMAT, week, start.toLocalDate().format(DateTimeFormatter.ofPattern("MM-dd")),localDateTime.toLocalDate().format(DateTimeFormatter.ofPattern("MM-dd")));
        }else  if (StrUtil.equalsIgnoreCase(cycle, "month")) {
            return  start.toLocalDate().format(DateTimeFormatter.ofPattern("yyyy-MM"));
        }
        return "0";
    }
    public static DateRange day(LocalDateTime day) {
        DateRange range = new DateRange();
        range.setStart(day.toLocalDate().atStartOfDay());
        range.setEnd(day.toLocalDate().atTime(23, 59, 59));
        return range;
    }

    public static DateRange get(int offset,String cycle) {
        if (StrUtil.equalsIgnoreCase(cycle, "day")) {
            return day(offset);
        }else if (StrUtil.equalsIgnoreCase(cycle, "week")) {
            return week(offset);
        }else  if (StrUtil.equalsIgnoreCase(cycle, "month")) {
            return month(offset);
        }
        return lastDay();
    }
    public static DateRange lastDay() {
        return day(0);
    }
    public static DateRange day(int i) {
        return day( LocalDateTime.now().minusDays(i+1));
    }
    public static DateRange lastWeek() {
        return week(0);
    }
    public static DateRange week(int offset) {
        offset = offset + 1;
        LocalDateTime now = LocalDateTime.now();
        // 获取上周一的日期
        LocalDateTime lastMonday = now.minusWeeks(offset).with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        // 获取上周日的日期
        LocalDateTime lastSunday = lastMonday.plusDays(6);

        DateRange range = new DateRange();
        range.setStart(lastMonday.toLocalDate().atStartOfDay());
        range.setEnd(lastSunday.toLocalDate().atTime(23, 59, 59));
        return range;
    }

    public static DateRange lastMonth() {
        return month(0);
    }
    public static DateRange month(int offset) {
        offset = offset + 1;
        LocalDateTime now = LocalDateTime.now();
        // 获取上个月的第一天
        LocalDateTime firstDayOfLastMonth = now.minusMonths(offset).with(TemporalAdjusters.firstDayOfMonth());
        // 获取上个月的最后一天
        LocalDateTime lastDayOfLastMonth = now.minusMonths(offset).with(TemporalAdjusters.lastDayOfMonth());

        DateRange range = new DateRange();
        range.setStart(firstDayOfLastMonth.toLocalDate().atStartOfDay());
        range.setEnd(lastDayOfLastMonth.toLocalDate().atTime(23, 59, 59));
        return range;
    }
    public static void main(String args[]){
        System.out.println(get(0,"day"));
        System.out.println(get(1,"day"));
        System.out.println(get(2,"day"));

        System.out.println(get(0,"week"));
        System.out.println(get(1,"week"));
        System.out.println(get(2,"week"));

        System.out.println(get(0,"month"));
        System.out.println(get(1,"month"));
        System.out.println(get(2,"month"));



        System.out.println(getDateDate("day",LocalDateTime.now()));
        System.out.println(getDateDate("week",LocalDateTime.now().minusDays(2)));
        System.out.println(getDateDate("month",LocalDateTime.now()));
    }
}
