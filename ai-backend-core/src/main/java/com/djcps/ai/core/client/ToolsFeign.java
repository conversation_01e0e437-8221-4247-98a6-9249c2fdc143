package com.djcps.ai.core.client;

import com.djcps.ai.core.vo.ToolsFeignParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * feign地址
 *
 */
@FeignClient(name = "toolsFeignClient", url = "${client.url.tools}",path = "/djai-tool-starter")
public interface ToolsFeign {

    /**
     * 获取丢单分析影响指标
     */
    @PostMapping("/mcp/api/getMetricsNoAuth.do")
    String  getMetrics(@RequestBody ToolsFeignParam toolsFeignParam);


}
