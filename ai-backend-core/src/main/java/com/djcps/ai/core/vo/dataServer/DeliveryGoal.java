package com.djcps.ai.core.vo.dataServer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;

@Data
public class DeliveryGoal {
    public static DeliveryGoal getDefaultGoal(String supplierId) {
        DeliveryGoal goal = new DeliveryGoal();
        //dayType 2 ，hourNum 0 ，orderDelay 1
        goal.setSupplierId(supplierId);
        return goal;
    }
    public static void main(String args[]) throws JsonProcessingException {
        DeliveryGoal defaultGoal = getDefaultGoal("11");
        ObjectMapper mapper = new ObjectMapper();
        System.out.println(defaultGoal);
        System.out.println(mapper.writeValueAsString(defaultGoal));
    }
    /**
     * 联合/供应商id
     */
    private String supplierId;
    private String skey;


    private Double out24Hours;
    private Double delivery24Hours;

}
