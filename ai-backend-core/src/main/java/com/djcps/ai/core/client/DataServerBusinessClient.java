package com.djcps.ai.core.client;

import com.djcps.ai.common.result.WebResultExt;
import com.djcps.ai.config.feign.DataServerFeignRequestInterceptor;
import com.djcps.ai.core.vo.DeliverAreaVo;
import com.djcps.ai.core.vo.DeliverArriveRate;
import com.djcps.ai.core.vo.DeliverOutRate;
import com.djcps.ai.core.vo.dataServer.DeliverOrderStatusVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "dataServerBusiness", url = "${dataServer.url}", configuration = DataServerFeignRequestInterceptor.class)
public interface DataServerBusinessClient {

    @PostMapping(value = "/al/deliver/no_order/detail", consumes = "application/json", produces = "application/json")
    String deliverOrderStatus(@RequestBody DeliverOrderStatusVo statusVo);

    @PostMapping(value = "/al/report/delivery/first/order_no", consumes = "application/json", produces = "application/json")
    String firstOrderNo(@RequestBody DeliverOrderStatusVo statusVo);

    @PostMapping(value = "/ybill/deliver/all/out", consumes = "application/json", produces = "application/json")
    WebResultExt<List<DeliverOutRate>> deliverAllOut(@RequestBody DeliverOrderStatusVo statusVo);
    @PostMapping(value = "/ybill/deliver/all/arrive", consumes = "application/json", produces = "application/json")
    WebResultExt<List<DeliverArriveRate>> deliverAllArrive(@RequestBody DeliverOrderStatusVo statusVo);

    @PostMapping(value = "/al/report/delivery/cycle", consumes = "application/json", produces = "application/json")
    String deliverCycle(@RequestBody DeliverOrderStatusVo statusVo);
    @PostMapping(value = "/al/report/delivery/cycle/first/arrive", consumes = "application/json", produces = "application/json")
    String firstArrive(@RequestBody DeliverOrderStatusVo statusVo);
    @PostMapping(value = "/al/report/delivery/area", consumes = "application/json", produces = "application/json")
    WebResultExt<List<DeliverAreaVo>> deliverArea(@RequestBody DeliverOrderStatusVo statusVo);


}
