package com.djcps.ai.core.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class SendMsgVo {
    /**
     * {
     * "event": "message",
     * "task_id": "3e1764bf-b247-4a3b-9fab-9950360aed38",
     * "id": "490c3ed0-f248-46dd-b4d4-39638fbb8757",
     * "message_id": "490c3ed0-f248-46dd-b4d4-39638fbb8757",
     * "conversation_id": "9e8907a9-e933-4c0f-9708-255e76b8a7f2",
     * "mode": "advanced-chat",
     * "answer": "",
     * "metadata": {
     * "usage": {
     * "prompt_tokens": 5687,
     * "prompt_unit_price": "3.00",
     * "prompt_price_unit": "0.000001",
     * "prompt_price": "0.0170610",
     * "completion_tokens": 184,
     * "completion_unit_price": "15.00",
     * "completion_price_unit": "0.000001",
     * "completion_price": "0.0027600",
     * "total_tokens": 5871,
     * "total_price": "0.0198210",
     * "currency": "USD",
     * "latency": 9.753148856922053
     * }
     * },
     * "created_at": 1720597597
     * }
     */

    private String query;
    private String user;
    private String answer;
    @JsonProperty("conversation_id")
    private String conversationId;
    @JsonProperty("message_id")
    private String messageId;
}
