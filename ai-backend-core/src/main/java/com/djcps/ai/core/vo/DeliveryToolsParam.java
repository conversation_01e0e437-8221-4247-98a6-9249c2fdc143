package com.djcps.ai.core.vo;

import com.djcps.ai.core.vo.dataServer.DeliveryConfigJSON;
import com.djcps.ai.core.vo.dataServer.DeliveryGoal;
import lombok.Data;

import java.util.List;

@Data
public class DeliveryToolsParam {
    private String skey;
    private String union_supplier_id;
    private String mainSupplierId;
    private List<String> supplierIdList;
    private List<DeliveryConfigJSON> config_list;
    private DeliveryGoal goal;
}
