package com.djcps.ai.core.vo.in.favorite;

import com.djcps.ai.core.vo.ChartConfigResult;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Data
public class AddFavoriteDto {
    /**
     * 展示的名字
     */
    @NotNull(message = "名字不能为空")
    @NotBlank
    private String name;
    @NotNull(message = "会话id不能为空")
    @NotBlank
    private String conversationId;
    @NotNull(message = "消息id不能为空")
    @NotBlank
    private String messageId;
    /**
     * 图表配置
     */
    private ChartConfigResult chartConfig;
}
