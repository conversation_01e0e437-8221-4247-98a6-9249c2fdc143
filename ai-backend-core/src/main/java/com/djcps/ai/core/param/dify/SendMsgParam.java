package com.djcps.ai.core.param.dify;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

@NoArgsConstructor
@Data
public class SendMsgParam {
    private Map<String, Object> inputs = new HashMap<>();
    @JsonProperty("response_mode")
    private String responseMode = "blocking";

    private String query;
    @JsonProperty("conversation_id")
    private String conversationId;
    private String user;

}
