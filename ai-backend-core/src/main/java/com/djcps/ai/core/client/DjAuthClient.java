package com.djcps.ai.core.client;

import com.djcps.ai.common.result.WebResultExt;
import com.djcps.ai.core.param.AuthParam;
import com.djcps.ai.core.param.AuthResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "djauth", url = "${client.url.djauth}", path = "/djauth/")
public interface DjAuthClient {
    @PostMapping(value = "account/getUserCache.do", consumes = "application/json", produces = "application/json")
    WebResultExt<AuthResponse> getUserCache(@RequestBody AuthParam param);

}
