package com.djcps.ai.core.constants;


/**
 * 类 DifyConstants.java 描述: dify接口
 *
 * @author: yeb 2024/7/12 下午4:01
 **/

public  class DifyConstants {

    /** 获取应用配置信息,用于进入页面一开始，获取功能开关、输入参数名称、类型及默认值等使用。 **/
    public static final DifyRoute APPLICATION         = new DifyRoute("GET", "/parameters?user=%s");

    /** 发送对话消息,创建会话消息 **/
    public static final DifyRoute CREATE_MESSAGE      = new DifyRoute("POST", "/chat-messages");

    /** 消息反馈,点赞 like, 点踩 dislike, 撤销点赞 null **/
    public static final DifyRoute FEEDBACK            = new DifyRoute("POST", "/messages/%s/feedbacks");

    /** 获取下一轮建议问题列表 **/
    public static final DifyRoute SUGGESTED_MESSAGE   = new DifyRoute("GET", "/messages/%s/suggested?user=%s");

    /** 获取会话历史消息 **/
    public static final DifyRoute ALL_MESSAGES        = new DifyRoute("GET", "/messages?user=%s&conversation_id=%s");

    /** 获取当前用户的会话列表，默认返回最近的 20 条 **/
    public static final DifyRoute ALL_CONVERSATIONS   = new DifyRoute("GET", "/conversations?user=%s");

    /** 删除会话 %s:conversation_id **/
    public static final DifyRoute DEL_CONVERSATION    = new DifyRoute("DELETE", "/conversations/%s");

    /** 会话重命名 **/
    public static final DifyRoute RENAME_CONVERSATION = new DifyRoute("POST", "/conversations/%s/name");

    /** 停止响应,仅支持流式模式,task_id **/
    public static final DifyRoute STOP_CONVERSATION   = new DifyRoute("POST", "/chat-messages/%s/stop");


}
