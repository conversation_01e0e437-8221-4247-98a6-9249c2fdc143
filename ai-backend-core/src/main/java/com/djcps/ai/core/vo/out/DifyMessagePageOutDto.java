package com.djcps.ai.core.vo.out;

import java.util.List;

/**
 * 类 DifyMessagePageOutDto.java 描述: 消息详情
 *
 * @author: yeb 2024/7/15 下午1:03
 **/

public class DifyMessagePageOutDto {

    /**
     * data (array[object]) 消息列表<br>
     * id (string) 消息 ID<br>
     * conversation_id (string) 会话 ID<br>
     * inputs (array[object]) 用户输入参数。<br>
     * query (string) 用户输入 / 提问内容。<br>
     * message_files (array[object]) 消息文件<br>
     * id (string) ID<br>
     * type (string) 文件类型，image 图片<br>
     * url (string) 预览图片地址<br>
     * belongs_to (string) 文件归属方，user 或 assistant<br>
     * answer (string) 回答消息内容<br>
     * created_at (timestamp) 创建时间<br>
     * feedback (object) 反馈信息<br>
     * rating (string) 点赞 like / 点踩 dislike<br>
     * retriever_resources (array[RetrieverResource]) 引用和归属分段列表<br>
     * has_more (bool) 是否存在下一页<br>
     * limit (int) 返回条数，若传入超过系统限制，返回系统限制数量<br>
     */

    private int          limit;
    private boolean      has_more;
    private List<DataVo> data;

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public boolean isHas_more() {
        return has_more;
    }

    public void setHas_more(boolean has_more) {
        this.has_more = has_more;
    }

    public List<DataVo> getData() {
        return data;
    }

    public void setData(List<DataVo> data) {
        this.data = data;
    }

}
