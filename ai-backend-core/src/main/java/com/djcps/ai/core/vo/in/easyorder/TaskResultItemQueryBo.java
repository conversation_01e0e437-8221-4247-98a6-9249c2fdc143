package com.djcps.djeasyorder.smartbrief.model.bo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 任务结果子项查询参数
 *
 * <AUTHOR> Assistant
 * @create 2025-08-05
 */
@Data
public class TaskResultItemQueryBo {
    
    @NotNull(message = "taskId不能为空")
    private Long taskId;
    
    @NotBlank(message = "batchNo不能为空")
    private String batchNo;
}
