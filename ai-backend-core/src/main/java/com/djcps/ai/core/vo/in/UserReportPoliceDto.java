package com.djcps.ai.core.vo.in;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Data
public class UserReportPoliceDto  {
    /**
     * 举报类型
     */
    @NotEmpty(message = "举报类型不能为空")
    private String itemName;
    /**
     * 所属ai功能, 即之前放在header中的aiFunction;
     */
    @NotEmpty(message = "所属ai功能不能为空")
    private String functionName;
    /**
     * 用户填写内容
     */
    private String remark;
    /**
     * 会话id
     */
    private String conversationId;
    /**
     * 消息id
     */
    private String messageId;
}
