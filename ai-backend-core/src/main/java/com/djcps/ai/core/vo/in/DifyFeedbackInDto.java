package com.djcps.ai.core.vo.in;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 类 DifyFeedbackVo.java 描述: TODO
 *
 * @author: yeb 2024/7/12 下午4:30
 **/
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class DifyFeedbackInDto extends DifyUserInDto {

    /** DifyFeedbackEnum 点赞 like, 点踩 dislike, 撤销点赞 null **/
    private String rating;

}
