package com.djcps.djeasyorder.smartbrief.model.bo;

import com.djcps.djeasyorder.common.model.bo.PageModel;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class TaskResultSearchBo extends PageModel {
    /**
     * 搜索关键字
     */
    @NotBlank
    private String skey;
    @NotBlank
    private String supplierId;
    @NotBlank
    private String orgId;
    @NotBlank
    private String role;
    /**
     * 前端忽略
     */
    private String userId;
    /**
     * 前端忽略
     */
    private String batchNo;
    /**
     * 前端忽略
     */
    private String dataDate;

    /**
     * 周期类型：day, week, month
     */
    @NotBlank
    private String cycleType;
    /**
     * 业务类型 交付:deliver,销售 sale
     */
    @NotBlank
    private String bizType;
}
