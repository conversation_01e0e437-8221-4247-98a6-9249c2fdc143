package com.djcps.ai.core.client;

import com.djcps.ai.common.result.WebResultExt;
import com.djcps.ai.config.feign.DataServerFeignRequestInterceptor;
import com.djcps.ai.core.vo.dataServer.DeliverOrderStatusVo;
import com.djcps.ai.core.vo.dataServer.ExecSQL;
import com.djcps.ai.common.vo.PageResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "dataServer", url = "${dataServer.url}", path = "/ai/", configuration = DataServerFeignRequestInterceptor.class)
public interface DataServerClient {

    /**
     * 执行SQL
     *
     * @param param
     * @return
     */
    @PostMapping(value = "all_sql", consumes = "application/json", produces = "application/json")
    WebResultExt<PageResult<Object>> execSQL(@RequestBody ExecSQL param);
    @PostMapping(value = "/ybill/deliver/order/status", consumes = "application/json", produces = "application/json")
    WebResultExt<PageResult<Object>> deliverOrderStatus(@RequestBody DeliverOrderStatusVo statusVo);


}
