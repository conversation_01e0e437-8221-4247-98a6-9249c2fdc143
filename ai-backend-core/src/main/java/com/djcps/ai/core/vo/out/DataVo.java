package com.djcps.ai.core.vo.out;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DataVo {

    private String id;
    private String conversation_id;
    private Map<String, Object> inputs;
    private String query;
    private String answer;
    private boolean favorite;
    private Long favoriteId;
    private Object feedback;
    private int created_at;
    private List<?> message_files;
    private Object response;
}
