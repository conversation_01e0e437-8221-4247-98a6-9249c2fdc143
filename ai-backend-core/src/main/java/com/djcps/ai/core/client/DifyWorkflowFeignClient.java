package com.djcps.ai.core.client;

import com.djcps.ai.core.param.dify.DifyWordflowRequestParam;
import com.djcps.ai.core.vo.DifyWorkflowResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "difyWorkflow", url = "${dify.url}", path = "/v1/")
public interface DifyWorkflowFeignClient {


    /**
     * 发送消息
     * 
     * @param param
     * @return
     */
    @PostMapping(value = "workflows/run", consumes = "application/json", produces = "application/json")
    DifyWorkflowResponse run(@RequestBody DifyWordflowRequestParam param, @RequestHeader ("Authorization") String authorization);


}
