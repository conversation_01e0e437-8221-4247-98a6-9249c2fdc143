package com.djcps.ai.core.client;

import com.djcps.ai.config.feign.DifyFeignRequestInterceptor;
import com.djcps.ai.core.param.dify.SendMsgParam;
import com.djcps.ai.core.vo.SendMsgVo;
import com.djcps.ai.core.vo.in.*;
import com.djcps.ai.core.vo.out.DifyCoversationPageOutDto;
import com.djcps.ai.core.vo.out.DifyEditOutDto;
import com.djcps.ai.core.vo.out.DifyMessagePageOutDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

@FeignClient(name = "difyClient", url = "${dify.url}", path = "/v1/", configuration = DifyFeignRequestInterceptor.class)
public interface DifyFeignClient {

    //FIXME 是否需要加工返回值

    /**
     * 发送消息
     * 
     * @param param
     * @return
     */
    @PostMapping(value = "chat-messages", consumes = "application/json", produces = "application/json")
    SendMsgVo sendMessage(@RequestBody SendMsgParam param);

    /**
     * 重命名会话
     * 
     * @param inVo
     * @return
     */
    @PostMapping(value = "conversations/{conversation_id}/name", consumes = "application/json", produces = "application/json")
    DifyEditOutDto renameConversations(@PathVariable(name = "conversation_id") String conversation_id,
                                       @RequestBody DifyConversationInDto inVo);

    /**
     * 分页获取用户会话列表，默认一页20条
     *
     * <AUTHOR>
     * @return com.djcps.ai.core.vo.out.DifyCoversationPageDto
     * @date 2024/7/15
     */
    @GetMapping(value = "conversations", produces = "application/json")
    DifyCoversationPageOutDto getAllConversations(@SpringQueryMap DifyConversationsInDto dto);

    /**
     * 消息反馈
     * 
     * @param inVo
     * <AUTHOR>
     * @return com.djcps.ai.core.vo.out.DifyEditOutDto
     * @date 2024/7/15
     */
    @PostMapping(value = "messages/{message_id}/feedbacks", consumes = "application/json", produces = "application/json")
    DifyEditOutDto messageFeedbacks(@PathVariable(name = "message_id") String message_id,@RequestBody DifyFeedbackInDto inVo);

    /**
     * 获取会话历史消息,倒序
     * @param 
     * <AUTHOR>
     * @return com.djcps.ai.core.vo.out.DifyEditOutDto
     * @date 2024/7/15
     */
    @GetMapping(value = "messages", produces = "application/json")
    DifyMessagePageOutDto getAllMessage(@SpringQueryMap DifyMessageInDto inVo);
    /**
     * 删除会话
     * @param
     * <AUTHOR>
     * @return com.djcps.ai.core.vo.out.DifyEditOutDto
     * @date 2024/7/15
     */
    @DeleteMapping(value = "conversations/{conversation_id}", consumes = "application/json", produces = "application/json")
    DifyEditOutDto deleteConversation(@PathVariable(name = "conversation_id") String conversation_id, @RequestBody DifyUserInDto inVo);

}
