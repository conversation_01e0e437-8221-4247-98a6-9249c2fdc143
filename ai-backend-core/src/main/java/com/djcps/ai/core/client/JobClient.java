package com.djcps.ai.core.client;

import com.djcps.ai.core.vo.CreateCronJob;
import com.djcps.ai.core.vo.JobVo;
import com.djcps.ai.core.vo.UpdateParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "djjob",
        url = "${client.url.djjob}",
        path = "/djjob/")
public interface JobClient {

    /**
     * 创建定时任务
     *
     * @param jobParam
     * @return
     */
    @PostMapping(value = "timedTask/createCronTask.do", consumes = "application/json", produces = "application/json")
    JobVo createCronTask(@RequestBody CreateCronJob jobParam);

    @PostMapping(value = "timedTask/delJob.do", consumes = "application/json", produces = "application/json")
    String delJob(@RequestParam int id);

    @PostMapping(value = "timedTask/updateCronTask.do", consumes = "application/json", produces = "application/json")
    JobVo updateCronTask(@RequestBody UpdateParam updateParam);


}
