package com.djcps.ai.core.vo.out;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 类 DifyConversationDto.java 描述: 重命名后返回
 *
 * @author: yeb 2024/7/15 上午9:22
 **/
@Data
public class DifyConversationOutDto {

    /** 会话 ID */
    private String       id;
    /** 会话名称 */
    private String       name;
    /** 用户输入参数。 */
    private List<String> inputs;
    /** 开场白 */
    private String       introduction;
    /** 创建时间 */
    private Date         created_at;
}
