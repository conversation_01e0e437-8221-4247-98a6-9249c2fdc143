
package com.djcps.ai.config.feign;

import cn.hutool.core.util.StrUtil;
import com.djcps.ai.common.threadlocal.AgentThreadLocalUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;

/**
 * 类FeignRequestInterceptor.java的实现描述：增加请求头，需要根据包配置
 * 
 * <AUTHOR> 2022年7月7日 上午9:19:19
 */
//单独使用,不能加@Configuration注解否则变成全局的所有请求都会走这个拦截器
//只能在FeignClient中指明 configuration = DifyFeignRequestInterceptor.class
public class DifyFeignRequestInterceptor implements RequestInterceptor, EnvironmentAware {
    private Environment environment;

    @Override
    public void setEnvironment(Environment environment) {
        this.environment = environment;
    }

    public String getDifyAppKey(String aiFunction) {
        return environment.getProperty("dify.key." + aiFunction);
    }

    private static final String DIFY_APP_KEY_TEMP = "Bearer {}";
    private static final String HEADER_AUTH_KEY = "Authorization";

    @Override
    public void apply(RequestTemplate template) {
        template.header(HEADER_AUTH_KEY, getDifyAppKey());
    }

    private String getDifyAppKey() {
        String aiFunction = AgentThreadLocalUtil.getFunctionName();
        String appKey = getDifyAppKey(aiFunction);
        return StrUtil.format(DIFY_APP_KEY_TEMP, appKey);
    }

}
