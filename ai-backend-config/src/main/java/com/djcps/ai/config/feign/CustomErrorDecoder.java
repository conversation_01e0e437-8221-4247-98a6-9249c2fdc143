package com.djcps.ai.config.feign;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.djcps.ai.common.exception.ParamException;
import feign.Response;
import feign.codec.ErrorDecoder;
import org.springframework.http.HttpStatus;

public class CustomErrorDecoder implements ErrorDecoder {

    private final ErrorDecoder defaultErrorDecoder = new Default();

    @Override
    public Exception decode(String methodKey, Response response) {
        HttpStatus status = HttpStatus.valueOf(response.status());
        switch (status) {
            case BAD_REQUEST:
            case NOT_FOUND:
                Response.Body body = response.body();
                JSONObject jsonObject = JSONUtil.parseObj(body.toString());
                return new ParamException(jsonObject.get("message").toString());
            default:
                return defaultErrorDecoder.decode(methodKey, response);
        }
    }
}