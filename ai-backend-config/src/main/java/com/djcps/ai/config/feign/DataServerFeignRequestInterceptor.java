
package com.djcps.ai.config.feign;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.beans.factory.annotation.Value;

/**
 * 类FeignRequestInterceptor.java的实现描述：增加请求头，需要根据包配置
 *
 * <AUTHOR> 2022年7月7日 上午9:19:19
 */
//单独使用,不能加@Configuration注解否则变成全局的所有请求都会走这个拦截器
//只能在FeignClient中指明configuration = DataServerFeignRequestInterceptor.class
public class DataServerFeignRequestInterceptor implements RequestInterceptor {


    @Value("${dataServer.app_key}")
    private String dataServerAppKey;
    private static final String HEADER_AUTH_KEY = "appKey";

    @Override
    public void apply(RequestTemplate template) {
        template.header(HEADER_AUTH_KEY, dataServerAppKey);
    }

}
