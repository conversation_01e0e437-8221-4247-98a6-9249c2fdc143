package com.djcps.ai.config.security;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.web.SecurityFilterChain;

/**
 *
 * 类SecurityConfig.java的实现描述：actuator权限
 *
 * <AUTHOR> 2022年7月27日 上午9:46:37
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig {

	// 自定义配置URL资源的权限控制
	@Bean
	public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
		http.csrf(csrf -> csrf.disable())
			.authorizeHttpRequests(authz -> authz
				.requestMatchers("/actuator/info", "/actuator/health", "/actuator/shutdown").authenticated()
				.anyRequest().permitAll()
			)
			.httpBasic(httpBasic -> {});

		return http.build();
	}
}
