/*
 * Copyright 2018 huna.com All right reserved. This software is the confidential and proprietary information of huna.com
 * ("Confidential Information"). You shall not disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into with huna.com.
 */
package com.djcps.ai.config.env;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * 
 * 类EnvironmentExt.java的实现描述：环境
 * 
 * <AUTHOR> 2021年11月16日 下午10:34:45
 */
@Component
public class EnvironmentExt implements ApplicationContextAware {

	private ApplicationContext context = null;

	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		context = applicationContext;
	}

	/**
	 * 获取当前环境
	 * 
	 * @return
	 */
	public String getActiveProfile() {
		return context.getEnvironment().getActiveProfiles()[0];
	}

}
