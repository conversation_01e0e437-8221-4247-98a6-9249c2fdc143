<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>ai-backend</artifactId>
        <groupId>com.dongjing</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <groupId>com.djcps.ai</groupId>
    <artifactId>ai-backend-tools</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>ai-backend-tools</name>
    <description>ai-backend-tools</description>

    <properties>
        <java.version>17</java.version>
        <spring-ai.version>1.0.0</spring-ai.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-model-openai</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dongjing</groupId>
            <artifactId>ai-backend-core</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

    </dependencies>


</project>
