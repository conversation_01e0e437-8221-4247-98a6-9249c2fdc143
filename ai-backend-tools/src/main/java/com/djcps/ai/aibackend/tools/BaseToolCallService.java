package com.djcps.ai.aibackend.tools;

import cn.hutool.core.util.StrUtil;
import com.djcps.ai.aibackend.tools.vo.ToolCallParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;


@Slf4j
@Service
@RequiredArgsConstructor
public  class BaseToolCallService implements ToolCallService {

    @Override
    public String prompt(ToolCallParam runParam) {
        return "";
    }

    private final ChatClient chatClient;
    public ChatClient.ChatClientRequestSpec build(String systemPrompt, ToolCallParam runParam) {
        String prompt = systemPrompt;
        if (StrUtil.isEmptyIfStr(systemPrompt)) {
            prompt = prompt(runParam);
        }
        log.info("system prompt:<{}>,user prompt:<{}>", prompt, runParam.getBusinessPrompt());
        return chatClient.prompt(prompt)
                .user(runParam.getBusinessPrompt())
                .toolNames(runParam.getToolName());
    }

    @Override
    public Flux<String> stream(ToolCallParam runParam) {
        return stream(null, runParam);
    }

    @Override
    public Flux<String> stream(String systemPrompt, ToolCallParam runParam) {
        return build(systemPrompt,runParam).stream()
                .content();
    }



    @Override
    public String call(ToolCallParam runParam) {
        return  call(null, runParam);
    }

    @Override
    public String call(String systemPrompt, ToolCallParam runParam) {
        return build(systemPrompt, runParam).call().content();
    }

}
