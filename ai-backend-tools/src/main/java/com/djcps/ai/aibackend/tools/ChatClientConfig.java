package com.djcps.ai.aibackend.tools;

import com.djcps.ai.aibackend.tools.tools.DeliveryTools;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ChatClientConfig {

    @Bean
    public ChatClient chatClient(ChatClient.Builder chatClientBuilder) {
        return chatClientBuilder.build();
    }

    @Bean
    public ToolCallbackProvider tools(DeliveryTools deliveryTools) {
        return MethodToolCallbackProvider.builder().toolObjects(deliveryTools).build();
    }
}
