package com.djcps.ai.aibackend.tools;

import com.djcps.ai.aibackend.tools.vo.ToolCallParam;
import reactor.core.publisher.Flux;

public interface ToolCallService  {

    String prompt(ToolCallParam runParam);
    Flux<String> stream(ToolCallParam runParam);
    Flux<String> stream(String systemPrompt,ToolCallParam runParam);
    String call(ToolCallParam runParam);

    String call(String systemPrompt, ToolCallParam runParam);
}
