package com.djcps.ai.aibackend.tools;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

@Service
@RequiredArgsConstructor
@Slf4j
public class GenerateService {
    private final ChatClient chatClient;

    public Flux<String> generateStream(String systemPrompt, String customPrompt) {
        log.info("system prompt:<{}>,user prompt:<{}>",systemPrompt,customPrompt);
        return chatClient
                .prompt(systemPrompt)
                .user(customPrompt)
                .stream().content();
    }

}
