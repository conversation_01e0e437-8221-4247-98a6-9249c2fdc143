package com.djcps.ai.aibackend.tools;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.djcps.ai.aibackend.tools.vo.ToolCallParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.stereotype.Service;


@Service("text")
@Slf4j
public class TextToolCallService extends BaseToolCallService {


    public TextToolCallService(ChatClient chatClient) {
        super(chatClient);
    }

    @Override
    public String prompt(ToolCallParam runParam) {
        String format = StrUtil.format(AiConstant.date_template, DateUtil.today(), runParam.getCycleType());
        String paramJson = runParam.getParamJson();
        if (StrUtil.isNotBlank(paramJson)) {
            format += ".使用以下参数:" + paramJson;
        }
        return format;
    }

}
