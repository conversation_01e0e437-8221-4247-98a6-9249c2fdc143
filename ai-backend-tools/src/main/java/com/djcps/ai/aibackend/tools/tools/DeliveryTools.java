package com.djcps.ai.aibackend.tools.tools;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.djcps.ai.aibackend.tools.AiConstant;
import com.djcps.ai.common.result.WebResultExt;
import com.djcps.ai.core.client.DataServerBusinessClient;
import com.djcps.ai.core.client.EasyOrderFeign;
import com.djcps.ai.core.client.ToolsFeign;
import com.djcps.ai.core.vo.*;
import com.djcps.ai.core.vo.dataServer.DeliverOrderStatusVo;
import com.djcps.ai.core.vo.dataServer.DeliveryConfig;
import com.djcps.ai.core.vo.dataServer.DeliveryConfigJSON;
import com.djcps.ai.core.vo.dataServer.DeliveryGoal;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import static com.djcps.ai.core.constants.CommonConstants.FULL_DATE_TIME;

@Service("tools_delivery")
@RequiredArgsConstructor
@Slf4j
public class DeliveryTools {
    private final DataServerBusinessClient dataServerClient;
    private final ToolsFeign toolsFeign;
    private final EasyOrderFeign easyOrderFeign;
    private final static String resultRemark = "{}:{}";
    private final static DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private final static BigDecimal percent_10 = new BigDecimal(10);
    // 线程池用于异步执行
    private final Executor executor = Executors.newFixedThreadPool(10);

    /**
     * 根据当前时间范围和周期类型计算前一个周期的时间范围
     */
    private DateRange calculatePreviousPeriod(String cycleType, String startTime, String endTime) {
        try {
            LocalDateTime start = LocalDateTime.parse(startTime, formatter);
            LocalDateTime end = LocalDateTime.parse(endTime, formatter);

            // 计算当前周期的长度
            long periodLength;
            DateRange previousRange;

            switch (cycleType.toLowerCase()) {
                case "day":
                    periodLength = 1;
                    previousRange = new DateRange();
                    previousRange.setStart(start.minusDays(periodLength));
                    previousRange.setEnd(end.minusDays(periodLength));
                    break;
                case "week":
                    periodLength = 7;
                    previousRange = new DateRange();
                    previousRange.setStart(start.minusDays(periodLength));
                    previousRange.setEnd(end.minusDays(periodLength));
                    break;
                case "month":
                    previousRange = new DateRange();
                    previousRange.setStart(start.minusMonths(1));
                    previousRange.setEnd(end.minusMonths(1));
                    break;
                default:
                    // 默认按天处理
                    previousRange = new DateRange();
                    previousRange.setStart(start.minusDays(1));
                    previousRange.setEnd(end.minusDays(1));
                    break;
            }

            return previousRange;
        } catch (Exception e) {
            log.error("计算前一个周期时间范围失败", e);
            return null;
        }
    }


    @Tool(description = "参数-交付率定时任务")
    public String getDeliveryParam() {
        List<DeliveryToolsParam> list = new LinkedList<>();
        Map<String, Map<String, DeliveryConfig>> configMap = getConfigMap();
        getGoalMap();
        getPreGoalMap();

        //获取营销赋能的供应商,并组织返回参数
        List<SupplierInfoVo> supplierList = easyOrderFeign.getByEnergizeTypeCommon(new SupplierCondition()).fetchPageDataList();
        for (SupplierInfoVo infoVo : supplierList) {
            DeliveryToolsParam toolsParam = new DeliveryToolsParam();
            String supplierId = infoVo.getSupplierId();
            toolsParam.setSupplierIdList(CollUtil.newArrayList(supplierId));
            toolsParam.setSkey(supplierId);
            toolsParam.setUnion_supplier_id(supplierId);
            toolsParam.setMainSupplierId(supplierId);
            toolsParam.setConfig_list(CollUtil.newArrayList(getConfigValue(supplierId, configMap, supplierId)));
            list.add(toolsParam);
        }
        //获取所有联合供应商,并组织返回参数
        List<UnionSupplierInfoVo> unionSupplierList = easyOrderFeign.getAllUnionSupplierInfoCommon().fetchPageDataList();
        for (UnionSupplierInfoVo info : unionSupplierList) {
            DeliveryToolsParam toolsParam = new DeliveryToolsParam();
            //该接口中的id是联合供应商的id
            String unionSupplierId = info.getId();
            toolsParam.setUnion_supplier_id(unionSupplierId);
            toolsParam.setSkey(unionSupplierId);

            List<UnionSupplierInfoItemVo> itemList = info.getSupplierList();
            if (CollUtil.isNotEmpty(itemList)) {
                List<String> supplierIdList = new ArrayList<>();
                List<DeliveryConfigJSON> configList = new ArrayList<>();
                for (UnionSupplierInfoItemVo infoVo : itemList) {
                    String supplierId = infoVo.getSupplierId();
                    toolsParam.setMainSupplierId(infoVo.getSalesMain());
                    supplierIdList.add(supplierId);
                    configList.add(getConfigValue(supplierId, configMap, unionSupplierId));
                }
                toolsParam.setSupplierIdList(supplierIdList);
                toolsParam.setConfig_list(configList);
            }
            list.add(toolsParam);
        }
        String x = "[]";
        try {
            x = AiConstant.objectMapper.writeValueAsString(list);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        System.out.println(x);
        return x;
//        return """
//                [
//                 {
//                             "skey": "400",
//                             "supplierIdList": [
//                                 "400"
//                             ],
//                             "config_list": [
//                                 {
//                                     "supplier_id": "400",
//                                     "type": 2,
//                                     "hour": 0,
//                                     "postpone_close": 1,
//                                     "allow_count": 200
//                                 }
//                             ]
//                         }
//                ]
//                """;
    }

    private DeliveryConfigJSON getConfigValue(String supplierId, Map<String, Map<String, DeliveryConfig>> configMap, String skey) {
        DeliveryConfig defaultConfig = DeliveryConfig.getDefaultConfig(supplierId,skey);
        if (configMap.containsKey(supplierId)) {
           defaultConfig = configMap.get(supplierId).getOrDefault(skey, defaultConfig);
        }
        DeliveryConfigJSON configJSON = new DeliveryConfigJSON();
        configJSON.setSkey(skey);
        configJSON.setSupplier_id(supplierId);
        configJSON.setType(defaultConfig.getDayType());
        configJSON.setHour(defaultConfig.getHourNum());
        configJSON.setPostpone_close(defaultConfig.getOrderDelay());
        configJSON.setAllow_count(defaultConfig.getAllowMissingNum());
        return configJSON;
    }

    private Map<String, Map<String, DeliveryConfig>> getConfigMap() {
        //supplierId, skey, DeliveryConfig
        Map<String, Map<String, DeliveryConfig>> configMap = new HashMap<>();
        List<DeliveryConfig> unionSupplierList = easyOrderFeign.getAllConfigCommon().fetchPageDataList();
        for (DeliveryConfig info : unionSupplierList) {
            String supplierId = info.getSupplierId();
            Map<String, DeliveryConfig> item = configMap.getOrDefault(supplierId, new HashMap<>());
            item.put(info.getSkey(), info);
            configMap.put(supplierId, item);
        }
        return configMap;
    }

    Map<String, Map<String, DeliveryGoal>> currentMonthGoalMap = new HashMap<>();

    private void getGoalMap() {
        //supplierId, skey, DeliveryConfig
        YearMonth yearMonth = YearMonth.now().minusMonths(1);
        List<DeliveryGoal> currentMonthList = easyOrderFeign.getAllGoal(new GoalVo(yearMonth)).fetchPageDataList();
        for (DeliveryGoal info : currentMonthList) {
            String supplierId = info.getSupplierId();
            Map<String, DeliveryGoal> item = currentMonthGoalMap.getOrDefault(supplierId, new HashMap<>());
            item.put(info.getSkey(), info);
            currentMonthGoalMap.put(supplierId, item);
        }
    }

    Map<String, Map<String, DeliveryGoal>> preMonthGoalMap = new HashMap<>();

    private void getPreGoalMap() {
        //supplierId, skey, DeliveryConfig
        List<DeliveryGoal> previousMonthList = easyOrderFeign.getAllGoal(new GoalVo(YearMonth.now().minusMonths(2))).fetchPageDataList();
        for (DeliveryGoal info : previousMonthList) {
            String supplierId = info.getSupplierId();
            Map<String, DeliveryGoal> item = preMonthGoalMap.getOrDefault(supplierId, new HashMap<>());
            item.put(info.getSkey(), info);
            preMonthGoalMap.put(supplierId, item);
        }
    }

    private DeliveryGoal getGoalValue(String supplierId, String skey) {
        if (currentMonthGoalMap.containsKey(supplierId) && currentMonthGoalMap.get(supplierId).containsKey(skey)) {
            return  currentMonthGoalMap.get(supplierId).get(skey);
        }
        return null;
    }

    private DeliveryGoal getPreGoalValue(String supplierId, String skey) {
        if (preMonthGoalMap.containsKey(supplierId) && preMonthGoalMap.get(supplierId).containsKey(skey)) {
            return  preMonthGoalMap.get(supplierId).get(skey);
        }
        return null;
    }

    @Tool(description = "获取交付率相关数据")
    public List<String> delivery(@ToolParam(description = "供应商id列表") List<String> supplierIdList
            , @ToolParam(description = "配置列表") List<DeliveryConfigJSON> config_list
            , @ToolParam(description = "联合供应商id") String unioSupplierId
            , @ToolParam(description = "联合供应商主体id") String mainSupplierId
            , @ToolParam(description = "cycleType: 日 day, 周 week, 月 month") String cycleType
            , @ToolParam(description = "开始时间 YYYY-MM-DD HH:mm:ss格式") String startTime
            , @ToolParam(description = "结束时间 YYYY-MM-DD HH:mm:ss格式") String endTime) {
        log.debug("config_list value is  : {}", JSONUtil.toJsonPrettyStr(config_list));
        DeliverOrderStatusVo vo = new DeliverOrderStatusVo();
        vo.setStartTime(startTime);
        vo.setEndTime(endTime);
        vo.setSupplierList(supplierIdList);
        vo.setUnionSupplierId(unioSupplierId);
        vo.setConfigList(config_list);

        //将endTime转为LocalDateTime
        LocalDateTime parsed = LocalDateTime.parse(endTime, FULL_DATE_TIME);
        LocalDateTime start15 = DateRange.day(parsed.minusDays(14)).getStart();
        DeliverOrderStatusVo vo15 = new DeliverOrderStatusVo();
        vo15.setStartTime(start15.format(FULL_DATE_TIME));
        vo15.setEndTime(endTime);
        vo15.setSupplierList(supplierIdList);
        vo15.setUnionSupplierId(unioSupplierId);
        vo15.setConfigList(config_list);
        // 创建异步任务列表
        List<CompletableFuture<String>> futures = new ArrayList<>();

        // 异步调用 deliverOrderStatus
        CompletableFuture<String> deliverOrderStatusFuture = CompletableFuture.supplyAsync(() -> {
            try {
                String result = dataServerClient.deliverOrderStatus(vo15);
                log.debug("deliverOrderStatus unioSupplierId: {} cycleType: {} startTime: {} endTime: {} param: {} result: {}",unioSupplierId, cycleType,startTime, endTime, JSONUtil.toJsonPrettyStr(vo15),JSONUtil.toJsonPrettyStr(result));
                return StrUtil.format(resultRemark, """
                          结果字段说明:
                          未送达订单X笔 order_count_no
                              涉及客户X家 cnt_no
                              X万平方米 productarea_no
                        """, result);
            } catch (Exception e) {
                log.error("调用 deliverOrderStatus 失败", e);
                return StrUtil.format(resultRemark, "deliverOrderStatus 调用失败", e.getMessage());
            }
        }, executor);
        futures.add(deliverOrderStatusFuture);

        // 异步调用 firstOrderNo
        CompletableFuture<String> firstOrderNoFuture = CompletableFuture.supplyAsync(() -> {
            try {
                String result = dataServerClient.firstOrderNo(vo15);
                log.debug("firstOrderNo unioSupplierId: {} cycleType: {} startTime: {} endTime: {} param: {} result: {}",unioSupplierId, cycleType,startTime, endTime, JSONUtil.toJsonPrettyStr(vo15),JSONUtil.toJsonPrettyStr(result));
                return StrUtil.format(resultRemark, """
                          结果字段说明:
                          childpaymenttime   (X月X日)
                        """, result);
            } catch (Exception e) {
                log.error("调用 firstOrderNo 失败", e);
                return StrUtil.format(resultRemark, "firstOrderNo 调用失败", e.getMessage());
            }
        }, executor);
        futures.add(firstOrderNoFuture);

        // 计算前一个周期的时间范围
        DateRange previousRange = calculatePreviousPeriod(cycleType, startTime, endTime);
        // 创建前一个周期的查询参数
        DeliverOrderStatusVo previousVo = getPreviousParam(supplierIdList, config_list, unioSupplierId, previousRange);
        // 异步调用 deliverAllOut (包含环比计算)
        CompletableFuture<String> deliverAllOutFuture = CompletableFuture.supplyAsync(() -> {
            try {
                // 获取当前周期数据
                WebResultExt<List<DeliverOutRate>> currentResult = dataServerClient.deliverAllOut(vo);
                log.debug("deliverAllOut curren unioSupplierId: {} cycleType: {} startTime: {} endTime: {} param: {} result: {}",unioSupplierId, cycleType,startTime, endTime, JSONUtil.toJsonPrettyStr(vo),JSONUtil.toJsonPrettyStr(currentResult));
                String resultWithYoy = "无数据";

                try {

                    // 获取前一个周期数据
                    WebResultExt<List<DeliverOutRate>> previousResult = dataServerClient.deliverAllOut(previousVo);
                    log.debug("deliverAllOut previous unioSupplierId: {} cycleType: {} startTime: {} endTime: {} param: {} result: {}",unioSupplierId, cycleType,startTime, endTime, JSONUtil.toJsonPrettyStr(previousVo),JSONUtil.toJsonPrettyStr(previousResult));
                    // 提取24小时数据并计算环比
                    if (currentResult != null && currentResult.getData() != null && !currentResult.getData().isEmpty() &&
                            previousResult != null && previousResult.getData() != null && !previousResult.getData().isEmpty()) {

                        DeliverOutRate currentItem = currentResult.getData().get(0);
                        DeliverOutRate previousItem = previousResult.getData().get(0);
                        Double current24h = null, d = null;
                        if (currentItem!=null && previousItem!=null){
                            current24h = currentItem.getDelivery_rate24_hours();
                            d = current24h - previousItem.getDelivery_rate24_hours();
                            log.debug("交付率当前周期数据:{},前一个周期数据:{},环比差值:{}", currentItem, previousItem,d);
                        }

                        // 只提取24小时的数据
                        DeliveryGoal current = getGoalValue(unioSupplierId, mainSupplierId);
                        DeliveryGoal pre = getPreGoalValue(unioSupplierId, mainSupplierId);
                        log.debug("本月目标数据:{},前一个周期目标数据:{}", current, pre);
                        Double outGoalValue = null;
                        Double diff = null,huanbi = null;
                        if (current != null) {
                            outGoalValue = current.getOut24Hours();
                            if (outGoalValue != null && current24h != null) {
                                diff = current24h - outGoalValue;
                                if (pre != null && pre.getOut24Hours() != null) {
                                    huanbi = outGoalValue - pre.getOut24Hours();
                                }
                            }
                        }

                        // 构建简化的返回结果，只包含24小时数据
                        StringBuilder result = new StringBuilder();
                        result.append("不用乘以100%.\n24小时交付率: ").append(current24h).append("%");
                        result.append("\n24小时交付率环比: ").append(d).append("%");
                        result.append("\n目标差距,没有值用-展示: ").append(diff);
                        result.append("\n环比,没有值用-展示: ").append(huanbi);

                        resultWithYoy = result.toString();
                    } else {
                        resultWithYoy = "数据为空，无法计算24小时交付率";
                    }
                } catch (Exception e) {
                    log.warn("计算deliverAllOut环比值失败", e);
                    // 如果环比计算失败，至少返回当前数据
                    if (currentResult != null && currentResult.getData() != null && !currentResult.getData().isEmpty()) {
                        DeliverOutRate currentItem = currentResult.getData().get(0);
                        double current24h = currentItem.getDelivery_rate24_hours();
                        resultWithYoy = "24小时交付率: " + current24h + "\n环比计算失败: " + e.getMessage();
                    } else {
                        resultWithYoy = "环比计算失败: " + e.getMessage();
                    }
                }

                return StrUtil.format(resultRemark, "deliverAllOut 24小时交付率结果", resultWithYoy);
            } catch (Exception e) {
                log.error("调用 deliverAllOut 失败", e);
                return StrUtil.format(resultRemark, "deliverAllOut 调用失败", e.getMessage());
            }
        }, executor);
        futures.add(deliverAllOutFuture);

        // 异步调用 deliverAllArrive (包含环比计算)
        CompletableFuture<String> deliverAllArriveFuture = CompletableFuture.supplyAsync(() -> {
            try {
                // 获取当前周期数据
                WebResultExt<List<DeliverArriveRate>> currentResult = dataServerClient.deliverAllArrive(vo);
                log.debug("deliverAllArrive current unioSupplierId: {} cycleType: {} startTime: {} endTime: {} param: {} result: {}",unioSupplierId, cycleType,startTime, endTime, JSONUtil.toJsonPrettyStr(vo),JSONUtil.toJsonPrettyStr(currentResult));
                String resultWithYoy = "无数据";

                try {
                    // 获取前一个周期数据
                    WebResultExt<List<DeliverArriveRate>> previousResult = dataServerClient.deliverAllArrive(previousVo);
                    log.debug("deliverAllArrive current unioSupplierId: {} cycleType: {} startTime: {} endTime: {} param: {} result: {}",unioSupplierId, cycleType,startTime, endTime, JSONUtil.toJsonPrettyStr(previousVo),JSONUtil.toJsonPrettyStr(previousResult));
                    // 提取24小时数据并计算环比
                    if (currentResult != null && currentResult.getData() != null && !currentResult.getData().isEmpty() &&
                            previousResult != null && previousResult.getData() != null && !previousResult.getData().isEmpty()) {

                        DeliverArriveRate currentItem = currentResult.getData().get(0);
                        DeliverArriveRate previousItem = previousResult.getData().get(0);
                        Double current24h = null, d = null;
                        if (currentItem!=null && previousItem!=null){
                            current24h = currentItem.getDelivery_rate24_hours();
                            d = current24h - previousItem.getDelivery_rate24_hours();
                            log.debug("交付率当前周期数据:{},前一个周期数据:{},环比差值:{}", currentItem, previousItem,d);
                        }

                        // 只提取24小时的数据
                        DeliveryGoal current = getGoalValue(unioSupplierId, mainSupplierId);
                        DeliveryGoal pre = getPreGoalValue(unioSupplierId, mainSupplierId);
                        log.debug("本月目标数据:{},前一个周期目标数据:{}", current, pre);
                        Double outGoalValue = null;
                        Double diff = null,huanbi = null;
                        if (current != null) {
                            outGoalValue = current.getDelivery24Hours();
                            if (outGoalValue != null && current24h != null) {
                                diff = current24h - outGoalValue;
                                if (pre != null && pre.getDelivery24Hours() != null) {
                                    huanbi = outGoalValue - pre.getDelivery24Hours();
                                }
                            }
                        }

                        // 构建简化的返回结果，只包含24小时数据
                        StringBuilder result = new StringBuilder();
                        result.append("不用乘以100%.\n24小时到达率: ").append(current24h).append("%");
                        result.append("\n24小时到达率环比,没有值用-展示: ").append(d).append("%");
                        result.append("\n目标差距,没有值用-展示: ").append(diff);
                        result.append("\n环比,没有值用-展示: ").append(huanbi);
                        resultWithYoy = result.toString();
                    } else {
                        resultWithYoy = "数据为空，无法计算24小时到达率";
                    }
                } catch (Exception e) {
                    log.warn("计算deliverAllArrive环比值失败", e);
                    // 如果环比计算失败，至少返回当前数据
                    if (currentResult != null && currentResult.getData() != null && !currentResult.getData().isEmpty()) {
                        DeliverArriveRate currentItem = currentResult.getData().get(0);
                        double current24h = currentItem.getDelivery_rate24_hours();
                        resultWithYoy = "24小时到达率: " + current24h + "% \n环比计算失败: " + e.getMessage();
                    } else {
                        resultWithYoy = "环比计算失败: " + e.getMessage();
                    }
                }

                return StrUtil.format(resultRemark, "deliverAllArrive 24小时到达率结果", resultWithYoy);
            } catch (Exception e) {
                log.error("调用 deliverAllArrive 失败", e);
                return StrUtil.format(resultRemark, "deliverAllArrive 调用失败", e.getMessage());
            }
        }, executor);
        futures.add(deliverAllArriveFuture);

        // 异步调用 deliverCycle
        CompletableFuture<String> deliverCycleFuture = CompletableFuture.supplyAsync(() -> {
            try {
                String result = dataServerClient.deliverCycle(vo);
                log.debug("deliverCycle unioSupplierId: {} cycleType: {} startTime: {} endTime: {} param: {} result: {}",unioSupplierId, cycleType,startTime, endTime, JSONUtil.toJsonPrettyStr(vo15),JSONUtil.toJsonPrettyStr(result));
                return StrUtil.format(resultRemark, "deliverCycle 结果", result);
            } catch (Exception e) {
                log.error("调用 deliverCycle 失败", e);
                return StrUtil.format(resultRemark, "deliverCycle 调用失败", e.getMessage());
            }
        }, executor);
        futures.add(deliverCycleFuture);

        // 异步调用 firstArrive
        futures.add(CompletableFuture.supplyAsync(() -> {
            try {
                String result2 = dataServerClient.firstArrive(vo);
                log.debug("firstArrive unioSupplierId: {} cycleType: {} startTime: {} endTime: {} param: {} result: {}",unioSupplierId, cycleType,startTime, endTime, JSONUtil.toJsonPrettyStr(vo15),JSONUtil.toJsonPrettyStr(result2));
                return StrUtil.format(resultRemark, "firstArrive 结果", result2);
            } catch (Exception e2) {
                log.error("调用 firstArrive 失败", e2);
                return StrUtil.format(resultRemark, "firstArrive 调用失败", e2.getMessage());
            }
        }, executor));

        // 异步调用 deliverArea
        futures.add(CompletableFuture.supplyAsync(() -> {
            try {
                List<DeliverAreaVo> areaVos = dataServerClient.deliverArea(vo).fetchPageDataList();
                log.debug("deliverArea unioSupplierId: {} cycleType: {} startTime: {} endTime: {} param: {} result: {}",unioSupplierId, cycleType,startTime, endTime, JSONUtil.toJsonPrettyStr(vo),JSONUtil.toJsonPrettyStr(areaVos));
                if (CollUtil.isEmpty(areaVos)) {
                    return StrUtil.format(resultRemark, "deliverArea 结果", "无数据");
                }
                List<DeliverAreaVo> previousAreaVos = dataServerClient.deliverArea(previousVo).fetchPageDataList();
                log.debug("deliverArea unioSupplierId: {} cycleType: {}  param: {} result: {}",unioSupplierId, cycleType,  JSONUtil.toJsonPrettyStr(previousVo),JSONUtil.toJsonPrettyStr(previousAreaVos));
                if (CollUtil.isEmpty(areaVos)) {
                    return StrUtil.format(resultRemark, "deliverArea 结果", "无数据");
                }
                //先把 previousAreaVos 转为 map, key 为 areaName, value 为 delivery_rate24_hours
                Map<String, BigDecimal> previousAreaMap = previousAreaVos.stream().collect(Collectors.toMap(DeliverAreaVo::getAreaname, DeliverAreaVo::getDelivery_rate24_hours));
                //当前的 delivery_rate24_hours 减去前一个周期的 delivery_rate24_hours 如果 大于10 ,则收集到list中;
                List<String> list = new LinkedList<>();
                areaVos.forEach(areaVo -> {
                    String areaname = areaVo.getAreaname();
                    BigDecimal previousValue = previousAreaMap.get(areaname);
                    if (previousValue != null) {
                        BigDecimal deliveryRate24Hours = areaVo.getDelivery_rate24_hours();

                        if (previousValue.subtract(deliveryRate24Hours).compareTo(percent_10) > 0) {
                            list.add(areaname);
                        }
                    }
                });
                if (CollUtil.isEmpty(list)) {
                    return StrUtil.format(resultRemark, "deliverArea 暂无数据", "暂无数据");
                }

                return StrUtil.format(resultRemark, "以下 区域交期波动明显，需重点关注 ", StrUtil.join(",",list));
            } catch (Exception e1) {
                log.error("调用 deliverArea 失败", e1);
                return StrUtil.format(resultRemark, "deliverArea 调用失败", e1.getMessage());
            }
        }, executor));
        // 异步调用 deliverArea
        if (StrUtil.equalsIgnoreCase("month", cycleType)) {
            futures.add(CompletableFuture.supplyAsync(() -> {
                try {
                    String result = toolsFeign.getMetrics(new ToolsFeignParam(supplierIdList,true));
                    log.debug("交期影响 unioSupplierId: {} cycleType: {} startTime: {} endTime: {} param: {} result: {}",unioSupplierId, cycleType,startTime, endTime, JSONUtil.toJsonPrettyStr(vo15),JSONUtil.toJsonPrettyStr(result));
                    return StrUtil.format(resultRemark, "交期影响 结果 areaByDeliver 影响订单万平米,customerNumByDelivery 影响客户家数", result);
                } catch (Exception e) {
                    log.error("调用 deliverArea 失败", e);
                    return StrUtil.format(resultRemark, "deliverArea 调用失败", e.getMessage());
                }
            }, executor));
        }

        // 等待所有异步任务完成并收集结果
        try {
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            allFutures.join(); // 等待所有任务完成

            // 收集所有结果
            List<String> results = new ArrayList<>();
            for (CompletableFuture<String> future : futures) {
                results.add(future.get());
            }

            log.info("所有异步调用完成，共获取到 {} 个结果", results.size());
            log.debug("该任务入参:{},所有结果: {}",vo,results);
            return results;

        } catch (Exception e) {
            log.error("等待异步任务完成时发生错误", e);
            // 如果出现异常，返回已完成的结果
            List<String> results = new ArrayList<>();
            for (CompletableFuture<String> future : futures) {
                try {
                    if (future.isDone() && !future.isCompletedExceptionally()) {
                        results.add(future.get());
                    }
                } catch (Exception ex) {
                    log.warn("获取异步任务结果失败", ex);
                }
            }
            return results;
        }
    }

    private static DeliverOrderStatusVo getPreviousParam(List<String> supplierIdList, List<DeliveryConfigJSON> config_list, String unioSupplierId, DateRange previousRange) {
        // 创建前一个周期的查询参数
        DeliverOrderStatusVo previousVo = new DeliverOrderStatusVo();
        previousVo.setStartTime(previousRange.getStart().format(formatter));
        previousVo.setEndTime(previousRange.getEnd().format(formatter));
        previousVo.setSupplierList(supplierIdList);
        previousVo.setUnionSupplierId(unioSupplierId);
        previousVo.setConfigList(config_list);
        return previousVo;
    }


}
