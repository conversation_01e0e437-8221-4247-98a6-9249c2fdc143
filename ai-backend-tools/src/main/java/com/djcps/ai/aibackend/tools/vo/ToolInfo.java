package com.djcps.ai.aibackend.tools.vo;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.Data;
import org.springframework.ai.tool.definition.ToolDefinition;

import static com.djcps.ai.aibackend.tools.AiConstant.objectMapper;


@Data
public class ToolInfo {
    private Long id;
    private String name;
    private String description;
    private Object inputSchema;

    public ToolInfo(String name, String description) {
        this.name = name;
        this.description = description;
    }

    public ToolInfo(Long id, String name, String description) {
        this.id = id;
        this.name = name;
        this.description = description;
    }

    public static ToolInfo fromToolDefinition(ToolDefinition toolDefinition) {
        ToolInfo info = new ToolInfo(toolDefinition.name(), toolDefinition.description());
        String s = toolDefinition.inputSchema();
        if (StrUtil.isBlankIfStr(s)) {
            return info;
        }
        try {
            Object object = objectMapper.readValue(s, Object.class);
            info.setInputSchema(object);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        return info;
    }
}
