package com.djcps.ai.aibackend.tools;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.djcps.ai.aibackend.tools.vo.ToolCallParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.stereotype.Service;

import static com.djcps.ai.aibackend.tools.AiConstant.date_template;


@Service("chart")
@Slf4j
public class ChartToolCallService extends BaseToolCallService {

    public ChartToolCallService(ChatClient chatClient) {
        super(chatClient);
    }

    @Override
    public String prompt(ToolCallParam runParam) {
        String format = StrUtil.format(date_template, DateUtil.today(), runParam.getCycleType());
        format += ";请以JSON格式返回ECharts图表配置，包含完整的option对象，用于数据可视化展示。";
        return format;
    }

}
