# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Overview

This is a **Java-based AI backend service** built with Spring Boot 3.4.7 and Java 17, providing AI-powered functionalities including conversational AI, data analysis, and automation tools.

## Common Development Commands

### Build & Run
```bash
# Build entire project (from root)
mvn clean compile

# Package application
mvn clean package

# Run with specific profile (local is default)
mvn spring-boot:run -Dspring-boot.run.profiles=local

# Run with other environments
mvn clean package -P dev
mvn clean package -P aliyun-test  
mvn clean package -P exp
mvn clean package -P pro

# Run built JAR (after packaging)
java -jar ai-backend-web/target/ai-backend-web-1.0-SNAPSHOT.jar
```

### Testing
```bash
# Run all tests
mvn test

# Run tests for specific module
mvn test -pl ai-backend-service
mvn test -pl ai-backend-web
```

### Development
```bash
# Clean build artifacts
mvn clean

# Install dependencies to local repository
mvn install

# Skip tests during build
mvn clean package -DskipTests
```

## Architecture

The project follows a **multi-module Maven structure** with clear separation of concerns:

- **ai-backend-web**: Web layer (controllers, REST APIs) - *Main application module*
- **ai-backend-service**: Business logic layer  
- **ai-backend-core**: Core functionalities and external integrations
- **ai-backend-dao**: Data access layer with MyBatis-Plus
- **ai-backend-config**: Configuration management
- **ai-backend-common**: Common utilities and enums
- **ai-backend-tools**: AI tool implementations
- **ai-backend-utils**: Utility classes
- **ai-backend-feign**: Feign client definitions

### Key Architectural Patterns
- **Layered Architecture**: Clear separation between web, service, and data access layers
- **Dependency Injection**: Spring's IoC container manages all components
- **AOP**: Cross-cutting concerns handled via Spring AOP and AspectJ
- **Multi-Profile Configuration**: Environment-specific configurations via Maven profiles
- **RESTful APIs**: Endpoints follow `.do` suffix convention
- **Streaming Support**: Server-Sent Events for real-time AI responses

## Key Features

### 1. **AI Conversation Management**
- Integration with **Dify AI platform** for conversational AI capabilities
- Support for both regular and streaming chat responses
- Conversation history management (create, rename, delete)
- Message feedback and rating system
- User favorites management

### 2. **Multi-Function AI Support**
- **AI Report Generation**: Data analysis and reporting
- **FAQ Assistant**: Question-answering system
- **SQL Generation**: AI-powered SQL query generation
- **Cron Expression Generator**: Automated scheduling expressions

### 3. **Data Management**
- MySQL database with Druid connection pooling
- MyBatis-Plus for ORM operations
- System configuration management
- Task result tracking and storage

### 4. **External Integrations**
- **Dify AI Platform**: Primary AI service provider
- **Data Server**: External data source integration
- **Authentication Service**: User authentication via DjAuth
- **Job Scheduling**: Integration with DjJob service

### 5. **Modern Web Frontend**
- React-based admin interface
- Tailwind CSS styling with glassmorphism effects
- ECharts for data visualization
- Real-time SSE (Server-Sent Events) for streaming responses

## Technology Stack

### Backend
- **Spring Boot 3.4.7** with Spring Cloud 2024.0.0
- **Java 17** with Lombok
- **MyBatis-Plus 3.5.7** for database operations
- **Spring AI 1.0.0** for AI integrations
- **OpenFeign** for service-to-service communication
- **Druid 1.2.24** for database connection pooling
- **Spring Security** for authentication and authorization
- **Spring Cloud Alibaba 2023.0.3.2** for microservice support

### Frontend
- **React 18** with Babel JSX transformation
- **Tailwind CSS** for styling
- **ECharts** for data visualization
- **Server-Sent Events** for real-time updates

### Database & Storage
- **MySQL** for primary data storage
- **Apollo Configuration** for centralized configuration management

### Development Tools
- **Hutool 5.8.39** - Java utility library
- **Guava 32.1.3** - Google core libraries
- **Log4j2** for logging (Spring Boot default logging excluded)
- **Spring Boot Actuator** for monitoring and health checks

## Key Database Entities
- **SystemConfig**: System configuration management
- **TaskResult**: AI task execution results
- **UserFavorite**: User's favorite conversations/queries
- **UserTask**: User task management
- **UserTemplate**: Template management
- **UserTaskRunRecord**: Task execution history
- **TaskInvokeMethod**: Task invocation method definitions

## Main Application Entry Point
- **Main Class**: Located in `ai-backend-web` module
- **Application**: Spring Boot application with web server
- **Default Profile**: `local` (can be overridden with `-P` flag)

## API Endpoints
- `/ai/submit.do` - Submit AI queries
- `/ai/submitStream.do` - Streaming AI responses
- `/ai/getAllConversations.do` - Get conversation history
- `/ai/rename.do` - Rename conversations
- `/ai/feedbacks.do` - Submit message feedback
- `/generate/corn` - Generate cron expressions
- `/task/**` - Task management endpoints

## Security & Configuration
- Spring Security integration
- Apollo configuration management support
- Environment-specific configurations (local, dev, aliyun-test, exp, pro)
- JWT token-based authentication
- Maven resource filtering for environment variables

## Maven Module Dependencies
The dependency hierarchy flows from web → service → dao/core/config, with common utilities shared across all modules. The web module is the main executable application that packages all dependencies.

This project appears to be a comprehensive AI backend solution designed for enterprise use, providing conversational AI capabilities, data analysis tools, and administrative interfaces for managing AI-powered workflows.