#全局变量
---
spring:
  profiles:
    active: "@package.environment@"
  application:
    name: ai-backend
  main:
    allow-circular-references: true
  mvc:
    pathmatch:
      use-suffix-pattern: true
    servlet:
      path: /ai-backend
    async:
      request-timeout: 180s
    static-path-pattern: /**
  web:
    resources:
      static-locations: classpath:/static/
      add-mappings: true
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    initialSize: 5
    minIdle: 5
    maxActive: 20
    maxWait: 60000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 300000
    validationQuery: SELECT 1 FROM DUAL
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
    poolPreparedStatements: true
    filters: stat,wall,slf4j
    maxPoolPreparedStatementPerConnectionSize: 20
    useGlobalDataSourceStat: true
    connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=500
    publisher-returns: true

  http:
    client:
      read-timeout: 180000
feign:
  httpclient:
    connection-timeout: 30000
    enabled: true
    max-connections: 50
    max-connections-per-route: 20
  compression:
    request:
      enabled: true
      min-request-size: 2048
    response:
      enabled: true
server:
  compression:
    enabled: true
mybatis-plus:
  type-aliases-package: com.djcps.ai.dao.entity
  type-aliases-super-type: com.djcps.ai.dao.base.BaseEntity
  mapper-locations: classpath*:mapper/*.xml
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
client:
  url:
    easyOrder: http://djeasyorder
    crm: http://djcrm
    djjob: http://djjob
    djauth: http://djauth
    tools: http://djai-tool-starter
