app.id=ai-backend
#apollo.bootstrap.enabled=true
#apollo.bootstrap.namespaces=application,url,DJCPS.commonu-utils
#apollo.meta=http://service-apollo-config-server-dev.apollo.svc.cluster.local:8080
logging.level.com.djcps.ai=debug
logging.level.cn.hutool.db=debug
#openRouter
spring.ai.openai.base-url=https://openrouter.ai/api
spring.ai.openai.chat.options.model=anthropic/claude-sonnet-4
spring.ai.openai.api-key=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
spring.ai.openai.chat.options.temperature=0
#qwen
#spring.ai.openai.base-url=https://dashscope.aliyuncs.com/compatible-mode/
#spring.ai.openai.chat.options.model=qwen3-14b
#spring.ai.openai.api-key=sk-f3f80e4238b14457ae9f4c1e0c54b0f5