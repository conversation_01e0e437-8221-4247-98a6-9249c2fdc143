#测试环境公共配置
---
spring:
  config:
    activate:
      on-profile: exp
  datasource:
    username: djai_backend_sR6s
    password: JIh$B04GvhQcq#em
    url: *********************************************************************************************************************


dataServer:
  app_key: c59IrXu7
  url: http://data-server/data-server
dify:
  url: https://djai.djcps.com
  app_key: app-br4hIiGoTtMkRi9NRxLQh5pA
apollo:
  meta: http://service-apollo-config-server-dev.apollo.svc.cluster.local:8080
  bootstrap:
    enabled: true
    namespaces: application,url,DJCPS.commonu-utils