#本地测试环境公共配置
---
apollo:
  meta: http://192.168.23.12:8080
  bootstrap:
    enabled: true
    namespaces: application,DJCPS.url,DJCPS.dev-commonu-utils
---

spring:
  config:
    activate:
      on-profile: local
  datasource:
    username: djai_backend_L8mM
    password: QbxwXStjFfP@Dv6#
    url: *********************************************************************************************************************



dify:
  url: https://djai.djcps.com
  key:
    ai_report: app-LSONBVnMdB2kht6xDT5iojKP
    faq: app-EThtMqtjUoaWDtvqqK7h0Zve

dataServer:
  app_key: c59IrXu7
  url: http://172.31.0.16:30994/data-server
logging:
  level:
    com.alibaba.druid: debug