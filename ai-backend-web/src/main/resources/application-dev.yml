#本地测试环境公共配置
---
spring:
  config:
    activate:
      on-profile: dev
  main:
    allow-circular-references: true


  datasource:
    username: djoa
    password: dongjingoa
    url: *************************************************************************************
    initialSize: 5
    minIdle: 5
    maxActive: 20
    maxWait: 60000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 300000
    validationQuery: SELECT 1 FROM DUAL
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
    poolPreparedStatements: true
    filters: stat,wall,slf4j
    maxPoolPreparedStatementPerConnectionSize: 20
    useGlobalDataSourceStat: true
    connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=500


    publisher-returns : true

---
apollo:
  meta: http://*************:8080
  bootstrap:
    enabled: true
    namespaces: application,DJCPS.url,DJCPS.dev-commonu-utils
---
ai:
  python:
    url: http://*************:8000

dataServer:
  app_key: c59IrXu7
  url: http://***********:30994/data-server
dify:
  url: https://dkai.cpsol.net
  key:
    ai_report: app-0EhZWElBUSmIe9ePjJ5egH3A
    agent: app-WkvaPbCvjNTm1couylRLyZc6
    manage_board: app-s6BhL1I7YfX3Yc5dGQFZcbCP
    faq: app-LG0DAIPP3U7XiNO3ma9VxZo3
    faq_easyorder: app-LG0DAIPP3U7XiNO3ma9VxZo3
    faq_yzx: app-iWcEbbMU0bKXXNA4Ve6vwJKt