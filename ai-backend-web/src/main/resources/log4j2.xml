<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout
                    pattern="%d{yyyy.MM.dd HH:mm:ss z} [%thread] %-5level %class{36}.%M()/%L - %msg%xEx%n"/>
        </Console>
    </Appenders>

    <Loggers>
        <!-- 项目所有日志输出到控制台 -->
        <Root level="INFO">
            <appender-ref ref="Console"/>
        </Root>
        <!-- debug级别不会自动输出 -->
        <Logger name="org.springframework.amqp.rabbit.listener.BlockingQueueConsumer" level="INFO" additivity="false">
            <appender-ref ref="Console"/>
        </Logger>

        <Logger name="org.apache.kafka" level="INFO" additivity="false">
            <appender-ref ref="Console"/>
        </Logger>

        <Logger name="org.springframework.kafka" level="INFO" additivity="false">
            <appender-ref ref="Console"/>
        </Logger>

        <Logger name="org.apache.http.impl.conn" level="INFO" additivity="false">
            <appender-ref ref="Console"/>
        </Logger>
        <Logger name="org.springframework.jdbc" level="INFO" additivity="false">
            <appender-ref ref="Console"/>
        </Logger>


    </Loggers>
</configuration>
