<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown 渲染测试</title>
    <!-- 引入Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入Marked用于Markdown解析 -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <style>
        /* Markdown样式 */
        .markdown-content {
            line-height: 1.6;
            color: #374151;
        }
        
        .markdown-content h1,
        .markdown-content h2,
        .markdown-content h3,
        .markdown-content h4,
        .markdown-content h5,
        .markdown-content h6 {
            font-weight: 600;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
            color: #1f2937;
        }
        
        .markdown-content h1 { font-size: 1.5em; }
        .markdown-content h2 { font-size: 1.3em; }
        .markdown-content h3 { font-size: 1.1em; }
        .markdown-content h4 { font-size: 1em; }
        .markdown-content h5 { font-size: 0.9em; }
        .markdown-content h6 { font-size: 0.8em; }
        
        .markdown-content p {
            margin-bottom: 1em;
        }
        
        .markdown-content ul,
        .markdown-content ol {
            margin-bottom: 1em;
            padding-left: 1.5em;
        }
        
        .markdown-content li {
            margin-bottom: 0.25em;
        }
        
        .markdown-content blockquote {
            border-left: 4px solid #e5e7eb;
            padding-left: 1em;
            margin: 1em 0;
            color: #6b7280;
            font-style: italic;
        }
        
        .markdown-content code {
            background-color: #f3f4f6;
            padding: 0.125em 0.25em;
            border-radius: 0.25rem;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.875em;
        }
        
        .markdown-content pre {
            background-color: #f3f4f6;
            padding: 1em;
            border-radius: 0.5rem;
            overflow-x: auto;
            margin: 1em 0;
        }
        
        .markdown-content pre code {
            background-color: transparent;
            padding: 0;
        }
        
        .markdown-content table {
            border-collapse: collapse;
            width: 100%;
            margin: 1em 0;
        }
        
        .markdown-content th,
        .markdown-content td {
            border: 1px solid #e5e7eb;
            padding: 0.5em;
            text-align: left;
        }
        
        .markdown-content th {
            background-color: #f9fafb;
            font-weight: 600;
        }
        
        .markdown-content a {
            color: #3b82f6;
            text-decoration: underline;
        }
        
        .markdown-content a:hover {
            color: #1d4ed8;
        }
        
        .markdown-content strong {
            font-weight: 600;
        }
        
        .markdown-content em {
            font-style: italic;
        }
        
        .markdown-content hr {
            border: none;
            border-top: 1px solid #e5e7eb;
            margin: 2em 0;
        }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Markdown 渲染测试</h1>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 原始 Markdown 输入 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">原始 Markdown 文本</h2>
                <textarea 
                    id="markdownInput" 
                    class="w-full h-96 p-4 border border-gray-300 rounded-md font-mono text-sm"
                    placeholder="在这里输入 Markdown 文本..."
                ># 分析结果示例

## 销售数据分析

### 主要发现

1. **销售增长**: 本月销售额较上月增长 **15.3%**
2. **热门产品**: 
   - 产品A: 销量增长 25%
   - 产品B: 销量增长 18%
   - 产品C: 销量增长 12%

### 详细数据

| 产品名称 | 销量 | 增长率 |
|---------|------|--------|
| 产品A   | 1,250 | +25% |
| 产品B   | 980   | +18% |
| 产品C   | 750   | +12% |

### 建议

> 基于以上数据分析，建议：
> 1. 加大对产品A的推广力度
> 2. 优化产品B的库存管理
> 3. 考虑产品C的价格策略调整

### 代码示例

```javascript
// 计算增长率
function calculateGrowthRate(current, previous) {
    return ((current - previous) / previous * 100).toFixed(1);
}
```

---

**注意**: 以上数据仅为示例，实际分析结果可能有所不同。</textarea>
            </div>
            
            <!-- 渲染后的结果 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">渲染后的结果</h2>
                <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                    <div class="text-xs font-medium text-blue-800 mb-2">分析结果</div>
                    <div 
                        id="markdownOutput" 
                        class="text-sm text-blue-900 leading-relaxed markdown-content"
                    ></div>
                </div>
            </div>
        </div>
        
        <div class="mt-8 bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">使用说明</h2>
            <div class="text-gray-600 space-y-2">
                <p>1. 在左侧文本框中输入或修改 Markdown 文本</p>
                <p>2. 右侧会实时显示渲染后的结果</p>
                <p>3. 支持标题、列表、表格、代码块、引用等 Markdown 语法</p>
                <p>4. 样式与实际应用中的分析结果显示保持一致</p>
            </div>
        </div>
    </div>

    <script>
        // 初始化
        const input = document.getElementById('markdownInput');
        const output = document.getElementById('markdownOutput');
        
        // 渲染 Markdown
        function renderMarkdown() {
            const markdownText = input.value;
            const htmlContent = marked.parse(markdownText);
            output.innerHTML = htmlContent;
        }
        
        // 监听输入变化
        input.addEventListener('input', renderMarkdown);
        
        // 初始渲染
        renderMarkdown();
    </script>
</body>
</html>
