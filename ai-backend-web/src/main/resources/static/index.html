<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理系统</title>
    <!-- 引入Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 自定义Tailwind配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#10b981',
                        dark: '#1f2937',
                    },
                },
            },
        }
    </script>
    <!-- 引入React和ReactDOM -->
    <script src="https://unpkg.com/react@18/umd/react.development.js" crossorigin></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js" crossorigin></script>
    <!-- 引入Babel用于JSX转换 -->
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <!-- 引入Marked用于Markdown解析 -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <!-- 自定义样式 -->
    <style>
        body {
            background-image: url('https://images.unsplash.com/photo-1557682250-33bd709cbe85?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2029&q=80');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            background-attachment: fixed;
            height: 100vh;
            margin: 0;
            font-family: 'Inter', sans-serif;
        }
        .glass {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        /* Markdown样式 */
        .markdown-content {
            line-height: 1.6;
            color: #374151;
        }

        .markdown-content h1,
        .markdown-content h2,
        .markdown-content h3,
        .markdown-content h4,
        .markdown-content h5,
        .markdown-content h6 {
            font-weight: 600;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
            color: #1f2937;
        }

        .markdown-content h1 { font-size: 1.5em; }
        .markdown-content h2 { font-size: 1.3em; }
        .markdown-content h3 { font-size: 1.1em; }
        .markdown-content h4 { font-size: 1em; }
        .markdown-content h5 { font-size: 0.9em; }
        .markdown-content h6 { font-size: 0.8em; }

        .markdown-content p {
            margin-bottom: 1em;
        }

        .markdown-content ul,
        .markdown-content ol {
            margin-bottom: 1em;
            padding-left: 1.5em;
        }

        .markdown-content li {
            margin-bottom: 0.25em;
        }

        .markdown-content blockquote {
            border-left: 4px solid #e5e7eb;
            padding-left: 1em;
            margin: 1em 0;
            color: #6b7280;
            font-style: italic;
        }

        .markdown-content code {
            background-color: #f3f4f6;
            padding: 0.125em 0.25em;
            border-radius: 0.25rem;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.875em;
        }

        .markdown-content pre {
            background-color: #f3f4f6;
            padding: 1em;
            border-radius: 0.5rem;
            overflow-x: auto;
            margin: 1em 0;
        }

        .markdown-content pre code {
            background-color: transparent;
            padding: 0;
        }

        .markdown-content table {
            border-collapse: collapse;
            width: 100%;
            margin: 1em 0;
        }

        .markdown-content th,
        .markdown-content td {
            border: 1px solid #e5e7eb;
            padding: 0.5em;
            text-align: left;
        }

        .markdown-content th {
            background-color: #f9fafb;
            font-weight: 600;
        }

        .markdown-content a {
            color: #3b82f6;
            text-decoration: underline;
        }

        .markdown-content a:hover {
            color: #1d4ed8;
        }

        .markdown-content strong {
            font-weight: 600;
        }

        .markdown-content em {
            font-style: italic;
        }

        .markdown-content hr {
            border: none;
            border-top: 1px solid #e5e7eb;
            margin: 2em 0;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div id="root"></div>

    <!-- API配置 -->
    <script src="js/config/api.js"></script>

    <!-- React组件 -->
    <script type="text/babel" src="js/components/MessageToast.js"></script>
    <script type="text/babel" src="js/components/Sidebar.js"></script>
    <script type="text/babel" src="js/components/TemplateManagement.js"></script>
    <script type="text/babel" src="js/components/TaskManagement.js"></script>
    <script type="text/babel" src="js/components/TaskResultManagement.js"></script>
    <script type="text/babel" src="js/components/TaskInvokeMethodManagement.js"></script>
    <script type="text/babel" src="js/App.js"></script>
</body>
</html>