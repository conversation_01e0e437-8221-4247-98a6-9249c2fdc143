// App.js - 主应用组件
const { useState } = React;

function App() {
    // 当前选中的导航项
    const [activeNav, setActiveNav] = useState('task');
    
    // 根据activeNav渲染对应的组件
    const renderContent = () => {
        switch(activeNav) {
            case 'template':
                return <TemplateManagement />;
            case 'task':
                return <TaskManagement />;
            case 'taskInvokeMethod':
                return <TaskInvokeMethodManagement />;
            default:
                return <TaskManagement />;
        }
    };

    return (
        <div className="flex h-screen overflow-hidden">
            {/* 左侧导航栏 */}
            <Sidebar activeNav={activeNav} setActiveNav={setActiveNav} />
            
            {/* 主内容区域 */}
            <main className="flex-1 overflow-y-auto p-6">
                <div className="glass rounded-xl p-6 h-full">
                    {renderContent()}
                </div>
            </main>
        </div>
    );
}

// 渲染App组件到DOM
ReactDOM.createRoot(document.getElementById('root')).render(<App />);