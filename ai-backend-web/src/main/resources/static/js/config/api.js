// api.js - API配置文件
// 统一管理API基础URL和接口地址

// 获取当前页面的基础URL
const getBaseUrl = () => {
    const protocol = window.location.protocol;
    const host = window.location.host;
    const contextPath = '/ai-backend';
    return `${protocol}//${host}${contextPath}`;
};

// API基础配置
const API_CONFIG = {
    BASE_URL: getBaseUrl(),
    
    // 工具相关接口
    TOOLS: {
        LIST: '/tools/list',
        CALL: '/tools/callTool',
        TEST: '/tools/test'
    },
    
    // 任务相关接口
    TASK: {
        LIST: '/task/list',
        SAVE: '/task/save',
        DELETE: '/task/delete/{id}',
        RUN: '/task/run/{taskId}',
        DEBUG: '/task/debug/{taskId}/{count}',
        REANALYZE: '/task/reanalyze/{taskId}',
        REANALYZE_BY_BATCH: '/task/reanalyze/{taskId}/{batchNo}',
        RUN_RECORDS: '/task/runRecords/{taskId}',
        TASK_RESULTS: '/task/taskResults/{taskId}/{batchNo}',
        GENERATE_CRON: '/generate/corn',
        VALIDATE_CRON: '/task/validateCron'
    },
    
    // 模板相关接口
    TEMPLATE: {
        LIST: '/template/list',
        SAVE: '/template/save',
        DELETE: '/template/delete/{id}',
        TEST: '/tools/test'
    },
    
    // 任务结果相关接口
    TASK_RESULT: {
        PAGE: '/result/pageNoLogin.do',
        DELETE: '/taskResult/delete'
    },

    // 方法注册相关接口
    TASK_INVOKE_METHOD: {
        LIST: '/task-invoke-method/list',
        SAVE: '/task-invoke-method/save',
        DELETE: '/task-invoke-method/delete/{id}',
        GET_BY_ID: '/task-invoke-method/{id}'
    }
};

// 构建完整的API URL
const buildApiUrl = (endpoint, params = {}) => {
    let url = `${API_CONFIG.BASE_URL}${endpoint}`;

    // 替换URL中的参数占位符
    Object.keys(params).forEach(key => {
        url = url.replace(`{${key}}`, params[key]);
    });

    return url;
};

// 通用的fetch封装
const apiRequest = async (endpoint, options = {}, urlParams = {}) => {
    const url = buildApiUrl(endpoint, urlParams);
    const defaultOptions = {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    };

    const finalOptions = { ...defaultOptions, ...options };

    try {
        const response = await fetch(url, finalOptions);

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        // 检查响应内容是否为空
        const responseText = await response.text();
        if (!responseText.trim()) {
            // 如果响应为空，返回一个默认的成功响应
            return { success: true, message: '操作成功' };
        }

        try {
            return JSON.parse(responseText);
        } catch (parseError) {
            console.error('Failed to parse JSON response:', parseError);
            console.error('Response text:', responseText);
            throw new Error('Invalid JSON response');
        }
    } catch (error) {
        console.error(`API request failed for ${endpoint}:`, error);
        throw error;
    }
};

// 流式API请求封装（用于处理Server-Sent Events等流式响应）
const streamApiRequest = async (endpoint, options = {}, urlParams = {}) => {
    const url = buildApiUrl(endpoint, urlParams);
    const defaultOptions = {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    };

    const finalOptions = { ...defaultOptions, ...options };

    try {
        const response = await fetch(url, finalOptions);

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`HTTP error! Status: ${response.status}, Message: ${errorText}`);
        }

        return response; // 返回原始response对象，让调用者处理流
    } catch (error) {
        console.error(`Stream API request failed for ${endpoint}:`, error);
        throw error;
    }
};

// 导出配置和工具函数
window.API_CONFIG = API_CONFIG;
window.buildApiUrl = buildApiUrl;
window.apiRequest = apiRequest;
window.streamApiRequest = streamApiRequest;
