// MessageToast.js - 消息提示组件
const { useState, useEffect } = React;

function MessageToast({ message, type = 'info', duration = 3000, onClose }) {
    const [isVisible, setIsVisible] = useState(true);

    useEffect(() => {
        const timer = setTimeout(() => {
            setIsVisible(false);
            setTimeout(() => {
                if (onClose) onClose();
            }, 300); // 等待动画完成
        }, duration);

        return () => clearTimeout(timer);
    }, [duration, onClose]);

    const getTypeStyles = () => {
        switch (type) {
            case 'success':
                return 'bg-green-100 border-green-400 text-green-700';
            case 'error':
                return 'bg-red-100 border-red-400 text-red-700';
            case 'warning':
                return 'bg-yellow-100 border-yellow-400 text-yellow-700';
            default:
                return 'bg-blue-100 border-blue-400 text-blue-700';
        }
    };

    const getIcon = () => {
        switch (type) {
            case 'success':
                return (
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                );
            case 'error':
                return (
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                );
            case 'warning':
                return (
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                );
            default:
                return (
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                );
        }
    };

    if (!isVisible) {
        return null;
    }

    return (
        <div className={`fixed top-4 right-4 z-50 max-w-sm w-full transition-all duration-300 transform ${
            isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'
        }`}>
            <div className={`border-l-4 p-4 rounded-md shadow-lg ${getTypeStyles()}`}>
                <div className="flex items-center">
                    <div className="flex-shrink-0">
                        {getIcon()}
                    </div>
                    <div className="ml-3 flex-1">
                        <p className="text-sm font-medium">{message}</p>
                    </div>
                    <div className="ml-4 flex-shrink-0">
                        <button
                            className="inline-flex text-gray-400 hover:text-gray-600 focus:outline-none"
                            onClick={() => {
                                setIsVisible(false);
                                setTimeout(() => {
                                    if (onClose) onClose();
                                }, 300);
                            }}
                        >
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}

// 消息管理器
class MessageManager {
    constructor() {
        this.messages = [];
        this.container = null;
        this.init();
    }

    init() {
        // 创建消息容器
        this.container = document.createElement('div');
        this.container.id = 'message-toast-container';
        document.body.appendChild(this.container);
    }

    show(message, type = 'info', duration = 3000) {
        const messageId = Date.now() + Math.random();
        
        const messageComponent = React.createElement(MessageToast, {
            message,
            type,
            duration,
            onClose: () => this.remove(messageId)
        });

        this.messages.push({ id: messageId, component: messageComponent });
        this.render();

        return messageId;
    }

    remove(messageId) {
        this.messages = this.messages.filter(msg => msg.id !== messageId);
        this.render();
    }

    render() {
        const elements = this.messages.map(msg => msg.component);
        ReactDOM.render(React.createElement('div', null, ...elements), this.container);
    }

    success(message, duration = 3000) {
        return this.show(message, 'success', duration);
    }

    error(message, duration = 5000) {
        return this.show(message, 'error', duration);
    }

    warning(message, duration = 4000) {
        return this.show(message, 'warning', duration);
    }

    info(message, duration = 3000) {
        return this.show(message, 'info', duration);
    }
}

// 创建全局实例
window.messageManager = new MessageManager();

// 确认对话框组件
function ConfirmDialog({ message, onConfirm, onCancel, isVisible }) {
    if (!isVisible) return null;

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
            {/* 背景遮罩 */}
            <div className="fixed inset-0 bg-black bg-opacity-50" onClick={onCancel}></div>

            {/* 对话框 */}
            <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4 p-6">
                <div className="flex items-center mb-4">
                    <div className="flex-shrink-0">
                        <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                    </div>
                    <div className="ml-3">
                        <h3 className="text-lg font-medium text-gray-900">确认操作</h3>
                    </div>
                </div>

                <div className="mb-6">
                    <p className="text-sm text-gray-600">{message}</p>
                </div>

                <div className="flex justify-end space-x-3">
                    <button
                        onClick={onCancel}
                        className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                        取消
                    </button>
                    <button
                        onClick={onConfirm}
                        className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    >
                        确定
                    </button>
                </div>
            </div>
        </div>
    );
}

// 确认对话框管理器
class ConfirmManager {
    constructor() {
        this.container = null;
        this.currentDialog = null;
        this.init();
    }

    init() {
        this.container = document.createElement('div');
        this.container.id = 'confirm-dialog-container';
        document.body.appendChild(this.container);
    }

    show(message) {
        return new Promise((resolve) => {
            const handleConfirm = () => {
                this.hide();
                resolve(true);
            };

            const handleCancel = () => {
                this.hide();
                resolve(false);
            };

            this.currentDialog = React.createElement(ConfirmDialog, {
                message,
                onConfirm: handleConfirm,
                onCancel: handleCancel,
                isVisible: true
            });

            this.render();
        });
    }

    hide() {
        this.currentDialog = null;
        this.render();
    }

    render() {
        ReactDOM.render(this.currentDialog, this.container);
    }
}

// 创建全局实例
window.confirmManager = new ConfirmManager();

// 导出便捷方法
window.showMessage = {
    success: (message, duration) => window.messageManager.success(message, duration),
    error: (message, duration) => window.messageManager.error(message, duration),
    warning: (message, duration) => window.messageManager.warning(message, duration),
    info: (message, duration) => window.messageManager.info(message, duration)
};

// 导出确认对话框方法
window.showConfirm = (message) => window.confirmManager.show(message);
