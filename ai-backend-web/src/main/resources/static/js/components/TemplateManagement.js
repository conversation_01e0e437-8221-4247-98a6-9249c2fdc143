// TemplateManagement.js - 模板管理组件
const { useState, useEffect } = React;
function TemplateManagement() {
    // 状态管理
    const [templates, setTemplates] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedTemplate, setSelectedTemplate] = useState(null);
    const [isEditing, setIsEditing] = useState(false);
    const [isEditingMethod, setIsEditingMethod] = useState(false);
    const [formData, setFormData] = useState({
        name: '',
        description: '',
        content: '获取最近一周的数据,并分析接单面积的变化',
        type: 'text',
        methodId: '',
        subBizType: 'summary',
        bizType: 'deliver'
    });
    const [apiList, setApiList] = useState([]);
    const [apiLoading, setApiLoading] = useState(false);
    const [testResult, setTestResult] = useState(null);
    const [cycleType, setCycleType] = useState('week');
    const [showCycleDropdown, setShowCycleDropdown] = useState(false);
    const [resultType, setResultType] = useState('text'); // 结果类型：text 或 chart
    const [showResultTypeDropdown, setShowResultTypeDropdown] = useState(false);
    const [chartInstance, setChartInstance] = useState(null);

    // 获取模板列表
    useEffect(() => {
        fetchTemplateList();
    }, []);

    // 从后端获取模板列表
    const fetchTemplateList = async () => {
        try {
            setLoading(true);
            const result = await apiRequest(API_CONFIG.TEMPLATE.LIST);

            // 检查响应格式
            if (result.success && result.data) {
                // 将后端数据格式转换为前端格式
                const mappedTemplates = result.data.map(template => ({
                    id: template.id,
                    name: template.title || '',
                    description: template.remark || '',
                    content: template.prompt || '',
                    type: 'text', // 默认类型，可以根据需要调整
                    methodId: template.methodId || '',
                    subBizType: template.subBizType || 'summary',
                    bizType: template.bizType || 'deliver',
                    createdAt: template.createTime ? new Date(template.createTime).toLocaleDateString() : '',
                    updatedAt: template.createTime ? new Date(template.createTime).toLocaleDateString() : ''
                }));

                setTemplates(mappedTemplates);
            } else {
                console.error('Invalid response format:', result);
                setTemplates([]);
            }
        } catch (err) {
            console.error('Error fetching template list:', err);
            setTemplates([]);
        } finally {
            setLoading(false);
        }
    };

    // 获取接口列表
    useEffect(() => {
        fetchApiList();
    }, []);

    // 从后端获取数据源列表
    const fetchApiList = async () => {
        try {
            setApiLoading(true);
            const requestData = {
                pageNum: 1,
                pageSize: 1000, // 获取所有数据源
                name: '',
                invokeType: '',
                useType: 'fetchData' // 只查询fetchData类型的数据源
            };

            const result = await apiRequest(API_CONFIG.TASK_INVOKE_METHOD.LIST, {
                method: 'POST',
                body: JSON.stringify(requestData)
            });

            if (result.success && result.data) {
                setApiList(result.data.list || []);
            } else {
                console.error('获取数据源列表失败:', result.msg);
                setApiList([]);
            }
        } catch (err) {
            console.error('Error fetching data source list:', err);
            setApiList([]);
        } finally {
            setApiLoading(false);
        }
    };

    // 选择模板
    const handleSelectTemplate = (template) => {
        setSelectedTemplate(template);
        setIsEditing(false);
        setIsEditingMethod(false);
        setTestResult(null); // 清空测试结果
        // 清理图表实例
        if (chartInstance) {
            chartInstance.dispose();
            setChartInstance(null);
        }
    };

    // 创建新模板
    const handleCreateNew = () => {
        setFormData({
            name: '',
            description: '',
            content: '',
            type: 'text',
            methodId: '',
            subBizType: 'summary'
        });
        setSelectedTemplate(null);
        setIsEditing(true);
    };

    // 编辑模板
    const handleEdit = () => {
        if (!selectedTemplate) return;

        setFormData({
            name: selectedTemplate.name,
            description: selectedTemplate.description,
            content: selectedTemplate.content,
            type: selectedTemplate.type,
            methodId: selectedTemplate.methodId || '',
            subBizType: selectedTemplate.subBizType || 'summary',
            bizType: selectedTemplate.bizType || 'deliver'
        });
        setIsEditing(true);
    };

    // 取消编辑
    const handleCancel = () => {
        setIsEditing(false);
        setIsEditingMethod(false);
        if (selectedTemplate) {
            // 如果是编辑现有模板，恢复选中状态
            setSelectedTemplate(selectedTemplate);
        }
    };

    // 开始编辑数据源
    const handleEditMethod = () => {
        if (!selectedTemplate) return;
        setIsEditingMethod(true);
        // 将当前模板的methodId设置到formData中
        setFormData(prev => ({
            ...prev,
            methodId: selectedTemplate.methodId || ''
        }));
    };

    // 取消编辑数据源
    const handleCancelEditMethod = () => {
        setIsEditingMethod(false);
        // 恢复原来的methodId
        setFormData(prev => ({
            ...prev,
            methodId: selectedTemplate.methodId || ''
        }));
    };

    // 保存数据源
    const handleSaveMethod = async () => {
        if (!selectedTemplate) {
            window.showMessage.warning('请先选择一个模板');
            return;
        }

        try {
            // 构建要保存的模板数据，只更新数据源
            const templateData = {
                id: selectedTemplate.id,
                title: selectedTemplate.name,
                remark: selectedTemplate.description,
                prompt: selectedTemplate.content,
                methodId: formData.methodId,
                subBizType: selectedTemplate.subBizType,
                bizType: formData.bizType
            };

            const result = await apiRequest(API_CONFIG.TEMPLATE.SAVE, {
                body: JSON.stringify(templateData)
            });

            if (result.success && result.data) {
                // 将后端返回的数据转换为前端格式
                const savedTemplate = {
                    id: result.data.id,
                    name: result.data.title || '',
                    description: result.data.remark || '',
                    content: result.data.prompt || '',
                    type: 'text',
                    methodId: result.data.methodId || '',
                    subBizType: result.data.subBizType || 'summary',
                    bizType: result.data.bizType || 'deliver',
                    createdAt: result.data.createTime ? new Date(result.data.createTime).toLocaleDateString() : '',
                    updatedAt: result.data.createTime ? new Date(result.data.createTime).toLocaleDateString() : ''
                };

                // 重新加载模板列表以确保数据同步
                await fetchTemplateList();

                // 设置选中的模板
                setSelectedTemplate(savedTemplate);
                setIsEditingMethod(false);
                window.showMessage.success('数据源保存成功！');
            } else {
                throw new Error(result.msg || '保存失败');
            }
        } catch (error) {
            console.error('保存数据源失败:', error);
            window.showMessage.error('保存数据源失败: ' + error.message);
        }
    };
    // 测试模板
    const handleTest = async () => {
        // 兼容新建和详情：优先用 formData，若无则用 selectedTemplate
        const methodId = formData.methodId || (selectedTemplate && selectedTemplate.methodId);
        let businessPrompt = formData.content || (selectedTemplate && selectedTemplate.content);
        if (!methodId) return;
        
        // 添加cycleType到businessPrompt
        if (businessPrompt) {
            businessPrompt = businessPrompt.replace(/\b(day|week|month)\b/g, cycleType);
        }
        
        setTestResult("");
        try {
            const response = await streamApiRequest(API_CONFIG.TEMPLATE.TEST, {
                body: JSON.stringify({
                    methodId,
                    businessPrompt,
                    cycleType: cycleType, // 添加cycleType参数
                    resultType: resultType // 添加resultType参数
                })
            });

            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let resultText = '';

            while (true) {
                const { done, value } = await reader.read();
                if (done) {
                    break;
                }
                const chunk = decoder.decode(value, { stream: true });
                resultText += chunk;
                setTestResult(prev => prev + chunk); // 追加方式，保留格式
            }

            // 如果是图表类型，尝试渲染图表
            if (resultType === 'chart' && resultText.trim()) {
                setTimeout(() => {
                    renderChart(resultText);
                }, 100);
            }
        } catch (error) {
            console.error('测试失败:', error);
            window.showMessage.error(`测试失败: ${error.message}`);
        }
    };

    // 渲染图表
    const renderChart = (resultText) => {
        try {
            // 清理之前的图表实例
            if (chartInstance) {
                chartInstance.dispose();
            }

            // 尝试解析JSON配置
            let chartOption;
            try {
                // 尝试直接解析
                chartOption = JSON.parse(resultText);
            } catch (e) {
                // 如果直接解析失败，尝试提取JSON部分
                const jsonMatch = resultText.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    chartOption = JSON.parse(jsonMatch[0]);
                } else {
                    throw new Error('无法解析图表配置');
                }
            }

            // 获取图表容器
            const chartContainer = document.getElementById('chart-container');
            if (!chartContainer) {
                console.error('Chart container not found');
                return;
            }

            // 初始化图表
            const chart = echarts.init(chartContainer);
            chart.setOption(chartOption);
            setChartInstance(chart);

            // 监听窗口大小变化
            const resizeHandler = () => chart.resize();
            window.addEventListener('resize', resizeHandler);

        } catch (error) {
            console.error('图表渲染失败:', error);
            window.showMessage.error('图表渲染失败，请检查返回的数据格式');
        }
    };

    // 处理表单输入变化
    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    // 保存模板
    const handleSave = async () => {
        // 验证表单
        if (!formData.name.trim() || !formData.content.trim()) {
            window.showMessage.warning('模板名称和内容不能为空');
            return;
        }

        try {
            // 构建要保存的模板数据，映射前端字段到后端字段
            const templateData = {
                id: selectedTemplate ? selectedTemplate.id : null,
                title: formData.name,
                remark: formData.description,
                prompt: formData.content,
                methodId: formData.methodId,
                subBizType: formData.subBizType,
                bizType: formData.bizType
            };

            const result = await apiRequest(API_CONFIG.TEMPLATE.SAVE, {
                body: JSON.stringify(templateData)
            });

            if (result.success && result.data) {
                // 将后端返回的数据转换为前端格式
                const savedTemplate = {
                    id: result.data.id,
                    name: result.data.title || '',
                    description: result.data.remark || '',
                    content: result.data.prompt || '',
                    type: 'text',
                    methodId: result.data.methodId || '',
                    subBizType: result.data.subBizType || 'summary',
                    bizType: result.data.bizType || 'deliver',
                    createdAt: result.data.createTime ? new Date(result.data.createTime).toLocaleDateString() : '',
                    updatedAt: result.data.createTime ? new Date(result.data.createTime).toLocaleDateString() : ''
                };

                // 重新加载模板列表以确保数据同步
                await fetchTemplateList();

                // 设置选中的模板
                setSelectedTemplate(savedTemplate);
                setIsEditing(false);
                window.showMessage.success('模板保存成功！');
            } else {
                throw new Error(result.msg || '保存失败');
            }
        } catch (error) {
            console.error('保存模板失败:', error);
            window.showMessage.error('保存模板失败: ' + error.message);
        }
    };

    // 保存提示词
    const handleSavePrompt = async () => {
        if (!selectedTemplate) {
            window.showMessage.warning('请先选择一个模板');
            return;
        }

        if (!formData.content.trim()) {
            window.showMessage.warning('提示词内容不能为空');
            return;
        }

        try {
            // 构建要保存的模板数据，只更新提示词内容
            const templateData = {
                id: selectedTemplate.id,
                title: selectedTemplate.name,
                remark: selectedTemplate.description,
                prompt: formData.content,
                methodId: selectedTemplate.methodId,
                subBizType: selectedTemplate.subBizType,
                bizType: selectedTemplate.bizType || 'deliver'
            };

            const result = await apiRequest(API_CONFIG.TEMPLATE.SAVE, {
                body: JSON.stringify(templateData)
            });

            if (result.success && result.data) {
                // 将后端返回的数据转换为前端格式
                const savedTemplate = {
                    id: result.data.id,
                    name: result.data.title || '',
                    description: result.data.remark || '',
                    content: result.data.prompt || '',
                    type: 'text',
                    methodId: result.data.methodId || '',
                    subBizType: result.data.subBizType || 'summary',
                    bizType: result.data.bizType || 'deliver',
                    createdAt: result.data.createTime ? new Date(result.data.createTime).toLocaleDateString() : '',
                    updatedAt: result.data.createTime ? new Date(result.data.createTime).toLocaleDateString() : ''
                };

                // 重新加载模板列表以确保数据同步
                await fetchTemplateList();

                // 设置选中的模板
                setSelectedTemplate(savedTemplate);
                window.showMessage.success('提示词保存成功！');
            } else {
                throw new Error(result.msg || '保存失败');
            }
        } catch (error) {
            console.error('保存提示词失败:', error);
            window.showMessage.error('保存提示词失败: ' + error.message);
        }
    };

    // 删除模板
    const handleDelete = async () => {
        if (!selectedTemplate) return;

        const confirmed = await window.showConfirm(`确定要删除模板 "${selectedTemplate.name}" 吗？`);
        if (confirmed) {
            try {
                const result = await apiRequest(API_CONFIG.TEMPLATE.DELETE, {}, { id: selectedTemplate.id });

                if (result.success) {
                    // 重新加载模板列表以确保数据同步
                    await fetchTemplateList();
                    setSelectedTemplate(null);
                    window.showMessage.success('模板删除成功！');
                } else {
                    throw new Error(result.msg || '删除失败');
                }
            } catch (error) {
                console.error('删除模板失败:', error);
                window.showMessage.error('删除模板失败: ' + error.message);
            }
        }
    };

    // 渲染模板详情及测试区
    const renderTemplateDetailAndTest = () => {
        if (!selectedTemplate) {
            return (
                <div className="flex flex-col items-center justify-center h-full text-gray-500">
                    <svg className="w-16 h-16 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <p>选择一个模板查看详情</p>
                </div>
            );
        }
        // 查找关联的数据源
        const associatedMethod = apiList.find(method => method.id === selectedTemplate.methodId);
        return (
            <div className="h-full flex flex-col">
                <div className="flex justify-between items-center mb-4">
                    <h3 className="text-xl font-semibold">{selectedTemplate.name}</h3>
                    <div className="flex space-x-2">
                        <button
                            onClick={handleEdit}
                            className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
                        >
                            编辑模板
                        </button>
                        <button
                            onClick={handleDelete}
                            className="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600"
                        >
                            删除
                        </button>
                    </div>
                </div>
                <div className="text-sm text-gray-500 mb-2">
                    <p>{selectedTemplate.description}</p>
                    <p className="mt-1">
                        <span className="font-medium">业务类型: </span>
                        {selectedTemplate.bizType === 'deliver' ? '交付' :
                         selectedTemplate.bizType === 'sale' ? '销售' :
                         selectedTemplate.bizType === 'customer_service' ? '客户服务' :
                         selectedTemplate.bizType || '未设置'}
                    </p>

                    {/* 数据源显示/编辑区域 */}
                    <div className="mt-2 flex items-center">
                        <span>数据源: </span>
                        {isEditingMethod ? (
                            <div className="flex items-center space-x-2 ml-2">
                                <select
                                    name="methodId"
                                    value={formData.methodId}
                                    onChange={handleInputChange}
                                    className="px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                                >
                                    <option value="">选择数据源</option>
                                    {apiList.map(method => (
                                        <option key={method.id} value={method.id}>
                                            {method.name} - {method.invokeType}
                                        </option>
                                    ))}
                                </select>
                                <button
                                    onClick={handleSaveMethod}
                                    className="px-2 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600"
                                >
                                    保存
                                </button>
                                <button
                                    onClick={handleCancelEditMethod}
                                    className="px-2 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600"
                                >
                                    取消
                                </button>
                            </div>
                        ) : (
                            <div className="flex items-center ml-2">
                                {associatedMethod ? (
                                    <span className="font-medium">{associatedMethod.name} - {associatedMethod.description}</span>
                                ) : (
                                    <span className="text-gray-400">未关联数据源</span>
                                )}
                                <button
                                    onClick={handleEditMethod}
                                    className="ml-2 px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600"
                                >
                                    编辑
                                </button>
                            </div>
                        )}
                    </div>

                    <p className="mt-1">子业务域: <span className="font-medium">{selectedTemplate.subBizType}</span></p>
                    <p className="mt-1">创建于: {selectedTemplate.createdAt} | 更新于: {selectedTemplate.updatedAt}</p>
                </div>
                
                <div className="mb-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">业务提示词（可编辑）:</label>
                    <textarea
                        className="w-full h-60 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono mb-2"
                        value={formData.content}
                        onChange={e => setFormData(prev => ({ ...prev, content: e.target.value }))}
                    />
                </div>
                <div className="flex items-center space-x-2 mb-4">
                    {/* 结果类型选择 */}
                    <div className="relative inline-block text-left">
                        <button
                            type="button"
                            onClick={() => setShowResultTypeDropdown(!showResultTypeDropdown)}
                            className="inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                            {resultType === 'text' ? '文本' : '图表'}
                            <svg className="ml-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                            </svg>
                        </button>
                        {showResultTypeDropdown && (
                            <div className="origin-top-left absolute left-0 mt-2 w-24 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-10">
                                <div className="py-1">
                                    <button
                                        onClick={() => {
                                            setResultType('text');
                                            setShowResultTypeDropdown(false);
                                        }}
                                        className={`block w-full text-left px-4 py-2 text-sm ${resultType === 'text' ? 'bg-gray-100 text-gray-900' : 'text-gray-700'}`}
                                    >
                                        文本
                                    </button>
                                    <button
                                        onClick={() => {
                                            setResultType('chart');
                                            setShowResultTypeDropdown(false);
                                        }}
                                        className={`block w-full text-left px-4 py-2 text-sm ${resultType === 'chart' ? 'bg-gray-100 text-gray-900' : 'text-gray-700'}`}
                                    >
                                        图表
                                    </button>
                                </div>
                            </div>
                        )}
                    </div>

                    <div className="relative inline-block text-left">
                        <div className="flex">
                            <button
                                type="button"
                                onClick={handleTest}
                                className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-l-md text-white bg-green-500 hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                            >
                                测试 ({cycleType === 'day' ? '日' : cycleType === 'week' ? '周' : '月'})
                            </button>
                            <button
                                type="button"
                                onClick={() => setShowCycleDropdown(!showCycleDropdown)}
                                className="inline-flex items-center px-2 py-2 border border-transparent text-sm font-medium rounded-r-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                                aria-expanded="true"
                                aria-haspopup="true"
                            >
                                <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                                </svg>
                            </button>
                        </div>
                        {showCycleDropdown && (
                            <div className="origin-top-right absolute right-0 mt-2 w-24 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-10" role="menu" aria-orientation="vertical">
                                <div className="py-1" role="none">
                                    <button
                                        onClick={() => {
                                            setCycleType('day');
                                            setShowCycleDropdown(false);
                                        }}
                                        className={`block w-full text-left px-4 py-2 text-sm ${cycleType === 'day' ? 'bg-gray-100 text-gray-900' : 'text-gray-700'}`}
                                        role="menuitem"
                                    >
                                        日
                                    </button>
                                    <button
                                        onClick={() => {
                                            setCycleType('week');
                                            setShowCycleDropdown(false);
                                        }}
                                        className={`block w-full text-left px-4 py-2 text-sm ${cycleType === 'week' ? 'bg-gray-100 text-gray-900' : 'text-gray-700'}`}
                                        role="menuitem"
                                    >
                                        周
                                    </button>
                                    <button
                                        onClick={() => {
                                            setCycleType('month');
                                            setShowCycleDropdown(false);
                                        }}
                                        className={`block w-full text-left px-4 py-2 text-sm ${cycleType === 'month' ? 'bg-gray-100 text-gray-900' : 'text-gray-700'}`}
                                        role="menuitem"
                                    >
                                        月
                                    </button>
                                </div>
                            </div>
                        )}
                    </div>

                    {/* 保存提示词按钮 */}
                    <button
                        type="button"
                        onClick={handleSavePrompt}
                        className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-500 hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                        保存提示词
                    </button>
                </div>
                {testResult && (
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">测试结果:</label>
                        {resultType === 'chart' ? (
                            <div className="w-full border border-gray-300 rounded-md bg-gray-50">
                                <div id="chart-container" style={{width: '100%', height: '400px'}}></div>
                                <details className="p-2 border-t border-gray-200">
                                    <summary className="cursor-pointer text-sm text-gray-600">查看原始数据</summary>
                                    <pre className="mt-2 p-2 bg-white rounded text-xs overflow-auto" style={{maxHeight: '150px'}}>
                                        {testResult}
                                    </pre>
                                </details>
                            </div>
                        ) : (
                            <pre className="w-full p-2 border border-gray-300 rounded-md bg-gray-50 overflow-auto text-sm" style={{maxHeight: '200px'}}>
                                {testResult}
                            </pre>
                        )}
                    </div>
                )}
            </div>
        );
    };

    // 渲染创建/编辑表单
    const renderTemplateForm = () => (
        <div className="h-full flex flex-col">
            <h3 className="text-xl font-semibold mb-4">{selectedTemplate ? '编辑模板' : '创建新模板'}</h3>
            <div className="space-y-4 flex-1">
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">模板名称</label>
                    <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="输入模板名称"
                    />
                </div>
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">描述</label>
                    <input
                        type="text"
                        name="description"
                        value={formData.description}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="输入模板描述"
                    />
                </div>
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">业务类型</label>
                    <select
                        name="bizType"
                        value={formData.bizType}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        <option value="deliver">交付</option>
                        <option value="sale">销售</option>
                        <option value="customer_service">客户服务</option>
                    </select>
                </div>
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">子业务域</label>
                    <select
                        name="subBizType"
                        value={formData.subBizType}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        <option value="summary">汇总</option>
                        <option value="product">产品</option>
                        <option value="area">区域</option>
                        <option value="team">团队</option>
                        <option value="customer">客户</option>
                    </select>
                </div>
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">数据源</label>
                    <select
                        name="methodId"
                        value={formData.methodId}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        <option value="">选择数据源</option>
                        {apiList.map(method => (
                            <option key={method.id} value={method.id}>
                                {method.name} - {method.invokeType}
                            </option>
                        ))}
                    </select>
                </div>
                <div className="flex-1 flex flex-col">
                    <label className="block text-sm font-medium text-gray-700 mb-1">业务提示词</label>
                    <textarea
                        name="content"
                        value={formData.content}
                        onChange={handleInputChange}
                        className="flex-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono"
                        placeholder="输入业务提示词"
                    />
                </div>
                <div className="flex justify-end space-x-3 pt-4">
                    <button
                        onClick={handleCancel}
                        className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                    >
                        取消
                    </button>
                    <button
                        onClick={handleSave}
                        className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                    >
                        保存
                    </button>
                </div>
            </div>
        </div>
    );

    // 主渲染函数
    return (
        <div className="h-full flex flex-col text-gray-800">
            <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold">模板管理</h2>
                {!isEditing && (
                    <button 
                        onClick={handleCreateNew}
                        className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                    >
                        创建新模板
                    </button>
                )}
            </div>
            {loading ? (
                <div className="flex justify-center items-center h-64">
                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                </div>
            ) : (
                <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-6 h-full">
                    {/* 模板列表 */}
                    <div className="bg-white bg-opacity-80 rounded-xl shadow-sm overflow-hidden">
                        <div className="p-4 border-b border-gray-200">
                            <h3 className="font-medium">模板列表</h3>
                        </div>
                        <div className="overflow-y-auto" style={{ maxHeight: 'calc(100vh - 280px)' }}>
                            {templates.length === 0 ? (
                                <div className="p-4 text-center text-gray-500">
                                    暂无模板，点击"创建新模板"按钮添加
                                </div>
                            ) : (
                                <ul className="divide-y divide-gray-200">
                                    {templates.map(template => (
                                        <li 
                                            key={template.id}
                                            className={`p-4 cursor-pointer hover:bg-gray-50 transition-colors ${selectedTemplate && selectedTemplate.id === template.id ? 'bg-blue-50' : ''}`}
                                            onClick={() => {
                                                setSelectedTemplate(template);
                                                setFormData({
                                                    name: template.name,
                                                    description: template.description,
                                                    content: template.content,
                                                    type: template.type,
                                                    methodId: template.methodId,
                                                    subBizType: template.subBizType
                                                });
                                                setIsEditing(false);
                                                setIsEditingMethod(false);
                                                setTestResult("");
                                            }}
                                        >
                                            <h4 className="font-medium">{template.name}</h4>
                                            <p className="text-sm text-gray-500 mt-1 truncate">{template.description}</p>
                                            <p className="text-xs text-gray-400 mt-1">更新于: {template.updatedAt}</p>
                                        </li>
                                    ))}
                                </ul>
                            )}
                        </div>
                    </div>
                    {/* 模板详情及测试区 */}
                    <div className="bg-white bg-opacity-80 rounded-xl shadow-sm overflow-hidden md:col-span-2 p-6">
                        {isEditing ? renderTemplateForm() : renderTemplateDetailAndTest()}
                    </div>
                </div>
            )}
        </div>
    );
}