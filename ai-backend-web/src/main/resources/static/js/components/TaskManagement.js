// TaskManagement.js - 任务管理组件
const { useState, useEffect } = React;
function TaskManagement() {
    // 状态管理
    const [tasks, setTasks] = useState([]);
    const [templates, setTemplates] = useState([]);
    const [loading, setLoading] = useState(true);
    const [templatesLoading, setTemplatesLoading] = useState(false);
    const [selectedTask, setSelectedTask] = useState(null);
    const [isEditing, setIsEditing] = useState(false);
    const [formData, setFormData] = useState({
        title: '',
        remark: '',
        templateId: '',
        cornExp: '0 0 9 * * ?',  // 默认每天9点执行
        cycleType: '',
        methodId: '',
        reten: 0,  // 是否保留同一周期的数据,0不保留,1保留
        coverRepeat: 0  // 覆盖重复数据，默认0
    });

    // 新增状态：多模板选择相关
    const [selectedTemplateIds, setSelectedTemplateIds] = useState([]);
    const [cronGenerating, setCronGenerating] = useState(false);
    const [naturalLanguageInput, setNaturalLanguageInput] = useState('');
    const [cronValidationError, setCronValidationError] = useState('');
    const [taskRunning, setTaskRunning] = useState(false);
    const [quickRunningTasks, setQuickRunningTasks] = useState(new Set());
    const [debuggingTasks, setDebuggingTasks] = useState(new Set());
    const [reanalyzingBatches, setReanalyzingBatches] = useState(new Set());
    const [runRecords, setRunRecords] = useState([]);
    const [loadingRunRecords, setLoadingRunRecords] = useState(false);
    const [runRecordsPage, setRunRecordsPage] = useState({ current: 1, size: 20, total: 0 });

    // Tab相关状态
    const [activeTab, setActiveTab] = useState('execRecords'); // 'taskDetail' | 'execRecords'
    const [selectedRecord, setSelectedRecord] = useState(null);
    const [taskResultDetails, setTaskResultDetails] = useState(null);
    const [loadingTaskResults, setLoadingTaskResults] = useState(false);
    const [activeSubBizType, setActiveSubBizType] = useState('');
    const [refreshInterval, setRefreshInterval] = useState(null);

    // 新增状态：参数选择相关
    const [toolsData, setToolsData] = useState([]);
    const [toolsLoading, setToolsLoading] = useState(false);
    const [selectedMethodId, setSelectedMethodId] = useState('');

    // 获取任务列表
    useEffect(() => {
        fetchTaskList();
        fetchTemplateList();
        fetchToolsData();
    }, []);

    // 从后端获取任务列表
    const fetchTaskList = async () => {
        try {
            setLoading(true);
            const result = await apiRequest(API_CONFIG.TASK.LIST);

            // 检查响应格式
            if (result.success && result.data) {
                // 将后端数据格式转换为前端格式
                const mappedTasks = result.data.map(task => ({
                    id: task.id,
                    title: task.title || '',
                    remark: task.remark || '',
                    templateId: task.templateId || '',
                    cornExp: task.cornExp || '',
                    cycleType: task.cycleType || '',
                    methodId: task.methodId || '',
                    reten: task.reten || 0,
                    coverRepeat: task.coverRepeat || 0,
                    userId: task.userId || '',
                    createdAt: task.createTime ? new Date(task.createTime).toLocaleDateString() : '',
                    updatedAt: task.createTime ? new Date(task.createTime).toLocaleDateString() : ''
                }));

                setTasks(mappedTasks);
            } else {
                console.error('Invalid response format:', result);
                setTasks([]);
            }
        } catch (err) {
            console.error('Error fetching task list:', err);
            setTasks([]);
            window.showMessage.error('获取任务列表失败: ' + err.message);
        } finally {
            setLoading(false);
        }
    };

    // 从后端获取模板列表
    const fetchTemplateList = async () => {
        try {
            setTemplatesLoading(true);
            const result = await apiRequest(API_CONFIG.TEMPLATE.LIST);
            if (result.success && result.data) {
                setTemplates(result.data);
            } else {
                console.error('Invalid template response format:', result);
                setTemplates([]);
            }
        } catch (err) {
            console.error('Error fetching template list:', err);
            setTemplates([]);
        } finally {
            setTemplatesLoading(false);
        }
    };

    // 获取参数数据
    const fetchToolsData = async () => {
        try {
            setToolsLoading(true);
            const requestData = {
                pageNum: 1,
                pageSize: 1000, // 获取所有参数
                name: '',
                invokeType: '',
                useType: 'parameter' // 只查询parameter类型的参数
            };

            const result = await apiRequest(API_CONFIG.TASK_INVOKE_METHOD.LIST, {
                method: 'POST',
                body: JSON.stringify(requestData)
            });

            if (result.success && result.data) {
                setToolsData(result.data.list || []);
            } else {
                console.error('获取参数列表失败:', result.msg);
                setToolsData([]);
            }
        } catch (error) {
            console.error('获取参数数据失败:', error);
            setToolsData([]);
        } finally {
            setToolsLoading(false);
        }
    };

    // 分页获取任务执行记录
    const fetchRunRecords = async (taskId, pageNum = 1, pageSize = 20) => {
        try {
            setLoadingRunRecords(true);
            // 构建带参数的URL
            const url = API_CONFIG.TASK.RUN_RECORDS.replace('{taskId}', taskId) + `?pageNum=${pageNum}&pageSize=${pageSize}`;
            const result = await apiRequest(url, { method: 'POST' });

            if (result.success && result.data) {
                setRunRecords(result.data.list || []);
                setRunRecordsPage({
                    current: pageNum,
                    size: pageSize,
                    total: result.data.total || 0
                });
            } else {
                console.error('获取执行记录失败:', result.msg);
                setRunRecords([]);
                setRunRecordsPage({ current: 1, size: 20, total: 0 });
            }
        } catch (error) {
            console.error('获取执行记录失败:', error);
            setRunRecords([]);
            setRunRecordsPage({ current: 1, size: 20, total: 0 });
        } finally {
            setLoadingRunRecords(false);
        }
    };

    // 获取任务结果详情
    const fetchTaskResultDetails = async (taskId, batchNo) => {
        try {
            setLoadingTaskResults(true);
            const result = await apiRequest(API_CONFIG.TASK.TASK_RESULTS, { method: 'POST' }, { taskId, batchNo });

            if (result.success && result.data) {
                setTaskResultDetails(result.data);
                // 设置默认的sub_biz_type
                const subBizTypes = Object.keys(result.data.itemsBySubBizType || {});
                if (subBizTypes.length > 0) {
                    setActiveSubBizType(subBizTypes[0]);
                }
            } else {
                console.error('获取任务结果详情失败:', result.msg);
                setTaskResultDetails(null);
            }
        } catch (error) {
            console.error('获取任务结果详情失败:', error);
            setTaskResultDetails(null);
        } finally {
            setLoadingTaskResults(false);
        }
    };

    // 选择任务
    const handleSelectTask = (task) => {
        setSelectedTask(task);
        setIsEditing(false);
        setActiveTab('execRecords'); // 默认打开执行记录tab
        setSelectedRecord(null);
        setTaskResultDetails(null);

        // 清理定时器
        if (refreshInterval) {
            clearInterval(refreshInterval);
            setRefreshInterval(null);
        }

        setFormData({
            title: task.title,
            remark: task.remark,
            templateId: task.templateId,
            cornExp: task.cornExp,
            cycleType: task.cycleType,
            methodId: task.methodId,
            reten: task.reten || 0,
            coverRepeat: task.coverRepeat || 0
        });

        // 解析多个模板ID（逗号分隔）
        const templateIds = task.templateId ? task.templateId.split(',').map(id => id.trim()).filter(id => id) : [];
        setSelectedTemplateIds(templateIds);

        // 设置选中的参数ID
        setSelectedMethodId(task.methodId || '');

        // 获取执行记录
        fetchRunRecords(task.id);
    };

    // 创建新任务
    const handleCreateNew = () => {
        setFormData({
            title: '',
            remark: '',
            templateId: '',
            cornExp: '0 0 9 * * ?',
            cycleType: '',
            methodId: '',
            reten: 0,
            coverRepeat: 0
        });
        setSelectedTask(null);
        setIsEditing(true);
        setSelectedMethodId('');
        setSelectedTemplateIds([]);
    };

    // 编辑任务
    const handleEdit = () => {
        if (!selectedTask) return;

        setFormData({
            title: selectedTask.title,
            remark: selectedTask.remark,
            templateId: selectedTask.templateId,
            cornExp: selectedTask.cornExp,
            cycleType: selectedTask.cycleType,
            methodId: selectedTask.methodId,
            reten: selectedTask.reten || 0,
            coverRepeat: selectedTask.coverRepeat || 0
        });
        setIsEditing(true);

        // 解析多个模板ID（逗号分隔）
        const templateIds = selectedTask.templateId ? selectedTask.templateId.split(',').map(id => id.trim()).filter(id => id) : [];
        setSelectedTemplateIds(templateIds);

        // 设置选中的参数ID
        setSelectedMethodId(selectedTask.methodId || '');
    };

    // 取消编辑
    const handleCancel = () => {
        setIsEditing(false);
        if (selectedTask) {
            // 如果是编辑现有任务，恢复选中状态
            setSelectedTask(selectedTask);
        }
    };

    // 处理参数选择变化
    const handleMethodChange = (methodId) => {
        setSelectedMethodId(methodId);
    };

    // 处理模板选择变化
    const handleTemplateChange = (templateId) => {
        const currentIds = [...selectedTemplateIds];
        const index = currentIds.indexOf(templateId);

        if (index > -1) {
            // 如果已选中，则取消选择
            currentIds.splice(index, 1);
        } else {
            // 如果未选中，则添加选择
            currentIds.push(templateId);
        }

        setSelectedTemplateIds(currentIds);

        // 更新formData中的templateId字段（逗号分隔）
        const templateIdStr = currentIds.join(',');
        setFormData(prev => ({
            ...prev,
            templateId: templateIdStr
        }));

        // 如果是模板选择变化，清空参数选择
        setSelectedMethodId('');
    };

    // 处理表单输入变化
    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));

        // 如果是Cron表达式字段，进行实时校验
        if (name === 'cornExp') {
            const validationError = validateCronExpression(value);
            setCronValidationError(validationError || '');
        }
    };



    // 保存任务
    const handleSave = async () => {
        // 验证表单
        if (!formData.title.trim()) {
            window.showMessage.warning('任务标题不能为空');
            return;
        }

        if (selectedTemplateIds.length === 0) {
            window.showMessage.warning('请至少选择一个关联模板');
            return;
        }

        if (!formData.cornExp.trim()) {
            window.showMessage.warning('Cron表达式不能为空');
            return;
        }

        // 校验Cron表达式
        const cronValidationError = validateCronExpression(formData.cornExp);
        if (cronValidationError) {
            window.showMessage.warning(`Cron表达式格式错误: ${cronValidationError}`);
            setCronValidationError(cronValidationError);
            return;
        }

        try {
            // 保存选中的参数ID
            let methodIdValue = selectedMethodId || '';

            // 构建要保存的任务数据
            const taskData = {
                id: selectedTask ? selectedTask.id : null,
                title: formData.title,
                remark: formData.remark,
                templateId: selectedTemplateIds.join(','), // 多个模板ID用逗号分隔
                cornExp: formData.cornExp,
                cycleType: formData.cycleType,
                methodId: methodIdValue,
                reten: parseInt(formData.reten),
                coverRepeat: parseInt(formData.coverRepeat)
            };

            const result = await apiRequest(API_CONFIG.TASK.SAVE, {
                body: JSON.stringify(taskData)
            });

            if (result.success && result.data) {
                // 将后端返回的数据转换为前端格式
                const savedTask = {
                    id: result.data.id,
                    title: result.data.title || '',
                    remark: result.data.remark || '',
                    templateId: result.data.templateId || '',
                    cornExp: result.data.cornExp || '',
                    cycleType: result.data.cycleType || '',
                    methodId: result.data.methodId || '',
                    reten: result.data.reten || 0,
                    coverRepeat: result.data.coverRepeat || 0,
                    userId: result.data.userId || '',
                    createdAt: result.data.createTime ? new Date(result.data.createTime).toLocaleDateString() : '',
                    updatedAt: result.data.createTime ? new Date(result.data.createTime).toLocaleDateString() : ''
                };

                // 重新加载任务列表以确保数据同步
                await fetchTaskList();

                // 设置选中的任务
                setSelectedTask(savedTask);

                // 更新选中的模板ID列表
                const templateIds = savedTask.templateId ? savedTask.templateId.split(',').map(id => id.trim()).filter(id => id) : [];
                setSelectedTemplateIds(templateIds);

                setIsEditing(false);
                window.showMessage.success('任务保存成功！');
            } else {
                throw new Error(result.msg || '保存失败');
            }
        } catch (error) {
            console.error('保存任务失败:', error);
            window.showMessage.error('保存任务失败: ' + error.message);
        }
    };

    // 执行任务（带确认）
    const handleRun = async () => {
        if (!selectedTask) return;

        const confirmed = await window.showConfirm(`确定要立即执行任务 "${selectedTask.title}" 吗？`);
        if (confirmed) {
            try {
                setTaskRunning(true);
                await apiRequest(API_CONFIG.TASK.RUN, {}, { taskId: selectedTask.id });

                window.showMessage.success('任务执行成功！');
            } catch (error) {
                console.error('执行任务失败:', error);
                window.showMessage.error('执行任务失败: ' + error.message);
            } finally {
                setTaskRunning(false);
            }
        }
    };

    // 快捷执行任务（无确认）
    const handleQuickRun = async (task, event) => {
        // 阻止事件冒泡，避免选中任务
        event.stopPropagation();

        if (!task) return;

        try {
            setQuickRunningTasks(prev => new Set(prev).add(task.id));
            await apiRequest(API_CONFIG.TASK.RUN, {}, { taskId: task.id });

            window.showMessage.success(`任务 "${task.title}" 执行成功！`);
        } catch (error) {
            console.error('执行任务失败:', error);
            window.showMessage.error(`执行任务 "${task.title}" 失败: ` + error.message);
        } finally {
            setQuickRunningTasks(prev => {
                const newSet = new Set(prev);
                newSet.delete(task.id);
                return newSet;
            });
        }
    };

    // Debug任务
    const handleDebug = async (task, event) => {
        // 阻止事件冒泡，避免选中任务
        event.stopPropagation();

        if (!task) return;

        // 弹出输入框，默认值为1
        const count = 1;


        // 验证输入是否为正整数
        const countNum = parseInt(count);
        if (isNaN(countNum) || countNum <= 0) {
            window.showMessage.warning('请输入有效的正整数');
            return;
        }

        try {
            setDebuggingTasks(prev => new Set(prev).add(task.id));
            await apiRequest(API_CONFIG.TASK.DEBUG, {}, { taskId: task.id, count: countNum });

            window.showMessage.success(`任务 "${task.title}" Debug执行成功！(执行次数: ${countNum})`);
        } catch (error) {
            console.error('Debug任务失败:', error);
            window.showMessage.error(`Debug任务 "${task.title}" 失败: ` + error.message);
        } finally {
            setDebuggingTasks(prev => {
                const newSet = new Set(prev);
                newSet.delete(task.id);
                return newSet;
            });
        }
    };

    // 根据批次号重新分析任务
    const handleReanalyzeByBatch = async (taskId, batchNo, event) => {
        // 阻止事件冒泡
        if (event) {
            event.stopPropagation();
        }

        const confirmed = await window.showConfirm(`确定要重新分析批次 "${batchNo}" 的数据吗？\n\n这将根据该批次的原始数据重新执行分析功能，更新分析结果。`);
        if (!confirmed) return;

        const batchKey = `${taskId}_${batchNo}`;
        try {
            setReanalyzingBatches(prev => new Set(prev).add(batchKey));
            const result = await apiRequest(API_CONFIG.TASK.REANALYZE_BY_BATCH, {}, { taskId, batchNo });

            if (result.success !== false) {
                window.showMessage.success(`批次 "${batchNo}" 重新分析成功！\n${result.data || result.message || ''}`);
                // 重新加载执行记录
                fetchRunRecords(taskId);
            } else {
                throw new Error(result.msg || result.message || '重新分析失败');
            }
        } catch (error) {
            console.error('重新分析批次失败:', error);
            window.showMessage.error(`重新分析批次 "${batchNo}" 失败: ` + error.message);
        } finally {
            setReanalyzingBatches(prev => {
                const newSet = new Set(prev);
                newSet.delete(batchKey);
                return newSet;
            });
        }
    };

    // 删除任务
    const handleDelete = async () => {
        if (!selectedTask) return;

        const confirmed = await window.showConfirm(`确定要删除任务 "${selectedTask.title}" 吗？`);
        if (confirmed) {
            try {
                const result = await apiRequest(API_CONFIG.TASK.DELETE, {}, { id: selectedTask.id });

                // 如果请求没有抛出异常，则认为删除成功
                if (result.success !== false) {
                    // 重新加载任务列表以确保数据同步
                    await fetchTaskList();
                    setSelectedTask(null);
                    window.showMessage.success('任务删除成功！');
                } else {
                    throw new Error(result.msg || '删除失败');
                }
            } catch (error) {
                console.error('删除任务失败:', error);
                window.showMessage.error('删除任务失败: ' + error.message);
            }
        }
    };

    // 处理执行记录点击
    const handleRecordClick = (record) => {
        setSelectedRecord(record);
        fetchTaskResultDetails(selectedTask.id, record.batchNo);

        // 如果任务还在运行中，启动定时刷新
        if (record.execStatus === 'RUNNING') {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
            const interval = setInterval(() => {
                fetchTaskResultDetails(selectedTask.id, record.batchNo);
                // 同时刷新执行记录状态
                fetchRunRecords(selectedTask.id, runRecordsPage.current, runRecordsPage.size);
            }, 10000); // 10秒刷新一次
            setRefreshInterval(interval);
        } else {
            // 清理定时器
            if (refreshInterval) {
                clearInterval(refreshInterval);
                setRefreshInterval(null);
            }
        }
    };

    // 处理分页变化
    const handlePageChange = (pageNum) => {
        fetchRunRecords(selectedTask.id, pageNum, runRecordsPage.size);
    };

    // 组件卸载时清理定时器
    useEffect(() => {
        return () => {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        };
    }, [refreshInterval]);

    // 获取模板名称（支持多个模板ID，逗号分隔）
    const getTemplateName = (templateId) => {
        if (!templateId) return '未选择模板';

        const templateIds = templateId.split(',').map(id => id.trim()).filter(id => id);
        const templateNames = templateIds.map(id => {
            const template = templates.find(t => t.id.toString() === id);
            return template ? template.title : `未知模板(${id})`;
        });

        return templateNames.join(', ');
    };

    // 常用Cron表达式预设
    const cronPresets = [
        { label: '每天9点', value: '0 0 9 * * ?' },
        { label: '每天18点', value: '0 0 18 * * ?' },
        { label: '每周一9点', value: '0 0 9 ? * MON' },
        { label: '每月1号9点', value: '0 0 9 1 * ?' },
        { label: '每小时', value: '0 0 * * * ?' },
        { label: '每30分钟', value: '0 */30 * * * ?' }
    ];

    // Cron表达式校验函数
    const validateCronExpression = (cronExp) => {
        if (!cronExp || !cronExp.trim()) {
            return 'Cron表达式不能为空';
        }

        const parts = cronExp.trim().split(/\s+/);
        if (parts.length !== 6) {
            return 'Cron表达式必须包含6个字段：秒 分 时 日 月 周';
        }

        // 基本格式校验
        const [second, minute, hour, day, month, week] = parts;

        // 秒校验 (0-59)
        if (!isValidCronField(second, 0, 59)) {
            return '秒字段无效，应为0-59或*或*/n格式';
        }

        // 分钟校验 (0-59)
        if (!isValidCronField(minute, 0, 59)) {
            return '分钟字段无效，应为0-59或*或*/n格式';
        }

        // 小时校验 (0-23)
        if (!isValidCronField(hour, 0, 23)) {
            return '小时字段无效，应为0-23或*或*/n格式';
        }

        // 日期校验 (1-31 或 ?)
        if (day !== '?' && !isValidCronField(day, 1, 31)) {
            return '日期字段无效，应为1-31或?或*或*/n格式';
        }

        // 月份校验 (1-12)
        if (!isValidCronField(month, 1, 12)) {
            return '月份字段无效，应为1-12或*或*/n格式';
        }

        // 周校验 (1-7 或 MON-SUN 或 ?)
        if (week !== '?' && !isValidWeekField(week)) {
            return '周字段无效，应为1-7或MON-SUN或?或*格式';
        }

        // 日期和周不能同时指定
        if (day !== '?' && week !== '?') {
            return '日期和周字段不能同时指定，其中一个必须为?';
        }

        return null; // 校验通过
    };

    // 校验Cron字段的辅助函数
    const isValidCronField = (field, min, max) => {
        if (field === '*') return true;
        if (field === '?') return true;

        // 处理 */n 格式
        if (field.includes('*/')) {
            const step = parseInt(field.split('*/')[1]);
            return !isNaN(step) && step > 0 && step <= max;
        }

        // 处理范围 n-m 格式
        if (field.includes('-')) {
            const [start, end] = field.split('-').map(n => parseInt(n));
            return !isNaN(start) && !isNaN(end) && start >= min && end <= max && start <= end;
        }

        // 处理列表 n,m,k 格式
        if (field.includes(',')) {
            const values = field.split(',').map(n => parseInt(n));
            return values.every(v => !isNaN(v) && v >= min && v <= max);
        }

        // 处理单个数字
        const num = parseInt(field);
        return !isNaN(num) && num >= min && num <= max;
    };

    // 校验周字段的辅助函数
    const isValidWeekField = (field) => {
        if (field === '*' || field === '?') return true;

        const weekNames = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];

        // 处理周名称
        if (weekNames.includes(field.toUpperCase())) return true;

        // 处理数字 (1-7)
        const num = parseInt(field);
        if (!isNaN(num) && num >= 1 && num <= 7) return true;

        // 处理范围和列表
        if (field.includes('-') || field.includes(',')) {
            return field.split(/[-,]/).every(part => {
                const trimmed = part.trim();
                return weekNames.includes(trimmed.toUpperCase()) ||
                       (!isNaN(parseInt(trimmed)) && parseInt(trimmed) >= 1 && parseInt(trimmed) <= 7);
            });
        }

        return false;
    };

    // 智能生成Cron表达式
    const generateCronExpression = async () => {
        if (!naturalLanguageInput.trim()) {
            window.showMessage.warning('请输入自然语言描述');
            return;
        }

        try {
            setCronGenerating(true);
            console.log('开始生成Cron表达式，输入:', naturalLanguageInput);

            const requestBody = {
                key: 'prompt_gene_corn',
                content: naturalLanguageInput
            };
            console.log('请求体:', requestBody);

            const response = await streamApiRequest(API_CONFIG.TASK.GENERATE_CRON, {
                body: JSON.stringify(requestBody)
            });

            console.log('响应状态:', response.status);
            console.log('响应头:', Object.fromEntries(response.headers.entries()));

            // 处理流式响应
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let generatedCron = '';
            let chunkCount = 0;

            console.log('开始读取流式响应...');

            while (true) {
                const { done, value } = await reader.read();
                if (done) {
                    console.log('流式响应读取完成，总共接收到', chunkCount, '个数据块');
                    break;
                }

                const chunk = decoder.decode(value, { stream: true });
                chunkCount++;
                console.log(`接收到第${chunkCount}个数据块:`, JSON.stringify(chunk));
                generatedCron += chunk;
            }

            console.log('完整的生成内容:', JSON.stringify(generatedCron));
            console.log('生成内容长度:', generatedCron.length);

            // 清理生成的Cron表达式
            let cleanedCron = generatedCron.trim();
            console.log('清理后的内容:', JSON.stringify(cleanedCron));

            // 尝试多种正则表达式来提取Cron表达式
            const cronPatterns = [
                // 标准6字段Cron表达式
                /(\d+\s+\d+\s+\d+\s+[*?\d]+\s+[*\d]+\s+[*?\w]+)/,
                // 更宽松的匹配
                /([0-9*?\/,-]+\s+[0-9*?\/,-]+\s+[0-9*?\/,-]+\s+[0-9*?\/,-]+\s+[0-9*?\/,-]+\s+[0-9*?\/,A-Z-]+)/,
                // 匹配包含问号的表达式
                /([0-9*?\/,-]+\s+[0-9*?\/,-]+\s+[0-9*?\/,-]+\s+[0-9*?\/,-?]+\s+[0-9*?\/,-]+\s+[0-9*?\/,A-Z-?]+)/
            ];

            let cronMatch = null;
            for (let i = 0; i < cronPatterns.length; i++) {
                cronMatch = cleanedCron.match(cronPatterns[i]);
                if (cronMatch) {
                    console.log(`使用第${i+1}个正则表达式匹配成功:`, cronMatch[1]);
                    cleanedCron = cronMatch[1];
                    break;
                }
            }

            if (!cronMatch) {
                console.log('未找到标准Cron表达式，尝试直接使用生成的内容');
                // 如果没有匹配到，尝试按行分割并找到看起来像Cron表达式的行
                const lines = cleanedCron.split('\n').map(line => line.trim()).filter(line => line);
                console.log('分割后的行:', lines);

                for (const line of lines) {
                    const parts = line.split(/\s+/);
                    if (parts.length === 6) {
                        console.log('找到6字段的行:', line);
                        cleanedCron = line;
                        break;
                    }
                }
            }

            console.log('最终的Cron表达式:', JSON.stringify(cleanedCron));

            // 校验生成的Cron表达式
            const validationError = validateCronExpression(cleanedCron);
            console.log('校验结果:', validationError || '校验通过');

            if (validationError) {
                console.error('生成的Cron表达式校验失败:', validationError);
                console.error('原始生成内容:', JSON.stringify(generatedCron));
                window.showMessage.warning(`生成的Cron表达式格式有误: ${validationError}\n生成的内容: ${cleanedCron}`);
                return;
            }

            // 更新表单数据
            setFormData(prev => ({ ...prev, cornExp: cleanedCron }));
            setCronValidationError('');
            setNaturalLanguageInput('');
            window.showMessage.success('Cron表达式生成成功！');

        } catch (error) {
            console.error('生成Cron表达式失败:', error);
            window.showMessage.error('生成Cron表达式失败: ' + error.message);
        } finally {
            setCronGenerating(false);
        }
    };

    // 渲染任务详情
    const renderTaskDetail = () => {
        if (!selectedTask) {
            return (
                <div className="flex flex-col items-center justify-center h-full text-gray-500">
                    <svg className="w-16 h-16 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                    </svg>
                    <p>选择一个任务查看详情</p>
                </div>
            );
        }

        return (
            <div className="h-full flex flex-col">
                {/* Tab 导航 */}
                <div className="flex border-b border-gray-200 mb-4">
                    <button
                        onClick={() => setActiveTab('execRecords')}
                        className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                            activeTab === 'execRecords'
                                ? 'border-blue-500 text-blue-600'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        }`}
                    >
                        执行记录
                    </button>
                    <button
                        onClick={() => setActiveTab('taskDetail')}
                        className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                            activeTab === 'taskDetail'
                                ? 'border-blue-500 text-blue-600'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        }`}
                    >
                        任务详情
                    </button>
                </div>

                {/* Tab 内容 */}
                <div className="flex-1 overflow-hidden">
                    {activeTab === 'execRecords' ? renderExecRecords() : renderTaskDetailContent()}
                </div>
            </div>
        );
    };

    // 渲染任务详情内容
    const renderTaskDetailContent = () => {
        return (
            <div className="h-full flex flex-col">
                <div className="flex justify-between items-center mb-4">
                    <h3 className="text-xl font-semibold">{selectedTask.title}</h3>
                    <div className="flex space-x-2">
                        <button
                            onClick={handleRun}
                            disabled={taskRunning}
                            className="px-3 py-1 bg-green-500 text-white rounded-md hover:bg-green-600 text-sm disabled:bg-gray-400 disabled:cursor-not-allowed"
                        >
                            {taskRunning ? '执行中...' : '执行'}
                        </button>
                        <button
                            onClick={handleEdit}
                            className="px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 text-sm"
                        >
                            编辑
                        </button>
                        <button
                            onClick={handleDelete}
                            className="px-3 py-1 bg-red-500 text-white rounded-md hover:bg-red-600 text-sm"
                        >
                            删除
                        </button>
                    </div>
                </div>

                <div className="space-y-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">任务描述:</label>
                        <p className="text-gray-600">{selectedTask.remark || '无描述'}</p>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">关联模板:</label>
                        {selectedTask.templateId ? (
                            <div className="space-y-1">
                                {selectedTask.templateId.split(',').map(id => id.trim()).filter(id => id).map(templateId => {
                                    const template = templates.find(t => t.id.toString() === templateId);
                                    return (
                                        <div key={templateId} className="bg-blue-50 px-2 py-1 rounded text-sm text-blue-700 inline-block mr-2 mb-1">
                                            {template ? template.title : `未知模板(${templateId})`}
                                        </div>
                                    );
                                })}
                            </div>
                        ) : (
                            <p className="text-gray-600">未选择模板</p>
                        )}
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Cron表达式:</label>
                        <p className="text-gray-600 font-mono">{selectedTask.cornExp}</p>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">周期类型:</label>
                        <p className="text-gray-600">
                            {selectedTask.cycleType === 'day' ? '日' :
                             selectedTask.cycleType === 'week' ? '周' :
                             selectedTask.cycleType === 'month' ? '月' :
                             selectedTask.cycleType || '未设置'}
                        </p>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">选择的参数:</label>
                        {selectedTask.methodId ? (
                            <div className="bg-gray-50 p-3 rounded-md">
                                {(() => {
                                    const selectedMethod = toolsData.find(method => method.id == selectedTask.methodId);
                                    return selectedMethod ? (
                                        <div>
                                            <p className="text-sm text-gray-600">
                                                {selectedMethod.name} - {selectedMethod.invokeType}
                                            </p>
                                            <p className="text-xs text-gray-500 mt-1">ID: {selectedTask.methodId}</p>
                                        </div>
                                    ) : (
                                        <p className="text-sm text-gray-600">{selectedTask.methodId}</p>
                                    );
                                })()}
                            </div>
                        ) : (
                            <p className="text-gray-600">未选择工具</p>
                        )}
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">数据保留设置:</label>
                        <p className="text-gray-600">
                            {selectedTask.reten === 1 ? '保留同一周期的数据' : '不保留同一周期的数据'}
                        </p>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">覆盖重复设置:</label>
                        <p className="text-gray-600">{selectedTask.coverRepeat || 0}</p>
                    </div>

                    <div className="text-sm text-gray-500">
                        <p>创建于: {selectedTask.createdAt}</p>
                        <p>更新于: {selectedTask.updatedAt}</p>
                    </div>
                </div>
            </div>
        );
    };

    // 渲染执行记录
    const renderExecRecords = () => {
        return (
            <div className="h-full flex gap-4">
                {/* 左侧：执行记录列表 - 进一步缩小宽度 */}
                <div className="w-1/5 flex flex-col">
                    <div className="mb-4 flex items-center justify-between">
                        <h4 className="text-lg font-semibold text-gray-900">执行记录</h4>
                        <span className="text-sm text-gray-500">
                            共 {runRecordsPage.total} 条记录
                        </span>
                    </div>
                    <div className="flex-1 overflow-hidden">
                        {loadingRunRecords ? (
                            <div className="flex items-center justify-center py-12">
                                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
                                <span className="ml-3 text-gray-500">加载中...</span>
                            </div>
                        ) : runRecords.length === 0 ? (
                            <div className="text-center py-12">
                                <svg className="w-12 h-12 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <p className="text-gray-500">暂无执行记录</p>
                            </div>
                        ) : (
                            <div className="space-y-3 h-full overflow-y-auto pr-2">
                                {runRecords.map((record, index) => (
                                    <div
                                        key={record.id}
                                        className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                                            selectedRecord?.id === record.id
                                                ? 'bg-blue-50 border-blue-300 shadow-md'
                                                : 'bg-white border-gray-200 hover:border-gray-300 hover:shadow-sm'
                                        }`}
                                        onClick={() => handleRecordClick(record)}
                                    >
                                        <div className="flex items-start justify-between">
                                            <div className="flex-1 min-w-0">
                                                {/* 批次号 */}
                                                <div className="flex items-center space-x-2 mb-2">
                                                    <span className="text-sm font-semibold text-gray-900 truncate">
                                                        {record.batchNo}
                                                    </span>
                                                </div>

                                                {/* 状态标签 */}
                                                <div className="flex items-center space-x-2 mb-3">
                                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                                        record.execType === 'NORMAL' ? 'bg-blue-100 text-blue-800' :
                                                        record.execType === 'DEBUG' ? 'bg-orange-100 text-orange-800' :
                                                        'bg-purple-100 text-purple-800'
                                                    }`}>
                                                        {record.execType === 'NORMAL' ? '正常' :
                                                         record.execType === 'DEBUG' ? '调试' : '重新分析'}
                                                    </span>
                                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                                        record.execStatus === 'SUCCESS' ? 'bg-green-100 text-green-800' :
                                                        record.execStatus === 'FAILED' ? 'bg-red-100 text-red-800' :
                                                        'bg-yellow-100 text-yellow-800'
                                                    }`}>
                                                        {record.execStatus === 'SUCCESS' ? '成功' :
                                                         record.execStatus === 'FAILED' ? '失败' : '运行中'}
                                                    </span>
                                                </div>

                                                {/* 时间信息 */}
                                                <div className="text-xs text-gray-500 space-y-1">
                                                    <div className="flex items-center">
                                                        <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                        </svg>
                                                        开始: {record.execStartTime ? new Date(record.execStartTime).toLocaleString() : '未知'}
                                                    </div>
                                                    {record.execEndTime && (
                                                        <div className="flex items-center">
                                                            <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                            </svg>
                                                            结束: {new Date(record.execEndTime).toLocaleString()}
                                                        </div>
                                                    )}
                                                    {record.resultCount !== null && (
                                                        <div className="flex items-center">
                                                            <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                                            </svg>
                                                            处理项数: {record.resultCount}
                                                        </div>
                                                    )}
                                                </div>
                                                {record.errorMessage && (
                                                    <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-600">
                                                        <div className="flex items-start">
                                                            <svg className="w-3 h-3 mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                            </svg>
                                                            <span>{record.errorMessage}</span>
                                                        </div>
                                                    </div>
                                                )}
                                            </div>

                                            {/* 重新分析按钮 - 只对正常执行且成功的记录显示 */}
                                            {record.execType === 'NORMAL' && record.execStatus === 'SUCCESS' && (
                                                <div className="flex-shrink-0 ml-3">
                                                    <button
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            handleReanalyzeByBatch(selectedTask.id, record.batchNo, e);
                                                        }}
                                                        disabled={reanalyzingBatches.has(`${selectedTask.id}_${record.batchNo}`)}
                                                        className="px-3 py-1.5 text-xs bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow-md"
                                                        title="重新分析此批次的数据"
                                                    >
                                                        {reanalyzingBatches.has(`${selectedTask.id}_${record.batchNo}`) ? (
                                                            <span className="flex items-center">
                                                                <svg className="animate-spin -ml-1 mr-1 h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                                </svg>
                                                                分析中
                                                            </span>
                                                        ) : (
                                                            <span className="flex items-center">
                                                                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                                                </svg>
                                                                重新分析
                                                            </span>
                                                        )}
                                                    </button>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}

                        {/* 分页 */}
                        {runRecordsPage.total > runRecordsPage.size && (
                            <div className="mt-4 pt-4 border-t border-gray-200">
                                <div className="flex items-center justify-between">
                                    <div className="text-sm text-gray-500">
                                        显示 {((runRecordsPage.current - 1) * runRecordsPage.size) + 1} - {Math.min(runRecordsPage.current * runRecordsPage.size, runRecordsPage.total)} 条，共 {runRecordsPage.total} 条
                                    </div>
                                    <div className="flex space-x-1">
                                        <button
                                            onClick={() => handlePageChange(runRecordsPage.current - 1)}
                                            disabled={runRecordsPage.current <= 1}
                                            className="px-3 py-1.5 text-sm bg-white border border-gray-300 text-gray-700 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors"
                                        >
                                            上一页
                                        </button>
                                        <span className="px-3 py-1.5 text-sm text-gray-600 bg-gray-50 border border-gray-300 rounded-md">
                                            {runRecordsPage.current} / {Math.ceil(runRecordsPage.total / runRecordsPage.size)}
                                        </span>
                                        <button
                                            onClick={() => handlePageChange(runRecordsPage.current + 1)}
                                            disabled={runRecordsPage.current >= Math.ceil(runRecordsPage.total / runRecordsPage.size)}
                                            className="px-3 py-1.5 text-sm bg-white border border-gray-300 text-gray-700 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors"
                                        >
                                            下一页
                                        </button>
                                    </div>
                                </div>
                            </div>
                        )}
                </div>
                </div>
                {/* 右侧：任务结果详情 - 进一步增加宽度 */}
                <div className="w-4/5 flex flex-col">
                    <div className="mb-4 flex items-center justify-between">
                        <h4 className="text-lg font-semibold text-gray-900">结果详情</h4>
                        {selectedRecord && (
                            <span className="text-sm text-gray-500">
                                批次: {selectedRecord.batchNo}
                            </span>
                        )}
                    </div>

                    <div className="flex-1 overflow-hidden">
                        {selectedRecord ? renderTaskResultDetails() : (
                            <div className="flex flex-col items-center justify-center h-full text-gray-500 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                                <svg className="w-16 h-16 mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <p className="text-lg font-medium mb-2">选择执行记录</p>
                                <p className="text-sm">点击左侧执行记录查看详细结果</p>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        );
    };

    // 添加选中的TaskResult状态
    const [selectedTaskResult, setSelectedTaskResult] = useState(null);

    // 渲染任务结果详情
    const renderTaskResultDetails = () => {
        if (loadingTaskResults) {
            return (
                <div className="flex items-center justify-center h-full">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
                    <span className="ml-3 text-gray-500">加载结果详情中...</span>
                </div>
            );
        }

        if (!taskResultDetails) {
            return (
                <div className="flex flex-col items-center justify-center h-full text-gray-500">
                    <svg className="w-12 h-12 mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <p>暂无结果详情</p>
                </div>
            );
        }

        const { taskResults, taskResultItems } = taskResultDetails;

        return (
            <div className="h-full flex gap-4">
                {/* 左侧：TaskResult列表 - 进一步缩小 */}
                <div className="w-1/4 flex flex-col">
                    <div className="mb-3">
                        <h5 className="text-sm font-semibold text-gray-900">任务结果</h5>
                        <p className="text-xs text-gray-500">共 {taskResults?.length || 0} 条</p>
                    </div>

                    <div className="flex-1 overflow-y-auto space-y-2">
                        {taskResults && taskResults.length > 0 ? (
                            taskResults.map((result, index) => (
                                <div
                                    key={result.id}
                                    className={`p-3 rounded-lg border cursor-pointer transition-all duration-200 ${
                                        selectedTaskResult?.id === result.id
                                            ? 'bg-blue-50 border-blue-300 shadow-sm'
                                            : 'bg-white border-gray-200 hover:border-gray-300 hover:shadow-sm'
                                    }`}
                                    onClick={() => setSelectedTaskResult(result)}
                                >
                                    <div className="space-y-2">
                                        <div className="flex items-center justify-between">
                                            <span className="text-sm font-medium text-gray-900">
                                                {result.skey || `结果 #${index + 1}`}
                                            </span>
                                            {result.role && (
                                                <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${
                                                    result.role === 'salesman' ? 'bg-green-100 text-green-800' :
                                                    result.role === 'group' ? 'bg-blue-100 text-blue-800' :
                                                    result.role === 'manager' ? 'bg-purple-100 text-purple-800' :
                                                    'bg-gray-100 text-gray-800'
                                                }`}>
                                                    {result.role === 'salesman' ? '业务员' :
                                                     result.role === 'group' ? '团长' :
                                                     result.role === 'manager' ? '总经理' : result.role}
                                                </span>
                                            )}
                                        </div>

                                        <div className="text-xs text-gray-500 space-y-1">
                                            {result.dataDate && (
                                                <div className="flex items-center">
                                                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                    </svg>
                                                    数据日期: {result.dataDate}
                                                </div>
                                            )}
                                            <div className="flex items-center">
                                                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                                创建: {result.createTime ? new Date(result.createTime).toLocaleString() : '未知'}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))
                        ) : (
                            <div className="text-center py-8 text-gray-500">
                                <svg className="w-8 h-8 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <p className="text-sm">暂无任务结果</p>
                            </div>
                        )}
                    </div>
                </div>

                {/* 右侧：TaskResultItem列表 - 进一步增加宽度 */}
                <div className="w-3/4 flex flex-col">
                    <div className="mb-3">
                        <h5 className="text-sm font-semibold text-gray-900">分析结果</h5>
                        {selectedTaskResult ? (
                            <p className="text-xs text-gray-500">
                                {selectedTaskResult.skey || '未命名结果'} 的详细分析
                            </p>
                        ) : (
                            <p className="text-xs text-gray-500">选择左侧任务结果查看详情</p>
                        )}
                    </div>

                    <div className="flex-1 overflow-y-auto">
                        {selectedTaskResult ? (
                            <div className="space-y-3">
                                {taskResultItems
                                    ?.filter(item => item.resultId === selectedTaskResult.id)
                                    ?.map((item, index) => (
                                        <div key={item.id} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                                            <div className="flex items-center justify-between mb-3">
                                                <div className="flex items-center space-x-2">
                                                    <span className="text-sm font-medium text-gray-900">
                                                        分析项 #{index + 1}
                                                    </span>
                                                    <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${
                                                        item.subBizType === 'summary' ? 'bg-blue-100 text-blue-800' :
                                                        item.subBizType === 'product' ? 'bg-green-100 text-green-800' :
                                                        item.subBizType === 'area' ? 'bg-yellow-100 text-yellow-800' :
                                                        item.subBizType === 'team' ? 'bg-purple-100 text-purple-800' :
                                                        item.subBizType === 'customer' ? 'bg-pink-100 text-pink-800' :
                                                        'bg-gray-100 text-gray-800'
                                                    }`}>
                                                        {item.subBizType}
                                                    </span>
                                                </div>
                                                <span className="text-xs text-gray-500">ID: {item.id}</span>
                                            </div>

                                            {/* 时间信息 */}
                                            <div className="mb-3 text-xs text-gray-500 space-y-1">
                                                <div className="flex items-center">
                                                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                    </svg>
                                                    创建: {item.createTime ? new Date(item.createTime).toLocaleString() : '未知'}
                                                </div>
                                                {item.updateTime && (
                                                    <div className="flex items-center">
                                                        <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                                        </svg>
                                                        更新: {new Date(item.updateTime).toLocaleString()}
                                                    </div>
                                                )}
                                            </div>

                                            {/* 分析结果 */}
                                            {item.itemResult ? (
                                                <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mb-3">
                                                    <div className="flex items-start">
                                                        <svg className="w-4 h-4 mr-2 mt-0.5 text-blue-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                        </svg>
                                                        <div className="flex-1">
                                                            <div className="text-xs font-medium text-blue-800 mb-1">分析结果</div>
                                                            <div
                                                                className="text-sm text-blue-900 leading-relaxed markdown-content"
                                                                dangerouslySetInnerHTML={{
                                                                    __html: marked.parse(item.itemResult || '')
                                                                }}
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                            ) : (
                                                <div className="bg-gray-50 border border-gray-200 rounded-md p-3 text-center mb-3">
                                                    <svg className="w-6 h-6 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                    </svg>
                                                    <p className="text-sm text-gray-500">暂无分析结果</p>
                                                </div>
                                            )}

                                            {/* 原始数据 */}
                                            {item.originData ? (
                                                <div className="bg-gray-50 border border-gray-200 rounded-md p-3">
                                                    <div className="flex items-start">
                                                        <svg className="w-4 h-4 mr-2 mt-0.5 text-gray-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                        </svg>
                                                        <div className="flex-1">
                                                            <div className="text-xs font-medium text-gray-700 mb-2">原始数据</div>
                                                            <div className="bg-white border border-gray-200 rounded p-2 max-h-40 overflow-y-auto">
                                                                <pre className="text-xs text-gray-800 whitespace-pre-wrap font-mono leading-relaxed">
                                                                    {(() => {
                                                                        try {
                                                                            // 尝试格式化JSON数据
                                                                            const parsed = JSON.parse(item.originData);
                                                                            return JSON.stringify(parsed, null, 2);
                                                                        } catch (e) {
                                                                            // 如果不是JSON，直接显示原始文本
                                                                            return item.originData;
                                                                        }
                                                                    })()}
                                                                </pre>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            ) : (
                                                <div className="bg-gray-50 border border-gray-200 rounded-md p-3 text-center">
                                                    <svg className="w-6 h-6 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                                                    </svg>
                                                    <p className="text-sm text-gray-500">暂无原始数据</p>
                                                </div>
                                            )}
                                        </div>
                                    ))}

                                {(!taskResultItems || taskResultItems.filter(item => item.resultId === selectedTaskResult.id).length === 0) && (
                                    <div className="text-center py-12 text-gray-500">
                                        <svg className="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                        </svg>
                                        <p>该任务结果暂无分析项</p>
                                    </div>
                                )}
                            </div>
                        ) : (
                            <div className="flex flex-col items-center justify-center h-full text-gray-500">
                                <svg className="w-12 h-12 mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
                                </svg>
                                <p className="text-lg font-medium mb-2">选择任务结果</p>
                                <p className="text-sm">点击左侧任务结果查看详细分析</p>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        );
    };

    // 渲染创建/编辑表单
    const renderTaskForm = () => (
        <div className="h-full flex flex-col">
            <h3 className="text-xl font-semibold mb-4">{selectedTask ? '编辑任务' : '创建新任务'}</h3>
            <div className="space-y-4 flex-1">
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">任务标题 *</label>
                    <input
                        type="text"
                        name="title"
                        value={formData.title}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="输入任务标题"
                    />
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">任务描述</label>
                    <textarea
                        name="remark"
                        value={formData.remark}
                        onChange={handleInputChange}
                        rows="3"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="输入任务描述"
                    />
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">关联模板 * (可多选)</label>
                    {templatesLoading ? (
                        <p className="text-sm text-gray-500 mt-1">加载模板列表中...</p>
                    ) : (
                        <div className="space-y-2">
                            <div className="max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2">
                                {templates.map(template => (
                                    <label key={template.id} className="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                                        <input
                                            type="checkbox"
                                            checked={selectedTemplateIds.includes(template.id.toString())}
                                            onChange={() => handleTemplateChange(template.id.toString())}
                                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                        />
                                        <span className="text-sm">
                                            <span className="font-medium">{template.title}</span>
                                            {template.remark && <span className="text-gray-500"> - {template.remark}</span>}
                                        </span>
                                    </label>
                                ))}
                            </div>
                            {selectedTemplateIds.length > 0 && (
                                <div className="mt-2">
                                    <p className="text-sm text-gray-600 mb-1">已选择 {selectedTemplateIds.length} 个模板:</p>
                                    <div className="flex flex-wrap gap-1">
                                        {selectedTemplateIds.map(templateId => {
                                            const template = templates.find(t => t.id.toString() === templateId);
                                            return (
                                                <span key={templateId} className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                                                    {template ? template.title : `模板${templateId}`}
                                                </span>
                                            );
                                        })}
                                    </div>
                                </div>
                            )}
                        </div>
                    )}
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">周期类型</label>
                    <select
                        name="cycleType"
                        value={formData.cycleType}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        <option value="">选择周期类型</option>
                        <option value="day">日</option>
                        <option value="week">周</option>
                        <option value="month">月</option>
                    </select>
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">数据保留设置</label>
                    <select
                        name="reten"
                        value={formData.reten}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        <option value={0}>不保留同一周期的数据</option>
                        <option value={1}>保留同一周期的数据</option>
                    </select>
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">覆盖重复设置</label>
                    <input
                        type="number"
                        name="coverRepeat"
                        value={formData.coverRepeat}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="输入数字（默认0）"
                        min="0"
                    />
                </div>

                {/* 参数选择区域 */}
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">选择参数</label>
                    <select
                        value={selectedMethodId}
                        onChange={(e) => handleMethodChange(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        disabled={toolsLoading}
                    >
                        <option value="">请选择参数</option>
                        {toolsData.map(method => (
                            <option key={method.id} value={method.id}>
                                {method.name} - {method.invokeType}
                            </option>
                        ))}
                    </select>
                    {toolsLoading && (
                        <p className="text-sm text-gray-500 mt-1">加载参数列表中...</p>
                    )}
                    {selectedMethodId && (
                        <div className="mt-2 p-3 bg-blue-50 rounded-md border border-blue-200">
                            {(() => {
                                const selectedMethod = toolsData.find(method => method.id == selectedMethodId);
                                return selectedMethod ? (
                                    <div>
                                        <p className="text-sm text-blue-700">
                                            <strong>已选择参数:</strong> {selectedMethod.name}
                                        </p>
                                        <p className="text-xs text-blue-600 mt-1">类型: {selectedMethod.invokeType}</p>
                                        <p className="text-xs text-blue-600">ID: {selectedMethodId}</p>
                                    </div>
                                ) : (
                                    <p className="text-sm text-blue-700">
                                        <strong>已选择参数ID:</strong> {selectedMethodId}
                                    </p>
                                );
                            })()}
                        </div>
                    )}
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Cron表达式 *</label>
                    <div className="space-y-3">
                        {/* 智能生成区域 */}
                        <div className="bg-blue-50 p-3 rounded-md border border-blue-200">
                            <label className="block text-sm font-medium text-blue-700 mb-2">智能生成</label>
                            <div className="flex gap-2">
                                <input
                                    type="text"
                                    value={naturalLanguageInput}
                                    onChange={(e) => setNaturalLanguageInput(e.target.value)}
                                    className="flex-1 px-3 py-2 border border-blue-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                                    placeholder="例：每天上午9点执行、每周一下午3点执行、每月1号执行"
                                    disabled={cronGenerating}
                                />
                                <button
                                    type="button"
                                    onClick={generateCronExpression}
                                    disabled={cronGenerating || !naturalLanguageInput.trim()}
                                    className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-sm flex items-center"
                                >
                                    {cronGenerating ? (
                                        <>
                                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                            生成中...
                                        </>
                                    ) : (
                                        '智能生成'
                                    )}
                                </button>
                            </div>
                        </div>

                        {/* Cron表达式输入 */}
                        <div>
                            <input
                                type="text"
                                name="cornExp"
                                value={formData.cornExp}
                                onChange={handleInputChange}
                                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 font-mono ${
                                    cronValidationError
                                        ? 'border-red-300 focus:ring-red-500'
                                        : 'border-gray-300 focus:ring-blue-500'
                                }`}
                                placeholder="输入Cron表达式"
                            />
                            {cronValidationError && (
                                <p className="text-red-500 text-xs mt-1">{cronValidationError}</p>
                            )}
                        </div>

                        {/* 预设按钮 */}
                        <div className="flex flex-wrap gap-2">
                            {cronPresets.map((preset, index) => (
                                <button
                                    key={index}
                                    type="button"
                                    onClick={() => {
                                        setFormData(prev => ({ ...prev, cornExp: preset.value }));
                                        setCronValidationError('');
                                    }}
                                    className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
                                >
                                    {preset.label}
                                </button>
                            ))}
                        </div>

                        <p className="text-xs text-gray-500">
                            Cron表达式格式: 秒 分 时 日 月 周 (例: 0 0 9 * * ? 表示每天9点执行)
                        </p>
                    </div>
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                    <button
                        onClick={handleCancel}
                        className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                    >
                        取消
                    </button>
                    <button
                        onClick={handleSave}
                        className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                    >
                        保存
                    </button>
                </div>
            </div>
        </div>
    );

    // 主渲染函数
    return (
        <div className="h-full flex flex-col text-gray-800">
            <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold">任务管理</h2>
                {!isEditing && (
                    <button
                        onClick={handleCreateNew}
                        className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                    >
                        创建新任务
                    </button>
                )}
            </div>

            {loading ? (
                <div className="flex justify-center items-center h-64">
                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                </div>
            ) : (
                <div className="flex-1 grid grid-cols-1 md:grid-cols-8 gap-6 h-full">
                    {/* 任务列表 - 进一步减少宽度 */}
                    <div className="bg-white bg-opacity-80 rounded-xl shadow-sm overflow-hidden">
                        <div className="p-4 border-b border-gray-200">
                            <h3 className="font-medium">任务列表</h3>
                        </div>
                        <div className="overflow-y-auto" style={{ maxHeight: 'calc(100vh - 280px)' }}>
                            {tasks.length === 0 ? (
                                <div className="p-4 text-center text-gray-500">
                                    暂无任务，点击"创建新任务"按钮添加
                                </div>
                            ) : (
                                <ul className="divide-y divide-gray-200">
                                    {tasks.map(task => (
                                        <li
                                            key={task.id}
                                            className={`p-3 cursor-pointer hover:bg-gray-50 transition-colors ${selectedTask && selectedTask.id === task.id ? 'bg-blue-50' : ''}`}
                                            onClick={() => handleSelectTask(task)}
                                        >
                                            <div className="flex flex-col">
                                                <div className="flex-1 min-w-0">
                                                    <h4 className="font-medium text-sm truncate" title={task.title}>{task.title}</h4>
                                                    <p className="text-xs text-gray-500 mt-1 truncate" title={task.remark}>{task.remark || '无描述'}</p>
                                                    <div className="text-xs text-gray-400 mt-1">
                                                        <p className="truncate" title={getTemplateName(task.templateId)}>模板: {getTemplateName(task.templateId)}</p>
                                                    </div>
                                                </div>
                                                <div className="flex-shrink-0 ml-2 space-x-1">
                                                    {/* 执行按钮 */}
                                                    <button
                                                        onClick={(e) => handleQuickRun(task, e)}
                                                        disabled={quickRunningTasks.has(task.id) || debuggingTasks.has(task.id)}
                                                        className="p-2 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-full transition-colors disabled:text-gray-400 disabled:cursor-not-allowed"
                                                        title={quickRunningTasks.has(task.id) ? '执行中...' : '立即执行任务'}
                                                    >
                                                        {quickRunningTasks.has(task.id) ? (
                                                            <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                            </svg>
                                                        ) : (
                                                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                                                <path d="M8 5v14l11-7z"/>
                                                            </svg>
                                                        )}
                                                    </button>

                                                    {/* Debug按钮 */}
                                                    <button
                                                        onClick={(e) => handleDebug(task, e)}
                                                        disabled={quickRunningTasks.has(task.id) || debuggingTasks.has(task.id)}
                                                        className="p-2 text-orange-600 hover:text-orange-800 hover:bg-orange-50 rounded-full transition-colors disabled:text-gray-400 disabled:cursor-not-allowed"
                                                        title={debuggingTasks.has(task.id) ? 'Debug中...' : 'Debug任务'}
                                                    >
                                                        {debuggingTasks.has(task.id) ? (
                                                            <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                            </svg>
                                                        ) : (
                                                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                                                            </svg>
                                                        )}
                                                    </button>
                                                </div>
                                            </div>
                                        </li>
                                    ))}
                                </ul>
                            )}
                        </div>
                    </div>

                    {/* 任务详情及编辑区 - 进一步增加宽度 */}
                    <div className="bg-white bg-opacity-80 rounded-xl shadow-sm overflow-hidden md:col-span-7 p-6">
                        {isEditing ? renderTaskForm() : renderTaskDetail()}
                    </div>
                </div>
            )}
        </div>
    );
}
