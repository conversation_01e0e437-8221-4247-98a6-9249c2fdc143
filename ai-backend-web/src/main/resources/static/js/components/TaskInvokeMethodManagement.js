// TaskInvokeMethodManagement.js - 方法注册管理组件
const { useState, useEffect } = React;

function TaskInvokeMethodManagement() {
    // 状态管理
    const [methods, setMethods] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedMethod, setSelectedMethod] = useState(null);
    const [isEditing, setIsEditing] = useState(false);
    const [formData, setFormData] = useState({
        name: '',
        invokeType: 'tools',
        invokeParam: '',
        inputScheme: '',
        outputScheme: '',
        useType: 'parameter'
    });
    const [pagination, setPagination] = useState({
        pageNum: 1,
        pageSize: 10,
        total: 0
    });
    const [searchForm, setSearchForm] = useState({
        name: '',
        invokeType: '',
        useType: ''
    });

    // 调用类型选项
    const invokeTypeOptions = [
        { value: 'http', label: 'HTTP' },
        { value: 'dify', label: 'dify' },
        { value: 'tools', label: 'Tools' },
        { value: 'python', label: 'Python' },
        { value: 'groovy', label: 'Groovy' }
    ];

    // 使用类型选项
    const useTypeOptions = [
        { value: 'parameter', label: '参数' },
        { value: 'fetchData', label: '获取数据' },
        { value: 'system', label: '系统' }
    ];

    // 获取方法注册列表
    useEffect(() => {
        fetchMethodList();
    }, [pagination.pageNum, pagination.pageSize]);

    // 从后端获取方法注册列表
    const fetchMethodList = async () => {
        try {
            setLoading(true);
            const requestData = {
                pageNum: pagination.pageNum,
                pageSize: pagination.pageSize,
                ...searchForm
            };

            const result = await apiRequest(API_CONFIG.TASK_INVOKE_METHOD.LIST, {
                method: 'POST',
                body: JSON.stringify(requestData)
            });

            if (result.success && result.data) {
                setMethods(result.data.list || []);
                setPagination(prev => ({
                    ...prev,
                    total: result.data.total || 0
                }));
            } else {
                window.showMessage.error('获取方法注册列表失败: ' + (result.msg || '未知错误'));
            }
        } catch (error) {
            console.error('获取方法注册列表失败:', error);
            window.showMessage.error('获取方法注册列表失败: ' + error.message);
        } finally {
            setLoading(false);
        }
    };

    // 搜索处理
    const handleSearch = () => {
        setPagination(prev => ({ ...prev, pageNum: 1 }));
        fetchMethodList();
    };

    // 重置搜索
    const handleResetSearch = () => {
        setSearchForm({
            name: '',
            invokeType: '',
            useType: ''
        });
        setPagination(prev => ({ ...prev, pageNum: 1 }));
        setTimeout(() => {
            fetchMethodList();
        }, 0);
    };

    // 新增方法注册
    const handleAdd = () => {
        setSelectedMethod(null);
        setFormData({
            name: '',
            invokeType: 'dify',
            invokeParam: '',
            inputScheme: '',
            outputScheme: '',
            useType: 'parameter'
        });
        setIsEditing(true);
    };

    // 编辑方法注册
    const handleEdit = (method) => {
        setSelectedMethod(method);
        setFormData({
            name: method.name || '',
            invokeType: method.invokeType || 'dify',
            invokeParam: method.invokeParam || '',
            inputScheme: method.inputScheme || '',
            outputScheme: method.outputScheme || '',
            useType: method.useType || 'parameter'
        });
        setIsEditing(true);
    };

    // 删除方法注册
    const handleDelete = async (id) => {
        const confirmed = await window.showConfirm('确定要删除这个方法注册吗？');
        if (!confirmed) {
            return;
        }

        try {
            const result = await apiRequest(API_CONFIG.TASK_INVOKE_METHOD.DELETE, {
                method: 'POST'
            }, { id });

            if (result.success) {
                window.showMessage.success('删除成功');
                fetchMethodList();
            } else {
                window.showMessage.error('删除失败: ' + (result.msg || '未知错误'));
            }
        } catch (error) {
            console.error('删除方法注册失败:', error);
            window.showMessage.error('删除失败: ' + error.message);
        }
    };

    // 保存方法注册
    const handleSave = async () => {
        // 表单验证
        if (!formData.name.trim()) {
            window.showMessage.warning('请输入方法名称');
            return;
        }

        if (!formData.invokeType) {
            window.showMessage.warning('请选择调用类型');
            return;
        }


        try {
            const saveData = {
                ...formData,
                id: selectedMethod?.id || null
            };

            const result = await apiRequest(API_CONFIG.TASK_INVOKE_METHOD.SAVE, {
                method: 'POST',
                body: JSON.stringify(saveData)
            });

            if (result.success) {
                window.showMessage.success(selectedMethod ? '更新成功' : '创建成功');
                setIsEditing(false);
                fetchMethodList();
            } else {
                window.showMessage.error('保存失败: ' + (result.msg || '未知错误'));
            }
        } catch (error) {
            console.error('保存方法注册失败:', error);
            window.showMessage.error('保存失败: ' + error.message);
        }
    };

    // 取消编辑
    const handleCancel = () => {
        setIsEditing(false);
        setSelectedMethod(null);
        setFormData({
            name: '',
            invokeType: 'tools',
            invokeParam: '',
            inputScheme: '',
            outputScheme: '',
            useType: 'parameter'
        });
    };

    // 分页处理
    const handlePageChange = (newPage) => {
        setPagination(prev => ({ ...prev, pageNum: newPage }));
    };

    // 页面大小变更
    const handlePageSizeChange = (newSize) => {
        setPagination(prev => ({ ...prev, pageSize: newSize, pageNum: 1 }));
    };

    // 格式化JSON显示
    const formatJson = (jsonStr) => {
        if (!jsonStr) return '';
        try {
            const parsed = JSON.parse(jsonStr);
            return JSON.stringify(parsed, null, 2);
        } catch (e) {
            return jsonStr;
        }
    };

    return React.createElement('div', { className: 'p-6' },
        React.createElement('div', { className: 'glass rounded-lg p-6' },
            // 标题
            React.createElement('div', { className: 'flex justify-between items-center mb-6' },
                React.createElement('h2', { className: 'text-2xl font-bold text-gray-800' }, '方法注册管理'),
                React.createElement('button', {
                    onClick: handleAdd,
                    className: 'bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors'
                }, '新增方法')
            ),

            // 搜索表单
            React.createElement('div', { className: 'mb-6 p-4 bg-gray-50 rounded-lg' },
                React.createElement('div', { className: 'grid grid-cols-1 md:grid-cols-4 gap-4' },
                    React.createElement('div', null,
                        React.createElement('label', { className: 'block text-sm font-medium text-gray-700 mb-1' }, '方法名称'),
                        React.createElement('input', {
                            type: 'text',
                            value: searchForm.name,
                            onChange: (e) => setSearchForm(prev => ({ ...prev, name: e.target.value })),
                            placeholder: '请输入方法名称',
                            className: 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
                        })
                    ),
                    React.createElement('div', null,
                        React.createElement('label', { className: 'block text-sm font-medium text-gray-700 mb-1' }, '调用类型'),
                        React.createElement('select', {
                            value: searchForm.invokeType,
                            onChange: (e) => setSearchForm(prev => ({ ...prev, invokeType: e.target.value })),
                            className: 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
                        },
                            React.createElement('option', { value: '' }, '全部'),
                            ...invokeTypeOptions.map(option =>
                                React.createElement('option', { key: option.value, value: option.value }, option.label)
                            )
                        )
                    ),
                    React.createElement('div', null,
                        React.createElement('label', { className: 'block text-sm font-medium text-gray-700 mb-1' }, '使用类型'),
                        React.createElement('select', {
                            value: searchForm.useType,
                            onChange: (e) => setSearchForm(prev => ({ ...prev, useType: e.target.value })),
                            className: 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
                        },
                            React.createElement('option', { value: '' }, '全部'),
                            ...useTypeOptions.map(option =>
                                React.createElement('option', { key: option.value, value: option.value }, option.label)
                            )
                        )
                    ),
                    React.createElement('div', { className: 'flex items-end gap-2' },
                        React.createElement('button', {
                            onClick: handleSearch,
                            className: 'bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors'
                        }, '搜索'),
                        React.createElement('button', {
                            onClick: handleResetSearch,
                            className: 'bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors'
                        }, '重置')
                    )
                )
            ),

            // 方法注册列表
            loading ? 
                React.createElement('div', { className: 'text-center py-8' }, '加载中...') :
                React.createElement('div', { className: 'overflow-x-auto' },
                    React.createElement('table', { className: 'min-w-full bg-white border border-gray-200' },
                        React.createElement('thead', { className: 'bg-gray-50' },
                            React.createElement('tr', null,
                                React.createElement('th', { className: 'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider' }, 'ID'),
                                React.createElement('th', { className: 'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider' }, '方法名称'),
                                React.createElement('th', { className: 'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider' }, '调用类型'),
                                React.createElement('th', { className: 'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider' }, '使用类型'),
                                React.createElement('th', { className: 'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider' }, '调用参数'),
                                React.createElement('th', { className: 'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider' }, '输入方案'),
                                React.createElement('th', { className: 'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider' }, '输出方案'),
                                React.createElement('th', { className: 'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider' }, '创建时间'),
                                React.createElement('th', { className: 'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider' }, '操作')
                            )
                        ),
                        React.createElement('tbody', { className: 'bg-white divide-y divide-gray-200' },
                            methods.length === 0 ?
                                React.createElement('tr', null,
                                    React.createElement('td', { colSpan: 9, className: 'px-6 py-4 text-center text-gray-500' }, '暂无数据')
                                ) :
                                methods.map(method =>
                                    React.createElement('tr', { key: method.id, className: 'hover:bg-gray-50' },
                                        React.createElement('td', { className: 'px-6 py-4 whitespace-nowrap text-sm text-gray-900' }, method.id),
                                        React.createElement('td', { className: 'px-6 py-4 whitespace-nowrap text-sm text-gray-900' }, method.name),
                                        React.createElement('td', { className: 'px-6 py-4 whitespace-nowrap' },
                                            React.createElement('span', {
                                                className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                                    method.invokeType === 'http' ? 'bg-blue-100 text-blue-800' :
                                                    method.invokeType === 'tools' ? 'bg-green-100 text-green-800' :
                                                    method.invokeType === 'python' ? 'bg-yellow-100 text-yellow-800' :
                                                    'bg-purple-100 text-purple-800'
                                                }`
                                            }, method.invokeType?.toUpperCase())
                                        ),
                                        React.createElement('td', { className: 'px-6 py-4 whitespace-nowrap' },
                                            React.createElement('span', {
                                                className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                                    method.useType === 'parameter' ? 'bg-blue-100 text-blue-800' :
                                                    method.useType === 'fetchData' ? 'bg-green-100 text-green-800' :
                                                    'bg-purple-100 text-purple-800'
                                                }`
                                            }, method.useType === 'parameter' ? '参数' : method.useType === 'fetchData' ? '获取数据' : '系统')
                                        ),
                                        React.createElement('td', { className: 'px-6 py-4 text-sm text-gray-900 max-w-xs truncate' },
                                            method.invokeParam ?
                                                React.createElement('span', { title: method.invokeParam }, method.invokeParam) :
                                                React.createElement('span', { className: 'text-gray-400' }, '无')
                                        ),
                                        React.createElement('td', { className: 'px-6 py-4 text-sm text-gray-900 max-w-xs truncate' },
                                            method.inputScheme ?
                                                React.createElement('span', { title: method.inputScheme }, method.inputScheme) :
                                                React.createElement('span', { className: 'text-gray-400' }, '无')
                                        ),
                                        React.createElement('td', { className: 'px-6 py-4 text-sm text-gray-900 max-w-xs truncate' },
                                            method.outputScheme ?
                                                React.createElement('span', { title: method.outputScheme }, method.outputScheme) :
                                                React.createElement('span', { className: 'text-gray-400' }, '无')
                                        ),
                                        React.createElement('td', { className: 'px-6 py-4 whitespace-nowrap text-sm text-gray-900' },
                                            method.createTime ? new Date(method.createTime).toLocaleString() : '-'
                                        ),
                                        React.createElement('td', { className: 'px-6 py-4 whitespace-nowrap text-sm font-medium' },
                                            React.createElement('div', { className: 'flex space-x-2' },
                                                React.createElement('button', {
                                                    onClick: () => handleEdit(method),
                                                    className: 'text-blue-600 hover:text-blue-900'
                                                }, '编辑'),
                                                React.createElement('button', {
                                                    onClick: () => handleDelete(method.id),
                                                    className: 'text-red-600 hover:text-red-900'
                                                }, '删除')
                                            )
                                        )
                                    )
                                )
                        )
                    )
                ),

            // 分页
            React.createElement('div', { className: 'mt-6 flex justify-between items-center' },
                React.createElement('div', { className: 'text-sm text-gray-700' },
                    `共 ${pagination.total} 条记录，第 ${pagination.pageNum} 页，每页 ${pagination.pageSize} 条`
                ),
                React.createElement('div', { className: 'flex items-center space-x-2' },
                    React.createElement('button', {
                        onClick: () => handlePageChange(pagination.pageNum - 1),
                        disabled: pagination.pageNum <= 1,
                        className: 'px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50'
                    }, '上一页'),
                    React.createElement('span', { className: 'px-3 py-1' }, pagination.pageNum),
                    React.createElement('button', {
                        onClick: () => handlePageChange(pagination.pageNum + 1),
                        disabled: pagination.pageNum >= Math.ceil(pagination.total / pagination.pageSize),
                        className: 'px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50'
                    }, '下一页'),
                    React.createElement('select', {
                        value: pagination.pageSize,
                        onChange: (e) => handlePageSizeChange(parseInt(e.target.value)),
                        className: 'px-2 py-1 border border-gray-300 rounded-md'
                    },
                        React.createElement('option', { value: 10 }, '10条/页'),
                        React.createElement('option', { value: 20 }, '20条/页'),
                        React.createElement('option', { value: 50 }, '50条/页')
                    )
                )
            )
        ),

        // 编辑模态框
        isEditing && React.createElement('div', { className: 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50' },
            React.createElement('div', { className: 'bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto' },
                React.createElement('div', { className: 'flex justify-between items-center mb-4' },
                    React.createElement('h3', { className: 'text-lg font-semibold' }, selectedMethod ? '编辑方法注册' : '新增方法注册'),
                    React.createElement('button', {
                        onClick: handleCancel,
                        className: 'text-gray-400 hover:text-gray-600'
                    }, '✕')
                ),

                React.createElement('div', { className: 'space-y-4' },
                    // 方法名称
                    React.createElement('div', null,
                        React.createElement('label', { className: 'block text-sm font-medium text-gray-700 mb-1' }, '方法名称 *'),
                        React.createElement('input', {
                            type: 'text',
                            value: formData.name,
                            onChange: (e) => setFormData(prev => ({ ...prev, name: e.target.value })),
                            placeholder: '请输入方法名称',
                            className: 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
                        })
                    ),

                    // 调用类型
                    React.createElement('div', null,
                        React.createElement('label', { className: 'block text-sm font-medium text-gray-700 mb-1' }, '调用类型 *'),
                        React.createElement('select', {
                            value: formData.invokeType,
                            onChange: (e) => setFormData(prev => ({ ...prev, invokeType: e.target.value })),
                            className: 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
                        },
                            ...invokeTypeOptions.map(option =>
                                React.createElement('option', { key: option.value, value: option.value }, option.label)
                            )
                        )
                    ),

                    // 使用类型
                    React.createElement('div', null,
                        React.createElement('label', { className: 'block text-sm font-medium text-gray-700 mb-1' }, '使用类型 *'),
                        React.createElement('select', {
                            value: formData.useType,
                            onChange: (e) => setFormData(prev => ({ ...prev, useType: e.target.value })),
                            className: 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
                        },
                            ...useTypeOptions.map(option =>
                                React.createElement('option', { key: option.value, value: option.value }, option.label)
                            )
                        )
                    ),

                    // 调用参数
                    React.createElement('div', null,
                        React.createElement('label', { className: 'block text-sm font-medium text-gray-700 mb-1' }, '调用参数 (JSON格式)'),
                        React.createElement('textarea', {
                            value: formData.invokeParam,
                            onChange: (e) => setFormData(prev => ({ ...prev, invokeParam: e.target.value })),
                            placeholder: '请输入  调用参数, dify 是 appKey ',
                            rows: 6,
                            className: 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm'
                        }),
                        React.createElement('div', { className: 'mt-1 text-xs text-gray-500' }, '请确保输入的是有效的JSON格式')
                    ),

                    // input_scheme
                    React.createElement('div', null,
                        React.createElement('label', { className: 'block text-sm font-medium text-gray-700 mb-1' }, 'input_scheme'),
                        React.createElement('textarea', {
                            value: formData.inputScheme,
                            onChange: (e) => setFormData(prev => ({ ...prev, inputScheme: e.target.value })),
                            placeholder: '请输入 input_scheme 描述，最多3000个字符',
                            rows: 4,
                            className: 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
                        }),
                        React.createElement('div', { className: 'mt-1 text-xs text-gray-500' }, '最多3000个字符')
                    ),

                    // output_schema
                    React.createElement('div', null,
                        React.createElement('label', { className: 'block text-sm font-medium text-gray-700 mb-1' }, 'output_schema'),
                        React.createElement('textarea', {
                            value: formData.outputScheme,
                            onChange: (e) => setFormData(prev => ({ ...prev, outputScheme: e.target.value })),
                            placeholder: '请输入 output_schema 描述，最多4000个字符',
                            rows: 4,
                            className: 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
                        }),
                        React.createElement('div', { className: 'mt-1 text-xs text-gray-500' }, '最多4000个字符')
                    )
                ),

                React.createElement('div', { className: 'flex justify-end space-x-3 mt-6' },
                    React.createElement('button', {
                        onClick: handleCancel,
                        className: 'px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50'
                    }, '取消'),
                    React.createElement('button', {
                        onClick: handleSave,
                        className: 'px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600'
                    }, selectedMethod ? '更新' : '创建')
                )
            )
        )
    );
}
