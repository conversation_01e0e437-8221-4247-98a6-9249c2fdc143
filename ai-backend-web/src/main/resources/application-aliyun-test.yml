#测试环境公共配置
---
spring:
  config:
    activate:
      on-profile: aliyun-test
  datasource:
    username: djai_backend_L8mM
    password: QbxwXStjFfP@Dv6#
    url: *********************************************************************************************************************
#  ai:
#    openai:
#      base-url: https://dashscope.aliyuncs.com/compatible-mode/
#      api-key: sk-f3f80e4238b14457ae9f4c1e0c54b0f5
#      chat:
#        options:
#          model: qwen3-coder-plus
#      base-url: https://ark.cn-beijing.volces.com/api
#      api-key: 92267054-e3d7-4903-8b75-e9153ff2c51d
#      chat:
#        completions-path: /v3/chat/completions
#        options:
#          model: doubao-1-5-pro-32k-250115
apollo:
  meta: http://service-apollo-config-server-dev.apollo.svc.cluster.local:8080
  bootstrap:
    enabled: true
    namespaces: application,url,DJCPS.commonu-utils

dataServer:
  app_key: c59IrXu7
  url: http://***********:30994/data-server
dify:
  url: https://dkai.cpsol.net
  key:
    ai_report: app-0EhZWElBUSmIe9ePjJ5egH3A
    agent: app-WkvaPbCvjNTm1couylRLyZc6
    manage_board: app-s6BhL1I7YfX3Yc5dGQFZcbCP
    faq: app-LG0DAIPP3U7XiNO3ma9VxZo3
    faq_easyorder: app-LG0DAIPP3U7XiNO3ma9VxZo3
    faq_yzx: app-iWcEbbMU0bKXXNA4Ve6vwJKt
client:
  url:
    easyOrder: http://**********:31086
    djjob: http://djjob
    djauth: http://djauth
logging:
  level:
    com.djcps.ai: debug