package com.djcps.ai.web.dify.vo;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 类 DifyMessageInVo.java 描述: 查询会话历史消息
 *
 * @author: yeb 2024/7/15 下午1:10
 **/
@Data
public class DifyMessageInVo {

    /** 会话id */
    @NotBlank(message = "无法获取到会话id")
    private String conversationId;
    /**
     * 分页获取, messageId,第一次为空,第二次传递第一页中最旧的数据id
     */
    private String firstId;
    /** 一页多少行 */
    private int limit = 5;
}
