package com.djcps.ai.web.tools;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.djcps.ai.common.result.WebResultExt;
import com.djcps.ai.common.vo.PageResult;
import com.djcps.ai.core.vo.out.TaskRunRecordVO;
import com.djcps.ai.dao.entity.UserTask;
import com.djcps.ai.dao.entity.UserTaskRunRecord;
import com.djcps.ai.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/task")
@Slf4j
@RequiredArgsConstructor
public class TaskController {

    private final UserTaskService userTaskService;
    private final JobService jobService;
    private final TaskResultService taskResultService;
    private final UserTaskRunRecordService userTaskRunRecordService;

    @PostMapping("/run/{taskId}")
    public void run(@PathVariable Long taskId) {
        userTaskService.runTask(taskId,null);
    }
    @PostMapping("/debug/{taskId}/{count}")
    public void run(@PathVariable Long taskId,@PathVariable Integer count) {
        userTaskService.runTask(taskId,count);
    }

    @PostMapping("/list")
    public WebResultExt<List<UserTask>> listTask() {
        List<UserTask> list = userTaskService.list();
        return new WebResultExt<>(list);
    }

    @PostMapping("/save")
    public WebResultExt<UserTask> saveTask(@RequestBody UserTask template) {
        try {
            // 设置用户ID
            template.setUserId("0");
            //id为空则为新增
            boolean create = template.getId() == null;
            boolean needChangeCron = false;
            Integer oldJobId = 0;
            //检测cron表达式是否发生变化
            if (!create) {
                UserTask byId = userTaskService.getById(template.getId());
                if (byId != null) {
                    needChangeCron = !StrUtil.equalsIgnoreCase(byId.getCornExp(), template.getCornExp());
                    oldJobId = byId.getJobId();
                }

            }

            // 保存或更新模板
            boolean success = userTaskService.saveOrUpdate(template);

            if (create) {
                String jobId = jobService.createCronTask(template.getCornExp(), template.getId(),template.getTitle());
                template.setJobId(Integer.parseInt(jobId));
                success = userTaskService.saveOrUpdate(template);
            } else {
                if (needChangeCron && oldJobId !=null) {
                    jobService.updateCronTask(oldJobId, template.getCornExp());
                }
            }

            if (success) {
                return new WebResultExt<>(template);
            } else {
                return WebResultExt.failure("保存任务失败");
            }
        } catch (Exception e) {
            log.error("保存任务失败", e);
            return WebResultExt.failure("保存任务失败: " + e.getMessage());
        }
    }

    @PostMapping("/delete/{id}")
    public WebResultExt<Boolean> deleteTask(@PathVariable Long id) {
        try {
            UserTask byId = userTaskService.getById(id);
            boolean success = false;
            if (byId != null) {
                Integer jobId = byId.getJobId();
                success = userTaskService.removeById(id);
                if (success && jobId != null && jobId > 0) {
                    jobService.delJob(jobId);
                }
            }
            return new WebResultExt<>(success);
        } catch (Exception e) {
            log.error("删除任务失败", e);
            return WebResultExt.failure("删除任务失败: " + e.getMessage());
        }
    }


    /**
     * 根据批次号重新分析任务
     * 根据已有的origin_data重新调用分析功能，更新item_result
     *
     * @param taskId  任务ID
     * @param batchNo 批次号
     * @return 重新分析结果
     */
    @PostMapping("/reanalyze/{taskId}/{batchNo}")
    public WebResultExt<String> reanalyzeTaskByBatch(@PathVariable Long taskId, @PathVariable String batchNo) {
        try {
            // 检查任务是否存在
            UserTask task = userTaskService.getById(taskId);
            if (task == null) {
                return WebResultExt.failure("任务不存在");
            }

            // 执行重新分析
            int successCount = taskResultService.reanalyzeTaskByBatch(taskId, batchNo);

            String message = String.format("重新分析完成，成功处理 %d 个结果项", successCount);
            log.info("任务 {} 批次 {} 重新分析完成，成功处理 {} 个结果项", taskId, batchNo, successCount);

            return new WebResultExt<>(message);
        } catch (Exception e) {
            log.error("重新分析任务失败，taskId: {}, batchNo: {}", taskId, batchNo, e);
            return WebResultExt.failure("重新分析失败: " + e.getMessage());
        }
    }

    /**
     * 分页获取任务的执行记录
     *
     * @param taskId   任务ID
     * @param pageNum  页码，默认1
     * @param pageSize 页大小，默认20
     * @return 执行记录分页结果
     */
    @PostMapping("/runRecords/{taskId}")
    public WebResultExt<PageResult<TaskRunRecordVO>> getTaskRunRecords(
            @PathVariable Long taskId,
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "20") int pageSize) {
        try {
            // 分页获取执行记录
            PageResult<UserTaskRunRecord> pageResult = userTaskRunRecordService.getRunRecordsByPage(taskId, pageNum, pageSize);

            // 转换为VO
            List<TaskRunRecordVO> recordVOs = pageResult.getList().stream().map(record -> {
                TaskRunRecordVO vo = new TaskRunRecordVO();
                BeanUtil.copyProperties(record, vo);
                vo.setExecType(record.getExecType().name());
                vo.setExecStatus(record.getExecStatus().name());
                return vo;
            }).collect(Collectors.toList());

            PageResult<TaskRunRecordVO> result = new PageResult<>(pageResult.getTotal(), recordVOs);
            return new WebResultExt<>(result);
        } catch (Exception e) {
            log.error("获取任务执行记录失败，taskId: {}", taskId, e);
            return WebResultExt.failure("获取执行记录失败: " + e.getMessage());
        }
    }

    /**
     * 根据任务ID和批次号获取任务结果详情
     *
     * @param taskId  任务ID
     * @param batchNo 批次号
     * @return 任务结果详情
     */
    @PostMapping("/taskResults/{taskId}/{batchNo}")
    public WebResultExt<Map<String, Object>> getTaskResults(@PathVariable Long taskId, @PathVariable String batchNo) {
        try {
            Map<String, Object> result = taskResultService.getTaskResultDetails(taskId, batchNo);
            return new WebResultExt<>(result);
        } catch (Exception e) {
            log.error("获取任务结果详情失败，taskId: {}, batchNo: {}", taskId, batchNo, e);
            return WebResultExt.failure("获取任务结果详情失败: " + e.getMessage());
        }
    }
}
