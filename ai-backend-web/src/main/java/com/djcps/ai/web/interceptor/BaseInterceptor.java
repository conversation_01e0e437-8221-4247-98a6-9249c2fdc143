/*
 * Copyright 2018 huna.com All right reserved. This software is the
 * confidential and proprietary information of huna.com ("Confidential
 * Information"). You shall not disclose such Confidential Information and shall
 * use it only in accordance with the terms of the license agreement you entered
 * into with huna.com.
 */
package com.djcps.ai.web.interceptor;

import com.djcps.ai.common.log.LoggerExt;
import com.djcps.ai.common.result.WebResultExt;
import com.djcps.ai.common.string.StringUtil;
import com.djcps.ai.common.threadlocal.CommonParam;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import template.utils.json.JsonToolUtil;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Objects;

/**
 * 类BaseInterceptor.java 的实现描述：请求过滤基类
 *
 * @author: yeb 2021/9/26 下午6:02
 **/
@Component
@Slf4j
public class BaseInterceptor {

	static Logger logger = LoggerFactory.getLogger(BaseInterceptor.class);

	/**
	 * 验证结果是否正常
	 * 
	 * @param result
	 * @param response
	 * @return
	 * @throws IOException
	 */
	protected boolean validateResult(WebResultExt result, HttpServletResponse response) throws IOException {
		if (null != result && !result.isSuccess()) {
			response.setCharacterEncoding("UTF-8");
			response.setContentType("application/json");
			response.getWriter().print(JsonToolUtil.beanToJson(result));
			return false;
		}
		return true;
	}

	/**
	 * 组装参数
	 * 
	 * @param request
	 * @return
	 */
	protected CommonParam getParamVo(HttpServletRequest request) {
		String param = (String) request.getHeader("commonParam");
		CommonParam commonParamVo = JsonToolUtil.jsonToBean(param, CommonParam.class);
		return commonParamVo;
	}

	/**
	 * 获取id
	 * 
	 * @param request
	 * @return
	 */
	protected static String getRemoteIp(HttpServletRequest request) {
		String ipAddress = request.getHeader("x-forwarded-for");
		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getHeader("Proxy-Client-IP");
		}
		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getHeader("WL-Proxy-Client-IP");
		}
		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getRemoteAddr();
			if (Objects.equals(ipAddress, "127.0.0.1") || Objects.equals(ipAddress, "0:0:0:0:0:0:0:1")) {
				// 根据网卡取本机配置的IP
				InetAddress inet = null;
				try {
					inet = InetAddress.getLocalHost();
					if (inet != null) {
						ipAddress = inet.getHostAddress();
					}
				} catch (UnknownHostException e) {
					log.error(e.getMessage());
				}
			}
		}
		// 对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
		if (ipAddress != null && ipAddress.length() > 15) { // "***.***.***.***".length()
			if (ipAddress.indexOf(",") > 0) {
				ipAddress = ipAddress.substring(0, ipAddress.indexOf(","));
			}
		}
		return ipAddress;

	}

	// 判断浏览器
	public static String getBrowser(HttpServletRequest request) {
		String UserAgent = request.getHeader("USER-AGENT").toLowerCase();
		if (UserAgent != null) {
			if (UserAgent.indexOf("msie") >= 0)
				return "IE";
			if (UserAgent.indexOf("firefox") >= 0)
				return "FF";
			if (UserAgent.indexOf("safari") >= 0)
				return "SF";
		}
		return null;
	}

	/**
	 * 获取入参，get和post，其他的暂不处理
	 * 
	 * @param request
	 * @return
	 */
	public String getParamByRequest(HttpServletRequest request) {
		if (StringUtil.equals(request.getMethod(), HttpMethod.GET.name())) {
			return getParamByGet(request);
		} else if (StringUtil.equals(request.getMethod(), HttpMethod.POST.name())) {
			return getParamByJson(request);
		} else {
			return null;
		}
	}

	/**
	 * get请求入参
	 * 
	 * @param request
	 * @return
	 */
	private String getParamByGet(HttpServletRequest request) {
		try {
			return new String(request.getQueryString().getBytes("iso-8859-1"), "utf-8").replaceAll("%22", "\"");
		} catch (Exception e) {
			LoggerExt.error(logger, "get请求获取参数异常", e);
		}
		return "";
	}

	/**
	 * 获取json数据
	 * 
	 * @param request
	 * @return
	 * @throws IOException
	 */
	private String getParamByJson(HttpServletRequest request) {
		try {
			if (StringUtil.startsWith(request.getContentType(), "application/json")) {
				StringBuffer sb = new StringBuffer();
				InputStream is = request.getInputStream();
				InputStreamReader isr = new InputStreamReader(is);
				BufferedReader br = new BufferedReader(isr);
				String s = "";
				while ((s = br.readLine()) != null) {
					sb.append(s);
				}
				return sb.toString();
			}
		} catch (Exception e) {
			LoggerExt.error(logger, "post请求获取参数异常", e);
		}
		return "";
	}

}
