package com.djcps.ai.web.report;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.djcps.ai.common.exception.SystemBizExcetion;
import com.djcps.ai.common.result.WebResultExt;
import com.djcps.ai.common.threadlocal.CommonParam;
import com.djcps.ai.common.threadlocal.ThreadLocalUser;
import com.djcps.ai.common.vo.PageResult;
import com.djcps.ai.core.param.AuthResponse;
import com.djcps.ai.core.vo.in.UserReportPoliceDto;
import com.djcps.ai.core.vo.in.UserReportPoliceQueryDto;
import com.djcps.ai.dao.entity.UserReportPolice;
import com.djcps.ai.service.UserReportPoliceService;
import com.djcps.ai.service.report.param.SourceParam;
import com.djcps.ai.service.report.param.SystemConfigParam;
import com.djcps.ai.service.report.response.SystemConfigResponse;
import com.djcps.ai.service.system.SystemConfigService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import static com.djcps.ai.common.enums.SystemCodeEnum.MSG_TEMPLATEUS1;

@RestController
@RequestMapping("/system")
@Slf4j
@RequiredArgsConstructor
public class SystemConfigController {
    private final SystemConfigService systemConfigService;
    private final UserReportPoliceService reportPoliceService;

    /**
     * 获取系统配置
     *
     * @param param
     * @return
     */
    @PostMapping("/config.do")
    public WebResultExt<SystemConfigResponse> getSystemConfig(@RequestBody SystemConfigParam param) {
        SystemConfigResponse config = systemConfigService.findByKeyAndParse(param);
        return new WebResultExt(config);
    }

    @PostMapping("/getFeedbackType.do")
    public WebResultExt getSystemConfig(@RequestParam(defaultValue = "feedback_easyorder") String function) {
        SystemConfigParam param = new SystemConfigParam();
        param.setKey(function + "_type");
        SystemConfigResponse config = systemConfigService.findByKeyAndParse(param);
        return new WebResultExt(config.getValue());
    }


    /**
     * 获取功能列表V2
     *
     * @return
     */
    @PostMapping("/v2/getAuthFunctionList.do")
    public WebResultExt<SystemConfigResponse> getAuthFunctionListV2(@RequestBody SourceParam source) {
        SystemConfigResponse config = systemConfigService.getAuthFunctionListV2(source);
        return new WebResultExt(config);
    }

    @PostMapping("getUserInfo.do")
    public WebResultExt<AuthResponse> getUserInfo() {
        CommonParam commonParam = ThreadLocalUser.getCommonParam();
        if (commonParam == null) {
            throw new SystemBizExcetion(MSG_TEMPLATEUS1);
        }
        return new WebResultExt<>(new AuthResponse(commonParam.getUserId(), commonParam.getFphone()));
    }
    @PostMapping("saveReportPolice.do")
    public WebResultExt<Boolean> saveReportPolice(@RequestBody @Valid UserReportPoliceDto reportPolice) {
        reportPoliceService.saveReportPolice(reportPolice);
        return WebResultExt.success();
    }
    @PostMapping("listReportPolice.do")
    public WebResultExt<PageResult<UserReportPolice>> listReportPolice(@RequestBody UserReportPoliceQueryDto queryDto) {
        try {

            // 执行分页查询
            IPage<UserReportPolice> pageResult = reportPoliceService.pageQuery(
                    queryDto.getCurrent(),
                    queryDto.getSize(),
                    queryDto.getUserId(),
                    queryDto.getStatus(),
                    queryDto.getItemName(),
                    queryDto.getFunctionName()
            );

            return new WebResultExt<>(new PageResult(pageResult.getTotal(), pageResult.getRecords()));

        } catch (Exception e) {
            log.error("分页查询TaskResult失败", e);
            return WebResultExt.failure("查询失败: " + e.getMessage());
        }
    }

}
