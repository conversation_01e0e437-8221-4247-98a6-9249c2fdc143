/*
 * Copyright 2019 me.com All right reserved. This software is the confidential and proprietary information of me.com
 * ("Confidential Information"). You shall not disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into with me.com.
 */
package com.djcps.ai.web.interceptor;

import cn.hutool.core.util.StrUtil;
import com.djcps.ai.common.enums.SystemCodeEnum;
import com.djcps.ai.common.log.LoggerExt;
import com.djcps.ai.common.result.WebResultExt;
import com.djcps.ai.common.string.StringUtil;
import com.djcps.ai.common.threadlocal.CommonParam;
import com.djcps.ai.common.threadlocal.ThreadLocalUser;
import com.djcps.ai.core.constants.CommonConstants;
import com.djcps.ai.core.param.AuthResponse;
import com.djcps.ai.service.AuthService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import template.utils.bean.CookiesToolUtil;
import template.utils.json.JsonToolUtil;

/**
 * 
 * 类HeaderInterceptor.java的实现描述：获取请求数据
 * 
 * <AUTHOR> 2021年10月25日 上午9:34:27
 */
@Slf4j
public class HeaderInterceptor extends BaseInterceptor implements HandlerInterceptor {


	@Autowired
	private AuthService authService;
	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * org.springframework.web.servlet.HandlerInterceptor#preHandle(javax.servlet.
	 * http.HttpServletRequest, javax.servlet.http.HttpServletResponse,
	 * java.lang.Object)
	 */
	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
			throws Exception {
		String servletPath = request.getPathInfo();
		if (StrUtil.startWithAnyIgnoreCase(servletPath, "/static/","/tools/","/task/","/template/","/task-invoke-method/")) {
			return true;
		}
		if (StrUtil.endWithIgnoreCase(servletPath, "NoLogin.do")) {
			return true;
		}
		CommonParam param = new CommonParam();
		param.setStarttime(System.currentTimeMillis());
		param.setUrl(request.getRequestURI());
		param.setSource(request.getHeader("source"));
		param.setVersion(request.getHeader("version"));
		param.setLat(request.getHeader("lat"));
		param.setLon(request.getHeader("lon"));
		param.setDevice(request.getHeader("device"));
		param.setIp(getRemoteIp(request));
		String token = CookiesToolUtil.getToken(request);
		String system = request.getHeader(CommonConstants.HEADER_APP_NAME);
		param.setSystem(system);
        //UserInfo userInfo = this.getUserInfoByToken(token);
        param.setToken(token);
        //param.setUserInfo(userInfo);
        //param.setUserId(userInfo!=null?userInfo.getId():null);
        // param.setParams(this.getParamByRequest(request));
		String fuserid = authService.getUserIdFromRequest(request);
		if (StringUtil.isBlank(fuserid)) {
			return validateResult(WebResultExt.failure(SystemCodeEnum.MSG_TEMPLATEUS1), response);
        }else{
			param.setUserId(fuserid);
			try {
				AuthResponse userInfoFromRequest = authService.getUserInfoFromRequest(token, system);
				if (userInfoFromRequest != null) {
					param.setFphone(userInfoFromRequest.getFphone());
				}
			} catch (Exception e) {
				log.error("获取用户信息失败", e);
			}
			ThreadLocalUser.setCommonParam(param);
        }
		return true;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * org.springframework.web.servlet.HandlerInterceptor#postHandle(javax.servlet.
	 * http.HttpServletRequest, javax.servlet.http.HttpServletResponse,
	 * java.lang.Object, org.springframework.web.servlet.ModelAndView)
	 */
	@Override
	public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
			ModelAndView modelAndView) throws Exception {

	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * org.springframework.web.servlet.HandlerInterceptor#afterCompletion(javax.
	 * servlet.http.HttpServletRequest, javax.servlet.http.HttpServletResponse,
	 * java.lang.Object, java.lang.Exception)
	 */
	@Override
	public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
			throws Exception {
		if (ThreadLocalUser.getCommonParam() != null) {
			ThreadLocalUser.getCommonParam().setEndtime(System.currentTimeMillis());
			if (ex != null) {
				LoggerExt.error(logger, ex);
			}
			LoggerExt.info(logger, JsonToolUtil.beanToJson(ThreadLocalUser.getCommonParam()));
			ThreadLocalUser.clear();

		}
	}



//    /**
//     * 获取区域信息
//     */
//    public String getCode(HttpServletRequest request) {
//        return getKey("ocode", request);
//    }
//
//    /**
//     * 获取名称信息
//     */
//    public String getName(HttpServletRequest request) {
//        return getKey("uname", request);
//    }


	public static String getToken(HttpServletRequest request) {
        String token = request.getHeader("token");
        if (ObjectUtils.isEmpty(token)) {
            token = CookiesToolUtil.getCookieByName(request, "token");
        }
        return token;
    }

}
