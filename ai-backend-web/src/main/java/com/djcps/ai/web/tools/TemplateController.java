package com.djcps.ai.web.tools;

import com.djcps.ai.common.result.WebResultExt;
import com.djcps.ai.dao.entity.UserTemplate;
import com.djcps.ai.service.UserTemplateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/template")
@Slf4j
@RequiredArgsConstructor
public class TemplateController {

    private final UserTemplateService templateService;

    @PostMapping("/list")
    public WebResultExt<List<UserTemplate>> listTemplates() {
        List<UserTemplate> list = templateService.list();
        return new WebResultExt<>(list);
    }

    @PostMapping("/save")
    public WebResultExt<UserTemplate> saveTemplate(@RequestBody UserTemplate template) {
        try {
            // 设置用户ID
            template.setUserId("958");

            // 保存或更新模板
            boolean success = templateService.saveOrUpdate(template);

            if (success) {
                return new WebResultExt<>(template);
            } else {
                return WebResultExt.failure("保存模板失败");
            }
        } catch (Exception e) {
            log.error("保存模板失败", e);
            return WebResultExt.failure("保存模板失败: " + e.getMessage());
        }
    }

    @PostMapping("/delete/{id}")
    public WebResultExt<Boolean> deleteTemplate(@PathVariable Long id) {
        try {
            boolean success = templateService.removeById(id);
            return new WebResultExt<>(success);
        } catch (Exception e) {
            log.error("删除模板失败", e);
            return WebResultExt.failure("删除模板失败: " + e.getMessage());
        }
    }
}
