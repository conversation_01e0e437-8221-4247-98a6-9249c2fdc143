package com.djcps.ai.web.interceptor;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.djcps.ai.common.exception.SystemBizExcetion;
import com.djcps.ai.common.threadlocal.AgentInfo;
import com.djcps.ai.common.threadlocal.AgentThreadLocalUtil;
import com.djcps.ai.core.constants.CommonConstants;
import com.djcps.ai.service.AuthService;
import com.djcps.ai.service.system.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.servlet.HandlerInterceptor;
import template.utils.bean.CookiesToolUtil;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import static com.djcps.ai.common.enums.SystemCodeEnum.*;

@Slf4j
public class AiFunctionHeaderInterceptor implements HandlerInterceptor {

    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private AuthService authService;
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String userId = authService.getUserIdFromRequest(request);
        if (StrUtil.isBlankIfStr(userId)) {
            log.error("header fuserid is null");
            throw new SystemBizExcetion(MSG_TEMPLATEUS8);
        }
        String header = request.getHeader(CommonConstants.HEADER_AI_FUNCTION);
        if (StrUtil.isBlankOrUndefined(header)) {
            log.error("header {} is null", CommonConstants.HEADER_AI_FUNCTION);
            throw new SystemBizExcetion(param_null);
        }
        if (StrUtil.equalsIgnoreCase(header, CommonConstants.SYSTEM_CONFIG_KEY_ENABLE_FUNCTIONS_AI_REPORT)) {
            boolean inWhiteList = systemConfigService.checkCurrentUserInWhiteList(userId);
            if (!inWhiteList) {
                log.error("current user is not in white list");
                throw new SystemBizExcetion(PERSON_LIMITED);
            }
        }
        AgentInfo agentInfo = new AgentInfo(header, CookiesToolUtil.getToken(request));
        log.debug("header {} value is:  {}", CommonConstants.HEADER_AI_FUNCTION, JSONUtil.toJsonStr(agentInfo));
        AgentThreadLocalUtil.setContext(agentInfo);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        AgentThreadLocalUtil.removeContext();
        log.debug("remove header {} value", CommonConstants.HEADER_AI_FUNCTION);
    }
}
