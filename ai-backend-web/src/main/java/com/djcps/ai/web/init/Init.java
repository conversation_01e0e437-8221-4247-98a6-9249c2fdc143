package com.djcps.ai.web.init;

import com.djcps.ai.config.env.EnvironmentExt;
import org.apache.logging.log4j.core.tools.picocli.CommandLine.Command;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

@Command
public class Init {

	@Autowired
	private DataSourceTransactionManager transactionManager;

	@Autowired
	private EnvironmentExt environmentExt;

	@Bean
	@ConditionalOnMissingBean
	public TransactionTemplate transactionTemplate() throws Throwable {
		return new TransactionTemplate(this.transactionManager);
	}

}
