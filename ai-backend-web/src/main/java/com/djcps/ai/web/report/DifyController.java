package com.djcps.ai.web.report;

import com.djcps.ai.common.result.WebResultExt;
import com.djcps.ai.common.threadlocal.ThreadLocalUser;
import com.djcps.ai.core.vo.in.*;
import com.djcps.ai.core.vo.in.favorite.AddFavoriteDto;
import com.djcps.ai.core.vo.out.DifyCoversationPageOutDto;
import com.djcps.ai.core.vo.out.DifyEditOutDto;
import com.djcps.ai.core.vo.out.DifyMessagePageOutDto;
import com.djcps.ai.dao.entity.UserFavorite;
import com.djcps.ai.service.UserFavoriteService;
import com.djcps.ai.service.dify.DifyConversationService;
import com.djcps.ai.service.report.param.SubmitQueryParam;
import com.djcps.ai.service.report.response.SubmitQueryResponse;
import com.djcps.ai.service.strategy.SQLResponseProcessor;
import com.djcps.ai.web.dify.vo.DifyEditSessionNameInVo;
import com.djcps.ai.web.dify.vo.DifyMessageFeedbackInVo;
import com.djcps.ai.web.dify.vo.DifyMessageInVo;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;

@RestController
@RequestMapping("/ai")
public class DifyController {

    @Autowired
    private DifyConversationService difyConversationService;
    @Autowired
    private UserFavoriteService favoriteService;

    /**
     * 提交问题,获取数据
     *
     * @param param
     * @return
     */
    @PostMapping("/submit.do")
    public WebResultExt<SubmitQueryResponse> submit(@Valid @RequestBody SubmitQueryParam param) {
        SubmitQueryResponse sendMsgVo = difyConversationService.sendMessageAndExecute(param.toMsg(ThreadLocalUser.getUserId()));
        return new WebResultExt<>(sendMsgVo);
    }
    @PostMapping("/submitStream.do")
    public SseEmitter submitStream(@Valid @RequestBody SubmitQueryParam param) {
       return difyConversationService.sendMessageAndExecuteStream(param.toMsg(ThreadLocalUser.getUserId()));
    }

    @PostMapping("/rename.do")
    public WebResultExt<DifyEditOutDto> rename(@Valid @RequestBody DifyEditSessionNameInVo param) {
        DifyConversationInDto inVo = new DifyConversationInDto();
        inVo.setName(param.getName());
        inVo.setUser(ThreadLocalUser.getUserId());
        DifyEditOutDto result = difyConversationService.renameConversations(param.getConversationId(), inVo);
        return new WebResultExt<>(result);
    }

    @PostMapping("/getAllConversations.do")
    public WebResultExt<DifyCoversationPageOutDto> getAllConversations(@RequestBody DifyConversationsInDto param) {
        if (param == null) {
            param = new DifyConversationsInDto();
        }
        if(param.getLimit()<=0 || param.getLimit()>50){
            param.setLimit(20);
        }
        param.setUser(ThreadLocalUser.getUserId());
        DifyCoversationPageOutDto result = difyConversationService.getAllConversations(param);
        return new WebResultExt<>(result);
    }

    @PostMapping("/feedbacks.do")
    public WebResultExt<DifyEditOutDto> messageFeedbacks(@Valid @RequestBody DifyMessageFeedbackInVo param) {
        DifyFeedbackInDto inVo = new DifyFeedbackInDto();
        inVo.setUser(ThreadLocalUser.getUserId());
        inVo.setRating(param.getRating());
        DifyEditOutDto result = difyConversationService.messageFeedbacks(param.getMessageId(), inVo);
        return new WebResultExt<>(result);
    }

    @PostMapping("/getAllMessage.do")
    public WebResultExt<DifyMessagePageOutDto> getAllMessage(@Valid @RequestBody DifyMessageInVo param) {
        DifyMessageInDto inVo = new DifyMessageInDto();
        inVo.setConversation_id(param.getConversationId());
        inVo.setFirst_id(param.getFirstId());
        if(param.getLimit()<=0 || param.getLimit()>50){
            param.setLimit(20);
        }
        inVo.setLimit(param.getLimit());
        inVo.setUser(ThreadLocalUser.getUserId());
        DifyMessagePageOutDto result = difyConversationService.getAllMessage(inVo);
        return new WebResultExt<>(result);
    }


    @PostMapping("/delAction.do")
    public WebResultExt<DifyEditOutDto> deleteConversation(@Valid @RequestBody DifyConversationDelInDto dto) {
        DifyUserInDto inVo = new DifyUserInDto();
        inVo.setUser(ThreadLocalUser.getUserId());
        DifyEditOutDto result = difyConversationService.deleteConversation(dto.getConversationId(), inVo);
        return new WebResultExt<>(result);
    }

    @PostMapping("addFavorite.do")
    public WebResultExt<Long> addFavorite(@Valid @RequestBody AddFavoriteDto dto) {
        Long id = favoriteService.addFavorite(dto);
        return WebResultExt.successWithData(id);
    }
    @PostMapping("delFavorite.do")
    public WebResultExt<DifyEditOutDto> delFavorite(@Valid @RequestBody IdDto dto) {
        boolean success = favoriteService.removeById(dto.getId());
        return new WebResultExt<>(success);
    }
    @PostMapping("getFavoriteById.do")
    public WebResultExt<SubmitQueryResponse> getFavoriteById(@Valid @RequestBody IdDto dto) {
        SubmitQueryResponse response = favoriteService.getFavoriteById(dto.getId());
        return new WebResultExt<>(response);
    }

    @PostMapping("renameById.do")
    public WebResultExt<SubmitQueryResponse> renameById(@Valid @RequestBody IdAndSingleFieldDto dto) {
        boolean success = favoriteService.renameById(dto);
        return new WebResultExt<>(success);
    }

    @PostMapping("getAllFavorite.do")
    public WebResultExt<List<UserFavorite>> getAllFavorite() {
        List<UserFavorite> list = favoriteService.getAllFavorite(ThreadLocalUser.getUserId());
        return new WebResultExt<>(list);
    }

    @Autowired
    private SQLResponseProcessor sqlResponseProcessor;

//    @PostMapping("run_sqlNoLogin.do")
//    public WebResultExt<Object> getAllFavorite(@RequestBody ChartConfigResult param) {
//        SubmitQueryResponse response = new SubmitQueryResponse();
//        SendMsgVo sendMsgVo = new SendMsgVo();
//        sendMsgVo.setAnswer(JSONUtil.toJsonStr(param));
//        sqlResponseProcessor.process(response, sendMsgVo);
//        return new WebResultExt<>(response);
//    }
}
