package com.djcps.ai.web.interceptor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 权限拦截器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/13 14:08
 */
@Slf4j
public class PermissionInterceptor implements HandlerInterceptor {

//	@Autowired
//	private PermissionService permissionService;
//
//	@Override
//	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
//			throws Exception {
//		LoggerExt.debug(log, "权限开始验证%s", request.getServletPath());
//		boolean hasPermission = hasPermission(handler, request);
//		LoggerExt.debug(log, "权限是否通过%s", hasPermission);
//		// 验证权限
//		return hasPermission;
//	}
//
//	/**
//	 * 是否有权限
//	 *
//	 * @param handler 处理器
//	 * @return 是否过滤
//	 */
//	private boolean hasPermission(Object handler, HttpServletRequest request) throws Exception {
//		String servletPath = request.getServletPath();
//		ScreenInterFace requiredPermission = getAnnotationByMethodOrDeclaringClass(handler);
//		String userId = request.getHeader("fuserid");
//		LoggerExt.info(log, "userId %s", userId);
//		// 如果标记了注解，则判断权限
//		if (requiredPermission != null && requiredPermission.permission()) {
//			if (ObjectUtils.isEmpty(userId)) {
//				return false;
//			}
//			return permissionService.isPermissionServiceByName(servletPath, userId);
//		}
//		return true;
//	}
//
//	@Override
//	public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
//			Exception ex) {
//	}
//
//	/**
//	 * 获取注解
//	 *
//	 * @param handler 处理器
//	 * @return 注解
//	 */
//	private ScreenInterFace getAnnotationByMethodOrDeclaringClass(Object handler) {
//		if (handler instanceof HandlerMethod) {
//			HandlerMethod handlerMethod = (HandlerMethod) handler;
//			// 获取方法上的注解
//			ScreenInterFace annotation = handlerMethod.getMethod().getAnnotation(ScreenInterFace.class);
//			// 如果方法上的注解为空 则获取类的注解
//			if (annotation == null) {
//				return handlerMethod.getMethod().getDeclaringClass().getAnnotation(ScreenInterFace.class);
//			}
//			return annotation;
//		}
//		return null;
//	}
}
