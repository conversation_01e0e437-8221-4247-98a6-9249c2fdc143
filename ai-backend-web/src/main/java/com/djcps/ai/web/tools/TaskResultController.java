package com.djcps.ai.web.tools;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.djcps.ai.common.result.WebResultExt;
import com.djcps.ai.common.vo.PageResult;
import com.djcps.ai.core.vo.in.TaskResultQueryDto;
import com.djcps.ai.dao.entity.TaskResult;
import com.djcps.ai.service.TaskResultService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * TaskResult控制器
 */
@RestController
@RequestMapping("/result")
@Slf4j
@RequiredArgsConstructor
public class TaskResultController {

    private final TaskResultService taskResultService;

    /**
     * 分页查询TaskResult
     * @param queryDto 查询条件
     * @return 分页结果
     */
    @PostMapping(value = {"/page.do","/pageNoLogin.do"})
    public WebResultExt<PageResult<TaskResult>> pageQuery(@RequestBody TaskResultQueryDto queryDto) {
        try {

            // 执行分页查询
            IPage<TaskResult> pageResult = taskResultService.pageQuery(
                queryDto.getCurrent(), 
                queryDto.getSize(), 
                queryDto.getSkey(), 
                queryDto.getCycleType(),
                    queryDto.getBizType()
            );

            return new WebResultExt<>(new PageResult(pageResult.getTotal(), pageResult.getRecords()));
            
        } catch (Exception e) {
            log.error("分页查询TaskResult失败", e);
            return WebResultExt.failure("查询失败: " + e.getMessage());
        }
    }
}
