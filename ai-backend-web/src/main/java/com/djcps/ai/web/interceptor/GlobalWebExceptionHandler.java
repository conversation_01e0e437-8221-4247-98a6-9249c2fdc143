package com.djcps.ai.web.interceptor;

import com.djcps.ai.common.enums.SystemCodeEnum;
import com.djcps.ai.common.exception.*;
import com.djcps.ai.common.log.LoggerExt;
import com.djcps.ai.common.result.WebResultExt;
import com.djcps.ai.common.result.WebResultIntExt;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageConversionException;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.NoHandlerFoundException;

import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

import static com.djcps.ai.common.enums.SystemCodeEnum.DONT_OPERATE;

/**
 * 
 * 类GlobalWebExceptionHandler.java的实现描述：spring boot web 异常统一处理
 * 
 * <AUTHOR> 2021年11月18日 上午10:57:33
 */
@RestControllerAdvice
@Slf4j
public class GlobalWebExceptionHandler extends BaseInterceptor {

	private static final String bad_reques_body_covert_fail = "参数类型不匹配";
	private static final String bad_reques_body_miss = "参数丢失";
	private static final String bad_reques_method_fail = "请求方法类型异常";
	private static final String bad_reques_not_found = "请求方法未找到";

	/**
	 * 参数异常处理
	 * 
	 * @param param
	 * @return
	 */
	@ExceptionHandler(ParamException.class)
	public WebResultExt handleValidationBodyException(ParamException param) {
		if (param != null) {
			return WebResultExt.failure(param.getCode(),param.getMessage());
		} else {
			return WebResultExt.failure(SystemCodeEnum.param_null);
		}
	}

	/**
	 * 业务异常处理
	 *
	 * @param response
	 * @return
	 */
	@ExceptionHandler(SystemBizExcetion.class)
	public WebResultExt handleUnProccessableServiceException(SystemBizExcetion biz, HttpServletResponse response) {
		if (biz != null) {
//			response.setStatus(Integer.valueOf(biz.getCode()));
			return WebResultExt.failure(biz.getCode(), biz.getMessage());
		} else {
			return WebResultExt.failure(SystemCodeEnum.MSG_TEMPLATE1);
		}
	}

	/**
	 * 唯一索引异常处理
	 *
	 * @param biz
	 * @return
	 */
	@ExceptionHandler(DuplicateKeyException.class)
	public WebResultIntExt handleSQLIntegrityConstraintViolationException(DuplicateKeyException biz) {
		if (biz != null) {
			return WebResultIntExt.failure(-1, DONT_OPERATE.getDesc());
		} else {
			return WebResultIntExt.failure(SystemCodeEnum.MSG_TEMPLATE1);
		}
	}

	/**
	 * 业务异常处理,codeNo
	 *
	 * @param biz
	 * @return
	 */
	@ExceptionHandler(SystemNoExcetion.class)
	public WebResultIntExt handleUnProccessableServiceException(SystemNoExcetion biz) {
		if (biz != null) {
			return WebResultIntExt.failure(biz.getCodeNo(), biz.getMessage());
		} else {
			return WebResultIntExt.failure(SystemCodeEnum.MSG_TEMPLATE1);
		}
	}


	@ExceptionHandler(CoreExcetion.class)
	public WebResultExt handleUnProccessableServiceException(CoreExcetion biz, HttpServletResponse response) {
		if (biz != null) {
//			response.setStatus(Integer.valueOf(biz.getCode()));
			return WebResultExt.failure(biz.getCode(), biz.getMessage());
		} else {
			return WebResultExt.failure(SystemCodeEnum.system_third_failed);
		}
	}

	/**
	 * 未找到接口异常处理
	 * 
	 * @param e
	 * @param request
	 * @return
	 */
	@ExceptionHandler(value = NoHandlerFoundException.class)
	public WebResultExt exception(NoHandlerFoundException e, HttpServletRequest request) {
		long now = System.currentTimeMillis();
		String uri = request.getRequestURI();
		String ip = getRemoteIp(request);
		LoggerExt.error(log, "{message:request not found,uri:%s,ip:%s,time:%s}", uri, ip, now);
		return WebResultExt.failure("" + HttpStatus.NOT_FOUND.value(), bad_reques_not_found);
	}

	/**
	 * http method请求不兼容问题
	 * 
	 * @param e
	 * @param request
	 * @return
	 */
	@ExceptionHandler(value = HttpRequestMethodNotSupportedException.class)
	public WebResultExt exception(HttpRequestMethodNotSupportedException e, HttpServletRequest request) {
		long now = System.currentTimeMillis();
		String uri = request.getRequestURI();
		String ip = getRemoteIp(request);
		LoggerExt.error(log, "{message:%s %s,uri:%s,ip:%s,time:%s}", e.getMethod(), e.getMessage(), uri, ip, now);
		return WebResultExt.failure("" + HttpStatus.BAD_REQUEST.value(), bad_reques_method_fail);
	}

	/**
	 * HttpMessageNotReadableException异常拦截
	 * 
	 * @param e
	 * @param request
	 * @param response
	 * @return
	 */
	@ExceptionHandler(value = { HttpMessageNotReadableException.class })
	public WebResultExt handleFilterException(HttpMessageNotReadableException e, HttpServletRequest request,
			ServletResponse response) throws Exception {
		long now = System.currentTimeMillis();
		String uri = request.getRequestURI();
		String ip = getRemoteIp(request);
		LoggerExt.error(log,e);
		LoggerExt.error(log, "{message:%s,uri:%s,ip:%s,time:%s}", e.getMessage(), uri, ip, now);
		return WebResultExt.failure("" + HttpStatus.BAD_REQUEST.value(), bad_reques_body_miss);
	}

	/**
	 * HttpMessageConversionException异常拦截
	 * 
	 * @param e
	 * @param request
	 * @param response
	 * @return
	 */
	@ExceptionHandler(value = { HttpMessageConversionException.class })
	public WebResultExt handleFilterException(HttpMessageConversionException e, HttpServletRequest request,
			ServletResponse response) throws Exception {
		long now = System.currentTimeMillis();
		String uri = request.getRequestURI();
		String ip = getRemoteIp(request);
		LoggerExt.error(log, "{message:%s,uri:%s,ip:%s,time:%s}", e.getMessage(), uri, ip, now);
		return WebResultExt.failure("" + HttpStatus.BAD_REQUEST.value(), bad_reques_body_covert_fail);
	}

	/**
	 * MethodArgumentNotValidException异常拦截
	 * 
	 * @param e
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	@ExceptionHandler(value = { MethodArgumentNotValidException.class })
	public WebResultExt handleFilterException(MethodArgumentNotValidException e, HttpServletRequest request,
			ServletResponse response) throws Exception {
		BindingResult bindingResult = e.getBindingResult();
		List<ObjectError> list = bindingResult.getAllErrors();
		// 获取第一条错误信息
		String defaultMessage = list.get(0).getDefaultMessage();
		long now = System.currentTimeMillis();
		String uri = request.getRequestURI();
		String ip = getRemoteIp(request);
		LoggerExt.error(log, "{message:%s,uri:%s,ip:%s,time:%s}", defaultMessage, uri, ip, now);
		return WebResultExt.failure(defaultMessage);
	}

	/**
	 * Exception异常拦截
	 * 
	 * @param e
	 * @param request
	 * @param response
	 * @return
	 */
	@ExceptionHandler(value = { Exception.class })
	public WebResultExt handleFilterException(Exception e, HttpServletRequest request, ServletResponse response)
			throws Exception {
		String uri = request.getRequestURI();
		String ip = getRemoteIp(request);
		LoggerExt.error(log, "{ip:%s,uri:%s,message:%s}", e, uri, ip, e.getMessage());
		return WebResultExt.failure(SystemCodeEnum.MSG_TEMPLATE1);
	}

	/**
	 * 看板异常
	 * 
	 * @param e
	 * @return
	 */
	@ExceptionHandler(value = { SaleMarketException.class })
	public WebResultExt adviceSaleMarketException(SaleMarketException e) {
		return WebResultExt.failure(e.getMessage());
	}

}
