package com.djcps.ai.web.interceptor;

import com.djcps.ai.common.log.LoggerExt;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 
 * 类TimeInterceptor.java的实现描述：随意定义一个时间日志，后期再换
 * 
 * <AUTHOR> 2021年10月19日 下午2:28:14
 */
@Slf4j
public class TimeInterceptor extends BaseInterceptor implements HandlerInterceptor {

	private ThreadLocal<Map<String, Object>> local = new ThreadLocal<Map<String, Object>>() {

		@Override
		protected java.util.Map<String, Object> initialValue() {
			return new LinkedHashMap<>();
		};
	};

	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
			throws Exception {
		local.get().put("starttime", System.currentTimeMillis());
		local.get().put("url", request.getRequestURI());
		local.get().put("ip", getRemoteIp(request));
		return true;
	}

	@Override
	public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
			@Nullable Exception ex) throws Exception {
		local.get().put("use", (System.currentTimeMillis() - (long) local.get().get("starttime")) + "ms");
		local.get().remove("starttime");
		LoggerExt.info(log, local.get().toString());
		local.remove();
	}

}
