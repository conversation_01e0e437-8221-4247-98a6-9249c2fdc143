/*
 * Copyright 2022 51zjb.com All right reserved. This software is the
 * confidential and proprietary information of 51zjb.com ("Confidential
 * Information"). You shall not disclose such Confidential Information and shall
 * use it only in accordance with the terms of the license agreement you entered
 * into with 51zjb.com.
 */
package com.djcps.ai.web;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 类CartonfactoryStarter.java的实现描述：启动类
 * 
 * <AUTHOR> 2022年7月5日 下午4:40:43
 */
@EnableAsync
@EnableScheduling
@EnableTransactionManagement
@ComponentScan(basePackages = {"com.djcps.ai.*"})
@SpringBootApplication
@MapperScan(value = "com.djcps.ai.dao.mapper")
@EnableFeignClients(basePackages = {"com.djcps.ai.*"})
//@EnableApolloConfig
public class AiBackendStarter {

	public static void main(String[] args) {
		SpringApplication.run(AiBackendStarter.class, args);
	}
}
