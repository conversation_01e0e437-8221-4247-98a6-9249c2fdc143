package com.djcps.ai.web.interceptor;

import com.djcps.ai.common.enums.SystemCodeEnum;
import com.djcps.ai.common.result.WebResultExt;
import com.djcps.ai.common.string.StringUtil;
import com.djcps.ai.common.threadlocal.CommonParam;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 类 ApiPowerInterceptor.java 描述: 权限拦截
 *
 * yeb 2021/9/26 下午6:07
 **/

public class ApiPowerInterceptor extends BaseInterceptor implements HandlerInterceptor {

	// @Autowired
	// 缓存或者从表中获取用户信息

	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object arg2) throws Exception {
		WebResultExt result = this.validate(request);
		return validateResult(result, response);
	}

	/**
	 * 验证参数主要为验证通用参数
	 *
	 * @return
	 */
	private WebResultExt validate(HttpServletRequest request) {
		try {
			CommonParam param = new CommonParam();
			param.setUrl(request.getServletPath());
			Cookie[] cookies = request.getCookies();
			if (null != cookies && 0 != cookies.length) {
				for (Cookie cookie : cookies) {
					if (cookie.getName().equals("token")) {
						param.setToken(cookie.getValue());
					} else if (cookie.getName().equals("userId")) {
						param.setUserId(cookie.getValue());
					}

				}
			}
			if (param.getToken() == null || StringUtil.isBlank(param.getToken())) {
				return WebResultExt.failure(SystemCodeEnum.MSG_TEMPLATEUS2);
			}
			// FiXME 获取用户信息，放入threadlocalUser中

			return WebResultExt.success();
		} catch (Exception e) {
			return WebResultExt.failure(SystemCodeEnum.MSG_TEMPLATE1);
		}

	}

}
