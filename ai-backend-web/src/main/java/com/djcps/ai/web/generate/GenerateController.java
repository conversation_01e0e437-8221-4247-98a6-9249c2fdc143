package com.djcps.ai.web.generate;

import com.djcps.ai.aibackend.tools.GenerateService;
import com.djcps.ai.aibackend.tools.vo.GeneVo;
import com.djcps.ai.service.system.SystemConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

@RestController
@RequestMapping("/generate")
@Slf4j
@RequiredArgsConstructor
public class GenerateController {

    private final GenerateService generateService;
    private final SystemConfigService systemConfigService;
    @PostMapping("/corn")
    public Flux<String> geneCorn(@RequestBody GeneVo geneVo) {
        log.info("收到Cron表达式生成请求: key={}, content={}", geneVo.getKey(), geneVo.getContent());
        String prompt = systemConfigService.findValueByKeyDefault(geneVo.getKey(), "根据用户输入生成标准的corn表达式");
        log.info("使用的系统提示词: {}", prompt);

        return generateService.generateStream(prompt, geneVo.getContent())
                .doOnNext(chunk -> {
                    log.debug("生成的内容块: [{}]", chunk);
                })
                .doOnComplete(() -> {
                    log.info("Cron表达式生成完成");
                })
                .doOnError(error -> {
                    log.error("Cron表达式生成失败", error);
                });
    }
}
