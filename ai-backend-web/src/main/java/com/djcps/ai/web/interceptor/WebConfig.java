/*
 * Copyright 2018 huna.com All right reserved. This software is the confidential and proprietary information of huna.com
 * ("Confidential Information"). You shall not disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into with huna.com.
 */
package com.djcps.ai.web.interceptor;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.servlet.config.annotation.*;

import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 * 类WebConfig.java的实现描述：组装拦截器
 *
 * <AUTHOR> 2018年2月24日 上午11:47:57
 */
@Configuration
@EnableWebMvc
@ComponentScan
public class WebConfig implements WebMvcConfigurer {

	@Bean
	ApiPowerInterceptor getApiPowerInterceptor() {
		return new ApiPowerInterceptor();
	}

	@Bean
	AiFunctionHeaderInterceptor getAiFunctionHeaderInterceptor() {
		return new AiFunctionHeaderInterceptor();
	}

	@Bean
	HeaderInterceptor getHeaderInterceptor() {
		return new HeaderInterceptor();
	}

	@Bean
	PermissionInterceptor getPermissionInterceptor() {
		return new PermissionInterceptor();
	}

	@Bean
	TimeInterceptor getTimeInterceptor() {
		return new TimeInterceptor();
	}

	@Override
	public void addInterceptors(InterceptorRegistry registry) {
		// registry.addInterceptor(getPermissionInterceptor());
		registry.addInterceptor(getTimeInterceptor()).addPathPatterns("/**");
		registry.addInterceptor(getAiFunctionHeaderInterceptor()).addPathPatterns("/ai/**");
		registry.addInterceptor(getHeaderInterceptor()).addPathPatterns("/**").excludePathPatterns("/swagger-ui/**",
				"/v3/api-docs/**", "/swagger-ui.html");
	}

	@Override
	public void addResourceHandlers(ResourceHandlerRegistry registry) {
		// 保持Spring Boot默认的静态资源处理，只添加必要的映射
		registry.addResourceHandler("/static/**").addResourceLocations("classpath:/static/");
	}

	@Override
	public void addCorsMappings(CorsRegistry registry) {
		registry.addMapping("/**")
				.allowCredentials(true)
				.allowedHeaders("*")
				.allowedOriginPatterns("*") // 使用allowedOriginPatterns替代allowedOrigins以支持动态域名
				.allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH");
	}

	/**
	 *
	 * @Description: 统一处理转json中的日期和Long属性
	 * @Author: yeb
	 * @Param:
	 * @Return:
	 * @Date: 2021/10/11 下午2:43
	 **/
	@Override
	public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
		converters.add(new StringHttpMessageConverter(StandardCharsets.UTF_8));
		MappingJackson2HttpMessageConverter jackson2HttpMessageConverter = new MappingJackson2HttpMessageConverter();
		ObjectMapper objectMapper = new ObjectMapper();
		SimpleModule simpleModule = new SimpleModule();
		simpleModule.addSerializer(Long.class, ToStringSerializer.instance);
		simpleModule.addSerializer(Long.TYPE, ToStringSerializer.instance);
		objectMapper.registerModule(simpleModule);
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
		jackson2HttpMessageConverter.setObjectMapper(objectMapper);
		converters.add(jackson2HttpMessageConverter);
	}

	/**
	 * 配置异步任务执行器
	 *
	 * @return AsyncTaskExecutor
	 */
	@Bean
	public AsyncTaskExecutor asyncTaskExecutor() {
		ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
		// 核心线程数
		executor.setCorePoolSize(10);
		// 最大线程数
		executor.setMaxPoolSize(50);
		// 队列容量
		executor.setQueueCapacity(200);
		// 线程名前缀
		executor.setThreadNamePrefix("async-task-");
		// 线程空闲时间（秒）
		executor.setKeepAliveSeconds(60);
		// 拒绝策略：由调用线程处理该任务
		executor.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy());
		// 等待所有任务结束后再关闭线程池
		executor.setWaitForTasksToCompleteOnShutdown(true);
		// 等待时间（秒）
		executor.setAwaitTerminationSeconds(60);
		executor.initialize();
		return executor;
	}

	/**
	 * 配置异步支持
	 *
	 * @param configurer AsyncSupportConfigurer
	 */
	@Override
	public void configureAsyncSupport(AsyncSupportConfigurer configurer) {
		// 设置异步任务执行器
		configurer.setTaskExecutor(asyncTaskExecutor());
		// 设置异步请求超时时间（毫秒）
		configurer.setDefaultTimeout(180000); // 3分钟
	}

}
