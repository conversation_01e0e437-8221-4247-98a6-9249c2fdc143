<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.dongjing</groupId>
		<artifactId>ai-backend</artifactId>
		<version>1.0-SNAPSHOT</version>
	</parent>

	<artifactId>ai-backend-web</artifactId>
	<name>ai-backend-web</name>
	<packaging>jar</packaging>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<maven.compiler.source>17</maven.compiler.source>
		<maven.compiler.target>17</maven.compiler.target>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.dongjing</groupId>
			<artifactId>ai-backend-service</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.djcps.ai</groupId>
			<artifactId>ai-backend-tools</artifactId>
			<version>0.0.1-SNAPSHOT</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-thymeleaf</artifactId>
			<version>3.5.0</version>
		</dependency>
	</dependencies>

	<profiles>
		<!-- 开发环境 -->
		<profile>
			<id>local</id>
			<properties>
				<package.environment>local</package.environment>
			</properties>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
		</profile>
		<profile>
			<id>dev</id>
			<properties>
				<package.environment>dev</package.environment>
			</properties>
		</profile>
		<profile>
			<id>aliyun-test</id>
			<properties>
				<package.environment>aliyun-test</package.environment>
			</properties>
		</profile>
		<profile>
			<id>exp</id>
			<properties>
				<package.environment>exp</package.environment>
			</properties>
		</profile>
		<profile>
			<id>pro</id>
			<properties>
				<package.environment>pro</package.environment>
			</properties>
		</profile>
	</profiles>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<artifactId>maven-antrun-plugin</artifactId>
				<executions>
					<execution>
						<id>copy</id>
						<phase>package</phase>
						<configuration>
							<tasks>
								<copy todir="../target/">
									<fileset dir="${project.build.directory}">
										<include name="${project.artifactId}-${project.version}.jar" />
									</fileset>
								</copy>
							</tasks>
						</configuration>
						<goals>
							<goal>run</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>

		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>true</filtering>
				<includes>
					<include>application.yml</include>
					<include>application-${package.environment}.yml</include>
					<include>**/*.*</include>
				</includes>
			</resource>
		</resources>
	</build>
</project>
