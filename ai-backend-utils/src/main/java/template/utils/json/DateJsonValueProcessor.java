package template.utils.json;

import net.sf.json.JsonConfig;
import net.sf.json.processors.JsonValueProcessor;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 类DateJsonValueProcessor.java的实现描述：日期类型转换
 * 
 * <AUTHOR> 2018年3月22日 下午1:26:22
 */
public class DateJsonValueProcessor implements JsonValueProcessor {

    private final String format;

    public DateJsonValueProcessor(){
        this.format = "yyyy-MM-dd";
    }

    public DateJsonValueProcessor(String format){
        this.format = format;
    }

    public Object processArrayValue(Object value, JsonConfig jsonConfig) {
        return null;
    }

    public Object processObjectValue(String key, Object value, JsonConfig jsonConfig) {
        if (value == null) {
            return "";
        } else if (value instanceof Date) {
            SimpleDateFormat formate = new SimpleDateFormat(format);
            return formate.format((Date) value);
        }
        return value.toString();
    }
}
