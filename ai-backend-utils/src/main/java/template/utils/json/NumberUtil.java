package template.utils.json;

import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

/**
 * 类NumberUtil.java的实现描述：数字工具类，提供数值类型和其他类型的转换
 * 
 * <AUTHOR> 2018年3月22日 下午1:26:42
 */

public final class NumberUtil {

	private NumberUtil() {
	}

	public static final int HEX = 16;

	public static final int DEICAML = 10;

	/**
	 * 字符串 转换成 整数
	 *
	 * @param str
	 * @return
	 */
	public static Integer toInteger(String str) {
		int result = 0;
		try {
			result = Integer.valueOf(str);
		} catch (Exception e) {
			result = 0;
		}
		return result;
	}

	/**
	 * 字符串 转换成 Double
	 *
	 * @param str
	 * @return
	 */
	public static Double toDouble(String string) {
		Double result = null;
		try {
			result = Double.valueOf(string);
		} catch (Exception e) {
			result = 0.0;
		}
		return result;
	}

	/**
	 * 字符串 转换成 Double
	 *
	 * @param str
	 * @return
	 */
	public static Double toDouble(String string, Double df) {
		Double result = null;
		try {
			result = Double.valueOf(string);
		} catch (Exception e) {
			result = df;
		}
		return result;
	}

	/**
	 * 转换失败默认为0
	 *
	 * @param string
	 * @param radix
	 * @return
	 */
	public static int toInteger(String string, int radix) {
		int result = 0;
		try {
			result = Integer.valueOf(string, radix);
		} catch (Exception e) {
			result = 0;
		}
		return result;
	}

	/**
	 * 转换失败默认0
	 *
	 * @param string
	 * @return
	 */
	public static float toFloat(String string) {
		float result = 0;
		try {
			result = Float.valueOf(string);
		} catch (Exception e) {
			result = 0;
		}
		return result;
	}

	/**
	 * 转换失败默认为BigDecimal.ZERO
	 *
	 * @param string
	 * @return
	 */
	public static BigDecimal toBigDecimal(String string) {
		try {
			return new BigDecimal(string);
		} catch (Exception e) {
			return BigDecimal.ZERO;
		}
	}

	/**
	 * 转换失败默认0
	 *
	 * @param string
	 * @return
	 */
	public static long toLong(String string) {
		long result = 0;
		try {
			result = Long.valueOf(string);
		} catch (Exception e) {
			result = 0;
		}
		return result;
	}

	/**
	 * 数据显示格式化,如果number为null,默认按照0处理
	 *
	 * @return
	 */
	public static String numberFormat(Number number, String format) {
		DecimalFormat decimalFormat = new DecimalFormat(format);
		if (number != null) {
			return decimalFormat.format(number);
		} else {
			return decimalFormat.format(0);
		}
	}

	/**
	 * double相加
	 *
	 * @param d1
	 * @param d2
	 * @return
	 */
	public static double add(double d1, double d2) {
		BigDecimal bD1 = BigDecimal.valueOf(d1);
		BigDecimal bD2 = BigDecimal.valueOf(d2);
		bD1 = bD1.add(bD2);
		return bD1.doubleValue();
	}

	/**
	 * BigDecimal类型数据相加
	 *
	 * @param bd1
	 * @param bd2
	 * @return
	 */
	public static BigDecimal add(BigDecimal... bds) {
		BigDecimal rt = null;
		for (int i = 0; i < bds.length; i++) {
			if (bds[i] != null) {
				if (rt == null) {
					rt = bds[i];
				} else {
					rt = rt.add(bds[i]);
				}
			}
		}
		return rt;
	}

	/**
	 * 获取最大的值
	 * 
	 * @param shorts
	 * @return
	 */
	public static Integer getMaxData(Integer... ints) {
		Integer temp = null;
		for (int i = 0; i < ints.length; i++) {
			if (ints[i] != null) {
				if (temp == null || temp < ints[i]) {
					temp = ints[i];
				}
			}
		}
		return temp;
	}

	/**
	 * 获取最小的值
	 * 
	 * @param shorts
	 * @return
	 */
	public static Integer getMinData(Integer... ints) {
		Integer temp = null;
		for (int i = 0; i < ints.length; i++) {
			if (ints[i] != null) {
				if (temp == null || temp > ints[i]) {
					temp = ints[i];
				}
			}
		}
		return temp;
	}

	/**
	 * 格式化BigDecimal型
	 *
	 * @param d
	 * @return
	 */
	public static String getFormatData(BigDecimal d) {
		if (d == null) {
			return null;
		}
		DecimalFormat df = new DecimalFormat(",##0.00");
		df.setRoundingMode(RoundingMode.HALF_UP);
		return df.format(d);
	}

	/**
	 * 根据字符串转换成Long值<br>
	 * 字符串格式有误，或为空则返回null
	 * 
	 * @param longStr
	 * @return
	 */
	public static Long getLongValueFromString(String longStr) {
		longStr = longStr.trim();
		if (StringUtils.isBlank(longStr)) {
			return null;
		}
		Long value = null;
		try {
			value = Long.parseLong(longStr);
		} catch (Exception e) {
			return null;
		}
		return value;
	}
}
