package template.utils.json;

import net.sf.json.JsonConfig;
import net.sf.json.processors.JsonValueProcessor;

/**
 * 类ShortJsonValueProcessor.java的实现描述：Short类型转换
 * 
 * <AUTHOR> 2018年3月22日 下午1:26:59
 */
public class ShortJsonValueProcessor implements JsonValueProcessor {

    public Object processArrayValue(Object value, JsonConfig jsonConfig) {
        return null;
    }

    public Object processObjectValue(String key, Object value, JsonConfig jsonConfig) {
        if (value == null) {
            return "";
        } else if (value instanceof Short) {
            return value.toString();
        }
        return value.toString();
    }
}
