package template.utils.json;

import com.djcps.ai.common.asserts.ParamAssert;
import com.djcps.ai.common.enums.SystemCodeEnum;
import lombok.extern.slf4j.Slf4j;
import net.sf.ezmorph.object.DateMorpher;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;
import net.sf.json.processors.JsonValueProcessor;
import net.sf.json.util.CycleDetectionStrategy;
import net.sf.json.util.JSONUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.Map.Entry;

/**
 * 类JsonUtil.java的实现描述：json工具类
 * 
 * <AUTHOR> 2018年3月22日 下午1:25:50
 */
@Slf4j
public class JsonToolUtil {

	public static final String                       DEFAULT_DATE_PATTERN        = "yyyy-MM-dd";
	public static final String                       FULL_DATE_PATTERN           = "yyyy-MM-dd HH:mm:ss";
	public static final String                       EMPTY_LIST_JSON             = "[]";
	public static final String                       EMPTY_MAP_JSON              = "{}";
	public static final JsonValueProcessor           DATE_YMD_VALUE_PROCESSOR    = new DateJsonValueProcessor();
	public static final JsonValueProcessor       DATE_YMDHMS_VALUE_PROCESSOR = new DateJsonValueProcessor(
			FULL_DATE_PATTERN);
	public static final JsonValueProcessor BIGDECIMAL_VALUE_PROCESSOR  = new BigDecimalJsonValueProcessor();
	public static final JsonValueProcessor      SHORT_VALUE_PROCESSOR       = new ShortJsonValueProcessor();

	private JsonToolUtil() {
		super();
	}

	/**
	 * 从一个JSON 对象字符格式中得到一个java对象<br>
	 * 其中beansList是一类的集合，形如： {"id" : idValue, "name" : nameValue, "aBean" :
	 * {"aBeanId" : aBeanIdValue, ...}}<br>
	 *
	 * @param jsonString
	 * @param clazz
	 * @param map        集合属性的类型 (key : 集合属性名, value : 集合属性类型class) eg: ("aBean" :
	 *                   Bean.class)
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public static <T> T jsonToBean(String jsonString, Class<T> clazz, Map map) {
		if (StringUtils.isBlank(jsonString)) {
			return null;
		}
		setDataFormat();
		JSONObject jsonObject = JSONObject.fromObject(jsonString);
		return (T) JSONObject.toBean(jsonObject, clazz, map);
	}

	@SuppressWarnings("unchecked")
	public static <T> T jsonToBean(String jsonString, Class<T> clazz) {
		if (StringUtils.isBlank(jsonString)) {
			return null;
		}
		setDataFormat();
		JSONObject jsonObject = JSONObject.fromObject(jsonString);
		return (T) JSONObject.toBean(jsonObject, clazz);
	}

	public static String beanToJson(Object object) {
		return beanToJson(object, FULL_DATE_PATTERN);
	}

	private static void setDataFormat() {
		JSONUtils.getMorpherRegistry()
				.registerMorpher(new DateMorpher(new String[] { DEFAULT_DATE_PATTERN, FULL_DATE_PATTERN }));
	}

	public static String beanToJson(Object object, String dateFormt) {
		String jsonString = null;
		// 日期值处理器
		JsonConfig jsonConfig = new JsonConfig();
		jsonConfig.registerJsonValueProcessor(java.util.Date.class, new DateJsonValueProcessor(dateFormt));
		if (object != null) {
			if (object instanceof Collection || object instanceof Object[]) {
				jsonString = JSONArray.fromObject(object, jsonConfig).toString();
			} else {
				jsonString = JSONObject.fromObject(object, jsonConfig).toString();
			}
		}
		return jsonString == null ? EMPTY_MAP_JSON : jsonString;
	}

	@SuppressWarnings("unchecked")
	public static <T> T jsonToConllection(String jsonString, Object obj) {
		try {
			if (StringUtils.isBlank(jsonString)) {
				return null;
			}
			setDataFormat();
			JSONArray jsonObject = JSONArray.fromObject(jsonString);
			return (T) JSONArray.toList(jsonObject, obj, new JsonConfig());
		} catch (Exception e) {
			log.error("json转换异常", e);
			ParamAssert.isTrue(true, SystemCodeEnum.swap_exception);
		}
		return null;
	}

	/**
	 * 将json字符串转换为MAP，其中jsonString是由mapToJson 方法转换而来的。
	 * 
	 * @param jsonString
	 * @return
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public static HashMap json2Map(String strJson) {
		JSONObject jsonObject = JSONObject.fromObject(strJson);
		HashMap hashMap = new HashMap();
		for (Iterator it = jsonObject.entrySet().iterator(); it.hasNext();) {
			Map.Entry entry = (Map.Entry) it.next();
			String propertyName = entry.getKey().toString();
			String propertyValue = entry.getValue().toString();
			hashMap.put(propertyName, propertyValue);
		}
		return hashMap;
	}

	/**
	 * 将Map对象转换为JSON字符串（支持复杂的对象类型）
	 * 
	 * @param map
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public static String map2Json(Map hashMap, String[] excludes, String datePattern) {
		if (datePattern == null) {
			datePattern = FULL_DATE_PATTERN;
		}
		if (excludes == null) {
			excludes = new String[0];
		}
		JsonConfig jsonConfig = configJson(excludes, datePattern);
		JSONObject jsonObject = JSONObject.fromObject(hashMap, jsonConfig);
		return jsonObject.toString();
	}

	/**
	 * 将Map对象转换为JSON字符串（支持复杂的对象类型）
	 * 
	 * @param map
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public static String map2Json(Map hashMap) {
		return map2Json(hashMap, null, null);
	}

	public static JsonConfig configJson(String[] excludes, String datePattern) {
		JsonConfig jsonConfig = new JsonConfig();
		jsonConfig.setExcludes(excludes);
		jsonConfig.setIgnoreDefaultExcludes(false);
		jsonConfig.setCycleDetectionStrategy(CycleDetectionStrategy.LENIENT);
		jsonConfig.registerJsonValueProcessor(Date.class, new DateJsonValueProcessor(datePattern));

		return jsonConfig;
	}

	/**
	 * 在strJson字符串中增加属性及值，再返回
	 *
	 * @param strJson
	 * @param key
	 * @param value
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static String setStrJsonProp(String strJson, String key, Object value) {

		Map<String, Object> map = new HashMap<String, Object>();
		if (StringUtils.isNotBlank(strJson)) {
			map = json2Map(strJson);
		}
		if (StringUtils.isNotBlank(key)) {
			map.put(key, value);
		}
		return map2Json(map, null, null);
	}

	/**
	 * 在strJson字符串中增加多个属性及值，再返回
	 *
	 * @param strJson
	 * @param key
	 * @param value
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static String setStrJsonProps(String strJson, Map<String, Object> propsMap) {
		Map<String, Object> map = new HashMap<String, Object>();
		if (StringUtils.isNotBlank(strJson)) {
			map = json2Map(strJson);
		}
		if (propsMap != null && !propsMap.isEmpty()) {
			for (Iterator<Entry<String, Object>> iterator = propsMap.entrySet().iterator(); iterator.hasNext();) {
				Entry<String, Object> entry = iterator.next();
				if (entry.getValue() != null) {
					map.put(entry.getKey(), entry.getValue());
				}
			}
		}
		return map2Json(map, null, null);
	}

	/**
	 * 返回strJson字符串中指定属性的值
	 *
	 * @param strJson
	 * @param key
	 * @param value
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static Object getJsonValue(String strJson, String key) {
		if (StringUtils.isEmpty(strJson) || StringUtils.isEmpty(key)) {
			return null;
		}
		Map<String, Object> map = json2Map(strJson);
		return map.get(key);
	}

	/**
	 * 返回strJson字符串中指定属性的值
	 *
	 * @param strJson
	 * @param key
	 * @param value
	 * @return
	 */
	public static String getJsonStrValue(String strJson, String key) {
		Object object = getJsonValue(strJson, key);
		if (object == null) {
			return null;
		}
		return String.valueOf(object);
	}

	/**
	 * 返回strJson字符串中指定属性的Long值
	 *
	 * @param strJson
	 * @param key
	 * @param value
	 * @return
	 */
	public static Long getJsonLongValue(String strJson, String key) {
		Object object = getJsonValue(strJson, key);
		if (object == null || "".equals(object)) {
			return null;
		}
		return Long.valueOf(String.valueOf(object));
	}

}
