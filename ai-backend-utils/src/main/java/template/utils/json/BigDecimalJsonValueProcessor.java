package template.utils.json;

import net.sf.json.JsonConfig;
import net.sf.json.processors.JsonValueProcessor;

import java.math.BigDecimal;

/**
 * 类BigDecimalJsonValueProcessor.java的实现描述：日期类型转换
 * 
 * <AUTHOR> 2018年3月22日 下午1:27:19
 */
public class BigDecimalJsonValueProcessor implements JsonValueProcessor {

	public Object processArrayValue(Object value, JsonConfig jsonConfig) {
		return null;
	}

	public Object processObjectValue(String key, Object value, JsonConfig jsonConfig) {
		if (value == null) {
			return "";
		} else if (value instanceof BigDecimal) {
			return NumberUtil.getFormatData((BigDecimal) value);
		}

		return value.toString();
	}
}
