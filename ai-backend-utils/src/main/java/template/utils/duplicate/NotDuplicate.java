/*
 * Copyright 2019 me.com All right reserved. This software is the
 * confidential and proprietary information of me.com ("Confidential
 * Information"). You shall not disclose such Confidential Information and shall
 * use it only in accordance with the terms of the license agreement you entered
 * into with me.com.
 */
package template.utils.duplicate;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 
 * 类NotDuplicateAop.java的实现描述：防止重复aop
 * 
 * <AUTHOR> 2022年7月27日 下午16:50:01
 */
@Target({ ElementType.METHOD })
@Retention(RetentionPolicy.RUNTIME)
public @interface NotDuplicate {

	/** 简单同名字互斥 */
	String name() default "";
}
