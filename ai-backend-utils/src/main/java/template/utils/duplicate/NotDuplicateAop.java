/*
 * Copyright 2019 me.com All right reserved. This software is the confidential and proprietary information of me.com
 * ("Confidential Information"). You shall not disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into with me.com.
 */
package template.utils.duplicate;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * 
 * 类NotDuplicateAop.java的实现描述：防止重复aop
 * 
 * <AUTHOR> 2022年7月27日 下午17:01:57
 */
@Aspect
@Component
@Slf4j
public class NotDuplicateAop {

	//private static final Set<String> KEY = new ConcurrentSkipListSet<>();
	//private static final Set<String> HUCHI = new ConcurrentSkipListSet<>();

	/*@Pointcut("execution(* com.dongjing.sale.service.impl.*.*(..))")
	public void duplicate() {
	}*/

	/**
	 * 对方法拦截后进行参数验证
	 * 
	 * @param pjp
	 * @return
	 * @throws Throwable
	 */
	/*@Around("duplicate()")
	public Object duplicate(ProceedingJoinPoint pjp) throws Throwable {

		String userid = ThreadLocalUser.getCommonParam().getUserId();
		MethodSignature msig = (MethodSignature) pjp.getSignature();
		Method currentMethod = pjp.getTarget().getClass().getMethod(msig.getName(), msig.getParameterTypes());
		if (StringUtil.isBlank(userid)) {
			LoggerExt.warn(log, "方法%s因为无用户id无法防重复处理.", currentMethod);
			return pjp.proceed();
		}
		NotDuplicate tranMethod = currentMethod.getAnnotation(NotDuplicate.class);
		if (tranMethod == null) {
			return pjp.proceed();
		}
		String name = tranMethod.name();
		String pixName = null;
		String sign = null;
		try {
			if (StringUtil.isNotBlank(name)) {
				pixName = new StringBuffer(userid).append("_").append(name).toString();
				boolean huchi = HUCHI.add(pixName);
				BizAssert.isTrue(!huchi, SystemCodeEnum.system_duplicate_failed);
			}
			// 拼接签名
			StringBuilder sb = new StringBuilder(currentMethod.toString());
			Object[] args = pjp.getArgs();
			for (Object object : args) {
				if (object != null) {
					sb.append(object.getClass().toString());
					sb.append(object.toString());
				}
			}
			sb.append(ThreadLocalUser.getCommonParam().getUserId());
			sign = sb.toString();
			LoggerExt.info(log, sign);
			boolean success = KEY.add(sign);
			if (!success) {
				BizAssert.isTrue(!success, SystemCodeEnum.system_duplicate_failed);
			}
			return pjp.proceed();
		} finally {
			if (CollectionUtil.isNotEmpty(KEY) && sign != null && KEY.contains(sign)) {
				KEY.remove(sign);
			}
			if (CollectionUtil.isNotEmpty(HUCHI) && pixName != null && HUCHI.contains(pixName)) {
				HUCHI.remove(pixName);
			}
		}

	}*/

}
