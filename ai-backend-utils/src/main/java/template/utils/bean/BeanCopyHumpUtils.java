/*
 * Copyright 2022 51zjb.com All right reserved. This software is the
 * confidential and proprietary information of 51zjb.com ("Confidential
 * Information"). You shall not disclose such Confidential Information and shall
 * use it only in accordance with the terms of the license agreement you entered
 * into with 51zjb.com.
 */
package template.utils.bean;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.StrUtil;
import com.djcps.ai.common.log.LoggerExt;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.cglib.core.ReflectUtils;

import java.beans.PropertyDescriptor;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 类BeanCopyHumpUtils.java的实现描述：对象copy，支持驼峰
 * 
 * <AUTHOR> 2022年7月11日 下午1:42:07
 */
@Slf4j
public class BeanCopyHumpUtils {

	/**
	 * 借用缓存，减少创建对象消耗
	 */
	private static final Map<Class<?>, CopyOptions> cacheMap = new ConcurrentHashMap<>();

	/**
	 * 驼峰转换
	 *
	 * @param source
	 * @param target
	 * @param <K>
	 * @param <T>
	 * @return
	 */
	public static <K, T> T copyAndParse(K source, Class<T> target) {
		try {
			T res = target.newInstance();
			BeanUtil.copyProperties(source, res, getCopyOptions(source.getClass()));
			return res;
		} catch (Exception e) {
			LoggerExt.error(log, e);
		}
		return null;
	}

	/**
	 * 驼峰集合转化
	 * 
	 * @param input
	 * @param clazz
	 * @param <E>
	 * @param <T>
	 * @return
	 */
	public static <E, T> List<T> copyAndParseToList(List<E> input, Class<T> clazz) {
		List<T> output = Lists.newArrayList();
		if (CollectionUtils.isNotEmpty(input)) {
			for (E source : input) {
				T target = BeanUtils.instantiateClass(clazz);
				BeanUtil.copyProperties(source, target, getCopyOptions(source.getClass()));
				output.add(target);
			}
		}
		return output;
	}

	/**
	 * 加入map
	 * 
	 * @param source
	 * @return
	 */
	private static CopyOptions getCopyOptions(Class<?> source) {
		CopyOptions options = cacheMap.get(source);
		if (options == null) {
			// 不加锁，我们认为重复执行不会比并发加锁带来的开销大
			options = CopyOptions.create().setFieldMapping(buildFieldMapper(source));
			cacheMap.put(source, options);
		}
		return options;
	}

	/**
	 * 具体实现
	 * 
	 * @return
	 */
	private static Map<String, String> buildFieldMapper(Class<?> source) {
		PropertyDescriptor[] properties = ReflectUtils.getBeanProperties(source);
		Map<String, String> map = new HashMap<>();
		for (PropertyDescriptor target : properties) {
			String name = target.getName();
			String camel = StrUtil.toCamelCase(name);
			if (!name.equalsIgnoreCase(camel)) {
				map.put(name, camel);
			}
			String under = StrUtil.toUnderlineCase(name);
			if (!name.equalsIgnoreCase(under)) {
				map.put(name, under);
			}
		}
		return map;
	}

	/**
	 * 对象属性拷贝 将源对象的属性拷贝到目标对象
	 *
	 * @param source 源对象
	 * @param target 目标对象
	 */
	public static void copyProperties(Object source, Object target) {
		BeanUtils.copyProperties(source, target);
	}

	/**
	 * @param input 输入集合
	 * @param clazz 输出集合类型
	 * @param <E>   输入集合类型
	 * @param <T>   输出集合类型
	 * @return 返回集合
	 */
	public static <E, T> List<T> convertListToList(List<E> input, Class<T> clazz) {
		List<T> output = Lists.newArrayList();
		if (CollectionUtils.isNotEmpty(input)) {
			for (E source : input) {
				T target = BeanUtils.instantiateClass(clazz);
				BeanUtils.copyProperties(source, target);
				output.add(target);
			}
		}
		return output;
	}
}
