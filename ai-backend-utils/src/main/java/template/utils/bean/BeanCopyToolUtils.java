/*
 * Copyright 2022 51zjb.com All right reserved. This software is the
 * confidential and proprietary information of 51zjb.com ("Confidential
 * Information"). You shall not disclose such Confidential Information and shall
 * use it only in accordance with the terms of the license agreement you entered
 * into with 51zjb.com.
 */
package template.utils.bean;

import org.springframework.cglib.beans.BeanCopier;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 类BeanCopyUtils.java的实现描述：对象copy
 * 
 * <AUTHOR> 2022年7月7日 上午9:27:26
 */
public class BeanCopyToolUtils {

	private static final Map<String, BeanCopier> beanCopierCache = new ConcurrentHashMap<>();

	/**
	 * 类型转化
	 * 
	 * @param <T>
	 * @param source      源对象
	 * @param targetClass 目标类
	 * @return
	 */
	public static <T> T copyProperties(Object source, Class<T> targetClass) {
		T t = null;
		try {
			t = targetClass.newInstance();
		} catch (InstantiationException | IllegalAccessException e) {
			throw new RuntimeException(
					String.format("bean copy Create new instance of %s failed: %s", targetClass, e.getMessage()));
		}
		copyProperties(source, t);
		return t;
	}

	private static void copyProperties(Object source, Object target) {
		BeanCopier copier = getBeanCopier(source.getClass(), target.getClass());
		copier.copy(source, target, null);
	}

	private static BeanCopier getBeanCopier(Class<?> sourceClass, Class<?> targetClass) {
		String beanKey = generateKey(sourceClass, targetClass);
		BeanCopier copier = null;
		if (!beanCopierCache.containsKey(beanKey)) {
			copier = BeanCopier.create(sourceClass, targetClass, false);
			beanCopierCache.put(beanKey, copier);
		} else {
			copier = beanCopierCache.get(beanKey);
		}
		return copier;
	}

	private static String generateKey(Class<?> class1, Class<?> class2) {
		return class1.toString() + class2.toString();
	}

}
