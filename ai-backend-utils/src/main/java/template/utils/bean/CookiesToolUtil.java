package template.utils.bean;

import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * @author:wangxiu
 * @date:2022/8/18
 */
@Component
public class CookiesToolUtil {
    public static final String TOKEN = "token";

    public static String getToken(HttpServletRequest request) {
        String token = request.getHeader(TOKEN);
        if (ObjectUtils.isEmpty(token)) {
            token = CookiesToolUtil.getCookieByName(request, TOKEN);
        }
        return token;
    }
    /**
     * 根据名字获取cookie
     *
     * @param request HttpServletRequest
     * @param name    String
     * @return String
     * <AUTHOR>
     * @since 2017年11月13日
     */
    public static String getCookieByName(HttpServletRequest request, String name) {
        Map<String, Cookie> cookieMap = readCookieMap(request);
        if (cookieMap.containsKey(name)) {
            Cookie cookie = cookieMap.get(name);
            return cookie.getValue();
        } else {
            return null;
        }

    }

    /**
     * 将cookie封装到Map里面
     *
     * @param request HttpServletRequest
     * @return Map
     * <AUTHOR>
     * @since 2017年11月13日
     */
    private static Map<String, Cookie> readCookieMap(HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        Map<String, Cookie> cookieMap;
        if (cookies == null) {
            cookieMap = new HashMap<>(16);
        } else {
            cookieMap = Stream.of(cookies).collect(Collectors.toMap(Cookie::getName, cookie -> cookie, (a, b) -> b));
        }
        return cookieMap;
    }
}
