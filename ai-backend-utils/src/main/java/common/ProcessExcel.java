package common;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;

import java.util.ArrayList;
import java.util.List;

public class ProcessExcel {
    public static void main(String args[]) {
        String httpPre = "https://djcps.oss-cn-hangzhou.aliyuncs.com/";
        String outFormat = "问题={};;;答案={};;;文件=";
        ExcelReader reader = cn.hutool.poi.excel.ExcelUtil.getReader("/Users/<USER>/Desktop/知识库/易纸箱.xlsx");
        List<Knowledge> read = reader.readAll(Knowledge.class);
        for (Knowledge knowledge : read) {
            String url = knowledge.getFileUrl();
            if (StrUtil.isBlankIfStr(url)) {
                continue;
            }
            List<String> fileList = new ArrayList<>();
            if (StrUtil.contains(url, ",")) {
                String[] split = url.split(",");
                for (String s1 : split) {
                    addIfNotExist(knowledge, s1, fileList);
                }
            } else {
                addIfNotExist(knowledge, url, fileList);
            }

            List<String> processedList = new ArrayList();
            for (String p : fileList) {
                if (StrUtil.startWith(p, "pro/fileserver/save/web/djdocument/")) {
                    p = StrUtil.format("{}{}", httpPre, url);
                }
                processedList.add(p);
            }
            knowledge.setFiles(processedList);
        }
        for (Knowledge knowledge : read) {
            String result = StrUtil.format(outFormat, knowledge.getQuestion(), knowledge.getAnswer());
            List<String> files = knowledge.getFiles();
            String stringFile = "";
            if (CollUtil.isNotEmpty(files)) {
                stringFile = CollUtil.join(files, ",");
            }
            System.out.println(StrUtil.format("{}{}", result, stringFile));
        }
    }

    private static void addIfNotExist(Knowledge knowledge, String s1, List<String> fileList) {
        if (!StrUtil.containsAnyIgnoreCase(knowledge.getAnswer(), s1)) {
            fileList.add(s1);
        }
    }
}
