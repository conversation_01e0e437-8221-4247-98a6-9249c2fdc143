package com.djcps.ai.common.enums;

import com.djcps.ai.common.string.StringUtil;

/**
 * <AUTHOR>
 * @date 2022/8/15 15:10
 * @description 订单稽查-自动稽查标志
 */
public enum OrderCheckExcpAutoEnum {
    /**
     * 手动标记
     */
    MANUAL_OPERATION("manual_operation", 0),
    /**
     * 自动标记
     */
    AUTOMATIC_OPERATION("automatic_operation", 1),
    ;
    private String code;
    private Integer value;

    OrderCheckExcpAutoEnum(String code, Integer value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    /**
     * 根据code获取value
     *
     * @param code
     * @return
     */
    public static Integer getEnumValueByCode(String code) {
        if (StringUtil.isNotBlank(code)) {
            for (OrderCheckExcpAutoEnum type : OrderCheckExcpAutoEnum.values()) {
                if (code.equals(type.getCode())) {
                    return type.getValue();
                }
            }
        }
        return null;
    }

    /**
     * 根据value获取code
     *
     * @param value
     * @return
     */
    public static String getEnumCodeByValue(Integer value) {
        if (value != null) {
            for (OrderCheckExcpAutoEnum type : OrderCheckExcpAutoEnum.values()) {
                if (value.equals(type.getValue())) {
                    return type.getCode();
                }
            }
        }
        return null;
    }
}
