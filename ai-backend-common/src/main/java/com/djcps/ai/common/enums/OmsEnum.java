package com.djcps.ai.common.enums;


/**
 * <AUTHOR>
 * @date 2017/11/28
 */
public enum OmsEnum implements MsgIntface {
    /**
     * 操作成功
     */
    SUCCESS("操作成功", 1),
    ERROR("操作失败", -1),
    UPDATE_ERROR("修改操作失败", 11),
    DISTRIBUTE_ERROR("分发失败", 12),
    ORDER_REDIS_LOCK_FALSE("此订单正在被他人操作，请稍后再试", 13),
    ORDER_DISTRIBUTE_FALSE("此组订单中有订单正在分发", 14),
    ORDER_DISTRIBUTE_ERROR("订单分发失败，原因是订单状态不是已付款", 15),
    CANCEL_ERROR("取消订单失败", 16),
    EXPORT_ERROR("导出订单失败", 17),

    INSERT_OPERATION_ERROR("插入操作记录失败", 18),
    ORDER_FALSE_PARAM("缺少参数", 19),
    RELATE_ERROR("关联生产订单号失败", 20),
    LOAD_SPLIT_ORDER_ERROR("加载拆单失败", 21),
    LOAD_USER_DETAIL_EROR("加载客户信息失败", 22),
    UPDATE_SPLIT_ORDER_ERROR("批量修改拆单信息失败", 23),
    ORDER_SPLIT_ERROR("拆分订单失败", 24),
    ORDER_SPLITINFO_ERROR("修改拆单数据失败", 25),
    ORDER_INFO_ERROR("批量修改子订单信息失败", 26),
    ORDER_FIND_BY_PAGE("查询失败", 110),
    ORDER_TIME_EXPIRES("只允许查询三个月以内的订单", 111),
    ORDER_WEEK_EXPIRES("只能查询一周内订单", 112),
    NO_DATA("没有数据", 113),
    FROM_THAN_TO_ERROR("开始时间大于结束时间", 114),
    THREE_MONTH_AGO_ERROR("查询时间必须为三个月以内", 115),
    DELIVERY_INCOMPLETE_ERROR("请填写完整的交期开始和结束时间", 116),
    GETTING_NUMBERED_FAILURE("获取编号失败", 117),
    URGENCY_ORDER_NULL_ERROR("急单字段为空", 118),
    CLOSE_REASON_NULL_ERROR("关闭理由为空", 119),
    CONFIG_INFO_ERROR("未找到订单编号或生产编号配置列", 120),
    CONFIG_INFO_EXISTS_OR_NOT_ERROR("请确保已经配置订单编号和生产订单编号", 121),
    EVERY_TIME_RELATION_FIVE_HUNDRED_ERROR("每次最多只能关联500条订单", 122),
    NOT_MATCHED_ERROR("请确保未更改最上方标题栏字段信息并且导入各列信息与导出配置信息互相匹配", 123),
    ORDER_TIME_AFTER_DELIVERY_ERROR("请确保交期时间比下单时间晚!", 124),
    MUST_BE_HAVE_PRODUCTIONNO_ERROR("分发的订单中生产单号不能为空!", 125),
    MUST_BE_NO_ABNORMALITY_ERROR("分发的订单中不能包含异常订单!", 126),
    ORDER_FALSE_PARAM_EMPTY("生产单号不能为空!", 127),
    REPEAT_ERROR("生产订单编号重复,关联失败", 128),
    FROM_THAN_TO_ERRORS("开始时间必须小于结束时间", 129),
    UPDATE_ORDER_ADDRESS_ERROR_ERROR("修改地址失败", 130),
    ORDER_ADDRESS_FAILURE("该地址已失效，请重新选择", 678),
    URGENCY_ORDER_CHECK_ERROR("有订单不符合设为急单的条件，请重新选择订单", 132),
    URGENCY_ORDER_ERROE("该订单已被取消急单或不符合取消急单的条件", 133),
    DELIVERY_CHECK_ERRORS("交期必须大于今天", 134),
    DELIVERY_CHECK_ERRORS_STATUS("有订单不符合修改交期的条件，请重新选择订单", 135),
    DELIVERY_CHECK_ERRORS_TIME_STATUS("时间格式有误", 135),
    DELIVERY_CHECK_ERRORS_TOOLATER("交期时间请勿大于明年12月31日", 136),
    REPEAT_ERRORS_STATUS("订单状态为待导入后（含待导入）的才允许关联生产订单编号！", 137),
    REPEAT_ORDER_ERROR("该订单已被取消重单", 200),
    ;
    private String msg;
    private Integer code;

    OmsEnum(String msg, Integer code) {
        this.msg = msg;
        this.code = code;
    }

    @Override
    public String getMsg() {
        return msg;
    }

    @Override
    public MsgIntface setMsg(String msg) {
        this.msg = msg;
        return null;
    }

    public static OmsEnum getEnumByMsg(String msg) {
        OmsEnum omsEnum = null;
        for (OmsEnum oms : OmsEnum.values()) {
            if (oms.getMsg().equals(msg)) {
                omsEnum = oms;
            }
        }
        return omsEnum;
    }

    @Override
    public int getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
}
