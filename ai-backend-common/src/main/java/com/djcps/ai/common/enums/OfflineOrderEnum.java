package com.djcps.ai.common.enums;

/**
 * <AUTHOR> rsw
 * @date 2022/6/8 19:34.
 */
public enum OfflineOrderEnum implements MsgInterface {
    /**
     * 参数异常
     */
    ERROR("-1","操作失败"),
    SUCCESS("1","操作成功"),
    ORDER_PARAM_ERROR("-1000", "参数不全"),
    OPERATE_SUCCESS("666666", "操作成功"),
    OPERATE_FAIL("-1000", "操作失败"),
    ADDRESS_NULL_ERROR( "204","收货信息被删除，请重新选择！"),
    ADDRESS_EDIT_ERROR( "204", "收货信息被修改，请重新选择！"),
    OFFLINE_ORDER_IS_NOT_EXIST( "-1000", "订单信息不存在"),
    ORDER_CHANGE_FLAG_IS_NOT_EMPTY("133", "订单修改标记不能为空！"),
    REPEAT_ORDER_ERROR( "200", "该订单已被取消重单或订单已关闭"),
    ORDER_ID_IS_NOT_EMPTY("132", "订单编号不能为空！"),
    PERMIT_SUCCESS("02","审核成功!"),
    PRODUCT_IS_CHANGED("01","产品信息发生变化!"),

    ;
    private String code;
    private String desc;

    OfflineOrderEnum(String code, String msg) {
        this.code = code;
        this.desc = msg;
    }

    /**
     * 响应码
     *
     * @return
     */
    @Override
    public String getCode() {
        return this.code;
    }

    /**
     * 响应消息
     *
     * @return
     */
    @Override
    public String getDesc() {
        return this.desc;
    }
}
