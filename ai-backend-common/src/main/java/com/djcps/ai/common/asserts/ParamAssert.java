/*
 * Copyright 2018 huna.com All right reserved. This software is the confidential and proprietary information of huna.com
 * ("Confidential Information"). You shall not disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into with huna.com.
 */
package com.djcps.ai.common.asserts;

import com.djcps.ai.common.enums.SystemCodeEnum;
import com.djcps.ai.common.exception.ParamException;
import com.djcps.ai.common.string.StringUtil;
import org.springframework.util.Assert;

/**
 * 类BizAssert.java的实现描述：判断是否为空等校验，配合异常类
 * 
 * <AUTHOR> 2018年3月22日 下午1:28:33
 */
public class ParamAssert extends Assert {

	/**
	 * 如果为空抛异常
	 * 
	 * @param obj
	 * @param message
	 */
	public static void isNull(Object obj, String message) {
		if (obj == null || (obj instanceof String && StringUtil.isBlank(obj + ""))) {
			throw new ParamException(message);
		}
	}

	/**
	 * 如果为空抛异常
	 * 
	 * @param obj
	 * @param message
	 */
	public static void isNull(Object obj, SystemCodeEnum codeEnum) {
		if (obj == null || (obj instanceof String && StringUtil.isBlank(obj + ""))) {
			throw new ParamException(codeEnum);
		}
	}

	/**
	 * 为真则抛出异常
	 * 
	 * @param bool
	 * @param message
	 */
	public static void isTrue(boolean bool, String message) {
		if (bool) {
			throw new ParamException(message);
		}
	}

	/**
	 * 为真则抛出异常
	 * 
	 * @param bool
	 * @param message
	 */
	public static void isTrue(boolean bool, SystemCodeEnum codeEnum) {
		if (bool) {
			throw new ParamException(codeEnum);
		}
	}
}
