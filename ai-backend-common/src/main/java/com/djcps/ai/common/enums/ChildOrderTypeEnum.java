package com.djcps.ai.common.enums;

import com.djcps.ai.common.string.StringUtil;

/**
 * <AUTHOR> rsw
 * @date 2022/8/24 20:12.
 * 子订单类型枚举
 */
public enum ChildOrderTypeEnum {
    /**
     * 订单类型-正常
     */
    NORMAL_ORDER("normal_order",1),
    /**
     * 订单类型-小单
     */
    SMALL_ORDER("small_order",2),
    /**
     * 订单类型-大单
     */
    BIG_ORDER("big_order",3);
    private String code;
    private Integer value;

    ChildOrderTypeEnum(String code, Integer value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    /**
     * 根据code获取value
     *
     * @param code
     * @return
     */
    public static Integer getEnumValueByCode(String code) {
        if (StringUtil.isNotBlank(code)) {
            for (ChildOrderTypeEnum type : ChildOrderTypeEnum.values()) {
                if (code.equals(type.getCode())) {
                    return type.getValue();
                }
            }
        }
        return null;
    }

    /**
     * 根据value获取code
     *
     * @param value
     * @return
     */
    public static String getEnumCodeByValue(Integer value) {
        if (value != null) {
            for (ChildOrderTypeEnum type : ChildOrderTypeEnum.values()) {
                if (value.equals(type.getValue())) {
                    return type.getCode();
                }
            }
        }
        return null;
    }
}
