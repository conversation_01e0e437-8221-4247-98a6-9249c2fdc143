package com.djcps.ai.common.enums;

/**
 * 类DifyCodeEnum的实现描述：dify 异常
 *
 * <AUTHOR> 2024/7/12 下午3:26
 */
public enum DifyCodeEnum implements MsgInterface {

    /**---------------------发送对话消息,创建会话消息---------------**/
    对话不存在(404,null,"对话不存在"),
    传入参数异常(400,"invalid_param","传入参数异常"),
    App配置不可用(400,"app_unavailable","App 配置不可用"),
    无可用模型凭据配置(400,"provider_not_initialize","无可用模型凭据配置"),
    模型调用额度不足(400,"provider_quota_exceeded","模型调用额度不足"),
    当前模型不可用(400,"model_currently_not_support","当前模型不可用"),
    文本生成失败(400,"completion_request_error","文本生成失败"),
    服务内部异常(500,null,"服务内部异常"),

    /**---------------------停止响应,仅支持流式模式---------------**/
    //无
    /**---------------------消息反馈（点赞）---------------**/


    ;
    private Integer code;
    private String message;
    private String desc;

    DifyCodeEnum(Integer code, String message,String desc) {
        this.code = code;
        this.message = message;
        this.desc = desc;
    }

    public String getCode() {
        return "" + this.code;
    }

    /**
     * 响应消息
     *
     * @return
     */
    @Override
    public String getDesc() {
        return this.desc;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
}
