package com.djcps.ai.common.enums;

/**
 * <AUTHOR> rsw
 * @date 2022/6/8 19:34.
 */
public enum ChangePriceEnum implements MsgInterface {
    /**
     * 参数异常
     */
    PARAM_ERROR("100013001", "参数异常"),
    /**
     * 操作失败
     */
    FAIL("100013002", "操作失败"),
    /**
     * 查询的订单不存在
     */
    LIST_ISNULL("100013002", "查询的订单不存在"),
    /**
     * 不是同一笔母订单，不能发起批量改价
     */
    ORDERID_ISNOT_ONE("100013003", "不是同一笔母订单，不可修改价格"),
    /**
     * 订单被取消，不可修改价格
     */
    ORDERID_DELETE("100013004", "订单被取消，不可修改价格"),
    /**
     * 订单状态发生变更，不可修改价格
     */
    ORDERID_STATUS("100013005", "订单状态发生变更，不可修改价格"),
    /**
     * 计算金额异常
     */
    PRICE_ERROR("100013006", "计算金额异常"),
    /**
     * 修改后的单价不能小于0，也不能大于100
     */
    PRICE_OVERRUN("100013007", "修改后的单价不能小于0，也不能大于100"),
    /**
     * 价格变动超出配置范围
     */
    CHANGE_OVERRUN("100013008", "价格修改超出权限范围，请修改"),
    /**
     * 优惠券被篡改，非法操作
     */
    COUPON_CHANGE("100013009", "优惠券或供应商被篡改，非法操作"),
    /**
     * 优惠券不存在
     */
    COUPON_SERVER_ERROR("100013010", "优惠券不存在"),
    /**
     * 存在订单改价后已不满足优惠券使用条件，请修改金额/联系客户不要使用优惠券重新下单
     */
    COUPON_CHECK_PRICE("100013011", "存在订单改价后已不满足优惠券使用条件，请修改金额/联系客户不要使用优惠券重新下单"),
    /**
     * 改价后订单应付金额不得小于0
     */
    AMOUNT_PRICE("100013012", "改价后订单应付金额不得小于0"),
    /**
     * 补价单不可发起改价
     */
    PRODUCT_TYPE("100013013", "补价单不可发起改价"),

    /**
     * 该笔补价订单已提交
     */
    REPLENISH_ORDER_IS_SUBMIT("100013014", "该笔补价订单已提交"),

    /**
     * 补价单号不可为空
     */
    REPLENISH_ORDER_IS_NUll("100013015", "补价单号不可为空!"),
    /**
     * 同一笔母订单正在修改，请稍后再操作
     */
    CHANGE_PRICE_REDIS("100013016", "同一笔母订单正在修改，请稍后再操作"),
    /**
     * 数据已变更,请刷新数据重新操作
     */
    HAVEN_UPDATE("100013017", "数据已变更,请刷新数据重新操作"),
    ;
    private String code;
    private String desc;

    ChangePriceEnum(String code, String msg) {
        this.code = code;
        this.desc = msg;
    }

    /**
     * 响应码
     *
     * @return
     */
    @Override
    public String getCode() {
        return this.code;
    }

    /**
     * 响应消息
     *
     * @return
     */
    @Override
    public String getDesc() {
        return this.desc;
    }
}
