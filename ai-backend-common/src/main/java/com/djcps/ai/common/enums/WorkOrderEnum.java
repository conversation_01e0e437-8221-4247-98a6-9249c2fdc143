package com.djcps.ai.common.enums;

/**
 * @author: bdd
 * @Date: Created in 14:13 2021/8/25
 * @Descripion:
 */
public enum WorkOrderEnum implements MsgInterface {
    /**
     * 参数错误
     */
    PARAM_ERROR("000303001", "参数错误"),
    /**
     * 配置项数量不能超过5
     */
    EXPORT_CONFIG_FIVE("000303002", "配置项数量不能超过5"),
    /**
     * 名称重复
     */
    CONFIG_NAME_REPEAT("000303003", "名称重复"),
    /**
     * 暂无数据
     */
    EXPORT_NO_DATA("000303004", "暂无数据"),
    /**
     * 取消重单后，才可更换地址
     */
    UPDATE_ADDRESS_ERROR("000303005", "取消重单后，才可更换地址"),
    /**
     * 无法更换地址
     */
    ORDER_STATUS_ERROR("000303006", "订单{0}，无法更换地址"),
    /**
     * 该地址为待升级地址，请重新选择
     */
    ORDER_ADDRESS_ERROR("000303007", "该地址为待升级地址，请重新选择"),
    /**
     * 该地址超出配送范围，请重新选择
     */
    ORDER_ADDRESS_OVER("000303008", "该地址超出配送范围，请重新选择"),
    /**
     * 订单不存在
     */
    ORDER_NOT_EXIST("000303009", "订单不存在"),
    /**
     * 此订单当前状态不可备注
     */
    ORDER_CANNOT_REMARK("000303010", "此订单当前状态不可备注"),
    /**
     * 订单导出配置不存在
     */
    ORDER_EXPORT_NOT_EXIST("000303011", "订单导出配置不存在"),
    /**
     * 该条件下订单数量超过10000条,请缩小条件范围
     */
    Order_OUT_OF_RANGE("000303012", "该条件下订单数量超过10000条,请缩小条件范围"),
    /**
     * 订单导出访问人数过多,请稍后再试
     */
    ORDER_EXPORT_OFTEN("000303013", "订单导出访问人数过多,请稍后再试"),;

    private String code;
    private String msg;

    WorkOrderEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getDesc() {
        return this.msg;
    }

    public WorkOrderEnum setCode(String code) {
        this.code = code;
        return this;
    }

    public WorkOrderEnum setMsg(String msg) {
        this.msg = msg;
        return this;
    }
}
