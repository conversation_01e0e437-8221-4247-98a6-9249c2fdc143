package com.djcps.ai.common.enums;

/**
 * @author:wangxiu
 * @date:2022/8/25
 */
public enum OfflineStatusEnum {

    /**
     * 已分发
     */
    DISTRIBUTED(3, "已分发"),
    /**
     * 已出库
     */
    IN_BOUND(4, "已入库"),
    /**
     * 配送中
     */
    OUT_BOUND(5, "配送中"),
    /**
     * 已完成
     */
    SIGNED(6, "已完成"),
    /**
     * 已取消
     */
    CANCELED(7, "已取消"),
    /**
     * 部分入库
     */
    PART_STORED(21, "部分入库"),
    /**
     * 已入库
     */
    STORED(22, "已入库"),
    /**
     * 已配货
     */
    HAVE_BEEN_PICKING(23, "已配货"),
    /**
     * 已提货
     */
    PICKED_UP(24, "已提货"),
    /**
     * 已装车
     */
    LOADED(25, "已装车"),
    /**
     * 其他状态，不在订单状态之内
     */
    DEFAULT(10, "其他状态，不在订单状态之内"),
    /**
     * 已送达
     */
    HAVE_BEEN_DELIVERED(32, "已送达");

    private int code;
    private String name;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    OfflineStatusEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getStatusNameByCode(int code) {
        for (OfflineStatusEnum data : OfflineStatusEnum.values()) {
            if (data.getCode() == code) {
                return data.getName();
            }
        }
        return null;
    }
}
