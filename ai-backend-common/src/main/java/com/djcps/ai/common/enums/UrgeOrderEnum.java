package com.djcps.ai.common.enums;

/**
 * <AUTHOR>
 * @date ：Created in 2022/1/14 17:13
 * @description：
 * @modified By：
 * @version: $
 */
public enum UrgeOrderEnum implements MsgInterface {

    /**
     * 操作成功
     */
    OPERATE_SUCCESS("000304001", "催单消息已发送,请耐心等待供应商处理"),

    /**
     * 已达3次催单上限
     */
    REMINDER_HAS_CAPPED("000304002","已达3次催单上限~如有疑问请联系客服"),

    /**
     * 今天已催单,请耐心等待
     */
    REMINDER_HAS_TODAY("000304003","今天已催单,请耐心等待"),

    /**
     * 无法催单
     */
    UNABLE_TO_REMINDER("000304004","当前订单状态,无法催单"),

    /**
     * 操作失败
     */
    OPERATE_FAIL("000304005", "催单失败!,请联系客服"),
    /**
     * 订单正在处理中,请稍后再试
     */
    ORDER_CANCEL_FAIL("000304006", "订单正在处理中,请稍后再试"),;


    private String code;
    private String desc;

    UrgeOrderEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public UrgeOrderEnum setMsg(String desc) {
        this.desc = desc;
        return this;
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }
}
