/*
 * Copyright 2022 51zjb.com All right reserved. This software is the
 * confidential and proprietary information of 51zjb.com ("Confidential
 * Information"). You shall not disclose such Confidential Information and shall
 * use it only in accordance with the terms of the license agreement you entered
 * into with 51zjb.com.
 */
package com.djcps.ai.common.vo;

import lombok.Data;

/**
 * 类PageBaseInVo.java的实现描述：入参分页
 * 
 * <AUTHOR> 2022年7月7日 下午3:27:58
 */
@Data
public class PageBaseInVo {

	// 页码
	private int pageNo = 0;

	// 一页行数
	private int pageSize = 10;

	public int getPageNo() {
		if (pageNo <= 0) {
			return 1;
		} else if (pageNo > 100) {
			return 100;
		}
		return pageNo;
	}

	public void setPageNo(int pageNo) {
		this.pageNo = pageNo;
	}

}
