package com.djcps.ai.common.enums;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/9/5 19:37
 */
public enum OrderNodeToStatus {
    /**
     * 待付款
     */
    WAIT_PAY_STATUS("wait_pay", "wait_pay"),
    /**
     * 已付款
     */
    ALREADY_PAY_STATUS("already_pay", "already_pay"),
    /**
     * 已分发
     */
    ALREADY_DISTRIBUTE_STATUS("already_distribute", "in_production"),
    /**
     * 已入库
     */
    ALREADY_STORAGE_STATUS("already_storage", "in_production"),
    /**
     * 运输中
     */
    IN_TRANSIT_STATUS("in_transit", "in_transit"),
    /**
     * 已签收
     */
    ALREADY_SIGN_STATUS("already_sign", "already_sign"),
    /**
     * 生产中
     */
    IN_PRODUCTION_STATUS("in_production", "in_production"),
    /**
     * 部分入
     */
    PART_ALREADY_STORAGE_STATUS("part_already_storage", "in_production"),
    /**
     * 已配货
     */
    ALREADY_DISTRIBUTION_STATUS("already_distribution", "in_production"),
    /**
     * 已提货
     */
    ALREADY_PICK_STATUS("already_pick", "in_production"),
    /**
     * 已装车
     */
    ALREADY_TRUCK_STATUS("already_truck", "in_production"),
    /**
     * 已送达
     */
    ALREADY_DELIVERY_STATUS("already_delivery", "in_transit"),;

    private String code;
    private String desc;

    private OrderNodeToStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static String getEnumValueByCode(String code) {
        if (code != null) {
            for (OrderNodeToStatus type : OrderNodeToStatus.values()) {
                if (code.equals(type.getCode())) {
                    return type.getDesc();
                }
            }
        }
        return null;
    }

    public static String getEnumCodeByValue(String desc) {
        if (desc != null) {
            for (OrderNodeToStatus type : OrderNodeToStatus.values()) {
                if (desc.equals(type.getDesc())) {
                    return type.getCode();
                }
            }
        }
        return null;
    }
}
