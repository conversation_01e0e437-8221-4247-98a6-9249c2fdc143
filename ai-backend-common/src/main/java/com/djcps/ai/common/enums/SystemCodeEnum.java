package com.djcps.ai.common.enums;

/**
 * 类SystemCodeEnum的实现描述：TODO 类实现描述
 *
 * <AUTHOR> 2021/9/13 13:08
 */
public enum SystemCodeEnum implements MsgInterface {



	// ----------------------通用异常-----------------------/
	// FIXME ???????为什么code都一样
	MSG_TEMPLATE1("100044", "操作失败"),

	OPERATE_SUCCESS("100144", "操作成功"),

	SUCCESS("100146", "创建成功"),

	FAILURE("100147", "创建失败"),

	LACK_PARAM("100145", "参数缺失或为空"),

	OPERATOR_ERROR("-10001207", "操作失败"),

	DONT_OPERATE("100075", "不要重复操作"),

	system_file_down_faild("100811", "文件下载失败"),

	DATABASE_FAILED("100080", "数据库异常"),

	DATA_UNFOUND("100081", "数据未找到"),

	PERSON_LIMITED("100152", "您无该操作权限"),

	swap_exception("100152", "数据转换异常"),

	system_duplicate_failed("100033", "您太热情了，请稍后再试."),

	system_third_failed("100055", "第三方获取异常."),
	/**
	 * 服务连接失败
	 */
	SERVER_FAIL("100056", "服务连接失败"),

	// ----------------------业务异常-----------------------/

	/**
	 * 业务ID不能为空
	 */
	param_null("1000", "参数不能为空或者参数异常"),

	/**
	 * 未获取到登录手机号
	 */
	MSG_TEMPLATE2("100045", "未获取到登录手机号"),

	/**
	 * 登录失效
	 */
	MSG_TEMPLATEUS1("100641", "登录失效"),

	/**
	 * 缺少登录账户
	 */
	MSG_TEMPLATEUS2("100642", "缺少登录账户"),

	/**
	 * 请求组织失败
	 */
	MSG_TEMPLATEUS3("100643", "请求组织失败"),

	/**
	 * 获取组织信息失败
	 */
	MSG_TEMPLATEUS4("100644", "获取组织信息失败"),

	/**
	 * 未获取到组织信息
	 */
	MSG_TEMPLATEUS5("100645", "未获取到组织信息"),

	/**
	 * 获取组织信息异常
	 */
	MSG_TEMPLATEUS6("100646", "获取组织信息异常"),

	/**
	 * 用户信息缓存异常
	 */
	MSG_TEMPLATEUS7("100647", "用户信息缓存异常"),

	/**
	 * 未获取到用户登录信息
	 */
	MSG_TEMPLATEUS8("100648", "未获取到用户登录信息"),

	/**
	 * 设置用户登录缓存
	 */
	MSG_TEMPLATEUS9("100649", "设置用户缓存异常"),

	/**
	 * 同步用户缓存异常
	 */
	MSG_TEMPLATEUS10("100650", "同步用户缓存异常"),

	/**
	 * 组织信息缺失
	 */
	MSG_TEMPLATEUS11("100651", "组织信息缺失"),

	/**
	 * 查询分组异常
	 */
	MSG_TEMPLATEUS12("100652", "查询分组异常"),

	/**
	 * 工单数据为空
	 */
	MSG_TEMPLATEUS13("100653", "工单数据为空"),

	/**
	 * 请求组织用户失败
	 */
	MSG_TEMPLATEUS14("100654", "请求组织用户失败"),

	/**
	 * 访问账户信息失败
	 */
	MSG_TEMPLATEUS15("100655", "访问账户信息失败"),

	/**
	 * 未查询到分组
	 */
	MSG_TEMPLATEUS27("100667", "未查询到分组"),

	/**
	 * 运营指派人不能为空
	 */
	MSG_TEMPLATEUS28("100668", "运营指派人不能为空"),

	LABEL_ENABLE("100151", "配置项调整，请重新操作"),

	USER_IS_NULL("100048", "用户不存在,请重新操作"),

	;

	private String code;
	private String desc;

	private SystemCodeEnum(String code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	/**
	 * @return the code
	 */
	public String getCode() {
		return code;
	}

	/**
	 * @param code the code to set
	 */
	public void setCode(String code) {
		this.code = code;
	}

	/**
	 * @return the desc
	 */
	public String getDesc() {
		return desc;
	}

	/**
	 * @param desc the desc to set
	 */
	public void setDesc(String desc) {
		this.desc = desc;
	}

}
