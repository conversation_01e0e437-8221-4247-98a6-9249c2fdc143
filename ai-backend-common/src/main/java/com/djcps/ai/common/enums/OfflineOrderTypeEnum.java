package com.djcps.ai.common.enums;

/**
 * 线下订单类型枚举
 * @author:wangxiu
 * @date:2022/8/23
 */
public enum OfflineOrderTypeEnum {
    /**
     * 线下纸板订单
     */
    OFF_BOARD_ORDER(1, "线下纸板订单"),
    OFF_BOARD_SUP(2, "线下纸板补单"),
    ON_BOARD_SUP(3, "平台纸板补单"),
    OFF_BOX_ORDER(4, "线下纸箱订单"),
    OFF_BOX_SUP(5, "线下纸箱补单"),
    WORKING_ORDER(6, "加工单"),
    ERROR_TYPE(7, "订单类型错误");
    private String name;
    private Integer code;

    OfflineOrderTypeEnum(Integer code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public static OfflineOrderTypeEnum getOrderTypeEnumByCode(Integer code) {
        switch (code) {
            case 1:
                return OFF_BOARD_ORDER;
            case 2:
                return OFF_BOARD_SUP;
            case 3:
                return ON_BOARD_SUP;
            case 4:
                return OFF_BOX_ORDER;
            case 5:
                return OFF_BOX_SUP;
            case 6:
                return WORKING_ORDER;
            default:
                return ERROR_TYPE;
        }
    }

    public static Integer getOrderTypeEnumByCode(String type) {
        switch (type) {
            case "线下纸板订单":
                return OFF_BOARD_ORDER.getCode();
            case "线下纸板补单":
                return OFF_BOARD_SUP.getCode();
            case "平台纸板补单":
                return ON_BOARD_SUP.getCode();
            case "线下纸箱订单":
                return OFF_BOX_ORDER.getCode();
            case "加工单":
                return WORKING_ORDER.getCode();
            case "线下纸箱补单":
                return OFF_BOX_SUP.getCode();
            default:
                return ERROR_TYPE.getCode();
        }
    }
}
