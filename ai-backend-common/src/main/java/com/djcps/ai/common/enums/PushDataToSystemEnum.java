package com.djcps.ai.common.enums;

/**
 * <AUTHOR>
 * @date ：Created in 2022/9/5 16:06
 * @description：
 * @modified By：
 * @version: $
 */
public enum PushDataToSystemEnum implements MsgInterface{

    /**
     * 合作方查询为空
     */
    THE_PARTNER_QUERY_IS_EMPTY("100014001", "合作方查询为空"),

    /**
     * 合作方列表为空
     */
    THE_PARTNER_LIST_IS_EMPTY("100014002", "合作方列表为空"),

    /**
     * 不需要推送
     */
    DO_NOT_NEED_PUSH("100014003","查询数据为空,不需要推送"),

    /**
     * 推送ERP订单数据不成功
     */
    DATA_PUSH_FAILURE("100014004","推送ERP订单数据不成功")
    ;


    private String code;
    private String desc;

    PushDataToSystemEnum(String code, String msg) {
        this.code = code;
        this.desc = msg;
    }

    /**
     * 响应码
     *
     * @return
     */
    @Override
    public String getCode() {
        return this.code;
    }

    /**
     * 响应消息
     *
     * @return
     */
    @Override
    public String getDesc() {
        return this.desc;
    }
}
