/*
PageBaseInVo * Copyright 2018 me.com All right reserved. This software is the confidential and proprietary information of me.com
 * ("Confidential Information"). You shall not disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into with me.com.
 */
package com.djcps.ai.common.exception;

import com.djcps.ai.common.enums.SystemCodeEnum;

/**
 * 
 * 类CoreExcetion.java的实现描述：三方异常类
 * 
 * <AUTHOR> 2022年7月13日 下午3:13:47
 */
public class CoreExcetion extends RuntimeException {

	private static final long serialVersionUID = 5964759405889919141L;
	/**
	 * 异常编码
	 */
	private String code = "-1";
	/**
	 * 提示信息
	 */
	private String message;

	public CoreExcetion() {
	}

	public CoreExcetion(String code, String message) {
		super(message);
		this.code = code;
		this.message = message;
	}

	public CoreExcetion(SystemCodeEnum codeEnum) {
		super(codeEnum.getDesc());
		this.code = codeEnum.getCode();
		this.message = codeEnum.getDesc();
	}

	public CoreExcetion(String message) {
		super(message);
		this.message = message;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	/**
	 * @return the message
	 */
	@Override
	public String getMessage() {
		return message;
	}

	/**
	 * @param message the message to set
	 */
	public void setMessage(String message) {
		this.message = message;
	}
}
