package com.djcps.ai.common.enums;

import com.djcps.ai.common.string.StringUtil;

/**
 * <AUTHOR>
 * @date 2022/8/22 15:18
 * @description 订单稽查-疑似异常类型
 */
public enum OrderCheckExcpTypeEnum {
    /**
     * 疑似异常类型
     * 0余额支付异常(balance)
     * 1银行卡支付异常(bank)
     * 2微信支付异常(wechat)
     * 3支付宝支付异常(alipay)
     * 4第三方支付异常(third)
     * 5混合支付异(mixture)
     * 6壹钱包支付异常(wallet)
     * 7东经易付通-台州银行支付异常(taizhou_bank)
     * 8东经易付通-厦门国际支付异常(xiamen_bank)
     * 9积分抵用扣除异常(integral)
     * 10招商支付异常(zhaoshang_ban)
     * 11兴业支付异常(xingye_ban)
     * 12民生银行支付异常(minsheng_ban)
     * 13承兑支付异常(accept)
     */
    BALANCE("balance", 0),
    BANK("bank", 1),
    wechat("wechat", 2),
    ALIPAY("alipay", 3),
    THIRD("third", 4),
    MIXTURE("mixture", 5),
    WALLET("wallet",6),
    TAIZHOU_BANK("taizhou_bank",7),
    XIAMEN_BANK("xiamen_bank",8),
    INTEGRAL("integral",9),
    ZHAOSHANG_BAN("zhaoshang_ban",10),
    XINGYE_BAN("xingye_ban",11),
    MINSHENG_BAN("minsheng_ban",12),
    ACCEPT("accept",13),
    ;
    private String code;
    private Integer value;

    OrderCheckExcpTypeEnum(String code, Integer value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    /**
     * 根据code获取value
     *
     * @param code
     * @return
     */
    public static Integer getEnumValueByCode(String code) {
        if (StringUtil.isNotBlank(code)) {
            for (OrderCheckExcpTypeEnum type : OrderCheckExcpTypeEnum.values()) {
                if (code.equals(type.getCode())) {
                    return type.getValue();
                }
            }
        }
        return null;
    }

    /**
     * 根据value获取code
     *
     * @param value
     * @return
     */
    public static String getEnumCodeByValue(Integer value) {
        if (value != null) {
            for (OrderCheckExcpTypeEnum type : OrderCheckExcpTypeEnum.values()) {
                if (value.equals(type.getValue())) {
                    return type.getCode();
                }
            }
        }
        return null;
    }
}
