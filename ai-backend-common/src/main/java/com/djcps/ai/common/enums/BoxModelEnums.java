package com.djcps.ai.common.enums;

/**
 * <AUTHOR>
 * @data 2019/12/27 15:49
 * @description
 */
public enum BoxModelEnums {
    /**
     * 0: "其他箱型",
     * 1: "普通箱型",
     * 2: "全包箱型",
     * 3: "半包箱型",
     * 4: "有底无盖箱型",
     * 5: "有盖无底箱型",
     * 6: "天地盖箱型"
     */
    OTHER("0", "其他"),
    COMMON("1", "普通"),
    ALL_INCLUSIVE("2", "全包"),
    HALF_INCLUSIVE("3", "半包"),
    WITHOUT_COVER("4", "有底无盖"),
    WITHOUT_BOTTOM("5", "有盖无底"),
    WITH_COVER_AND_BOTTOM("6", "天地盖"),
    ;

    private String code;

    private String name;

    BoxModelEnums(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    /**
     * 根据值获取对应名称
     *
     * @param code
     * @return
     */
    public static String nameOf(String code) {
        for (BoxModelEnums g : BoxModelEnums.values()) {
            if (g.getCode().equals(code)) {
                return g.getName();
            }
        }
        return code;
    }
}
