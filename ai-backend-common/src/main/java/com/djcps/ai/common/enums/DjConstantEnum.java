package com.djcps.ai.common.enums;

/**
 * @author:wangxiu
 * @date:2022/8/30
 */
public enum DjConstantEnum {

    /**
     * 接口常用枚举
     */
    SUCCESS(1, "success"),
    DATA(2, "data"),
    NUMONE(3, "1"),
    NUMTWO(4, "2"),
    DATE_TIME_FORMAT(5, "yyyy-MM-dd HH:mm:ss"),
    THIRTY_THOUSAND(30000, "30000"),
    ;

    private Integer code;
    private String name;

    DjConstantEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
