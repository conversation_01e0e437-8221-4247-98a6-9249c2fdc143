package com.djcps.ai.common.enums;

/**
 * @author:wangxiu
 * @date:2022/8/24
 */
public enum OmsOrderStatusEnum {

    /**
     * 未付款
     */
    NOT_PAY(1, "未付款"),
    /**
     * 已付款
     */
    PAID(2, "已付款"),
    /**
     * 待导入
     */
    DISTRIBUTED(3, "待导入"),
    /**
     * 已出库
     */
    IN_BOUND(4, "已入库"),
    /**
     * 运输中
     */
    OUT_BOUND(5, "运输中"),
    /**
     * 已完成
     */
    SIGNED(6, "已完成"),
    /**
     * 已取消
     */
    CANCELED(7, "已取消"),
    /**
     * 搜索过滤未付款订单专用
     */
    SEARCH_ORDER(8, "搜索过滤未付款订单专用"),
    /**
     * 生产中
     */
    IN_PRODUCTION(9, "生产中"),
    /**
     * 部分入库
     */
    PART_STORED(21, "部分入库"),
    /**
     * 已入库
     */
    STORED(22, "已入库"),
    /**
     * 已配货
     */
    HAVE_BEEN_PICKING(23, "已配货"),
    /**
     * 已提货
     */
    PICKED_UP(24, "已提货"),
    /**
     * 完成装车
     */
    LOADED(25, "完成装车"),
    /**
     * 其他状态，不在订单状态之内
     */
    DEFAULT(10, "其他状态，不在订单状态之内"),
    /**
     * 已送达
     */
    HAVE_BEEN_DELIVERED(32, "已送达");
    private String msg;
    private Integer code;

    OmsOrderStatusEnum(Integer code, String msg) {
        this.msg = msg;
        this.code = code;
    }

    public static OmsOrderStatusEnum getEmumByCode(Integer code) {
        for (OmsOrderStatusEnum e : OmsOrderStatusEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return DEFAULT;
    }

    public static OmsOrderStatusEnum getEnumByMsg(String msg) {
        for (OmsOrderStatusEnum e : OmsOrderStatusEnum.values()) {
            if (e.getMsg().equals(msg)) {
                return e;
            }
        }
        return DEFAULT;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
}
