package com.djcps.ai.common.enums;

import com.djcps.ai.common.string.StringUtil;

/**
 * <AUTHOR>
 * @date 2022/8/15 15:28
 * @description 订单产品类型枚举
 */
public enum OrderProductTypeEnum {
    /**
     * 正常产品订单
     */
    NORMAL_ORDER("normal_order", 1),
    /**
     * 补价产品订单
     */
    REPLENISH_ORDER("replenish_order", 2),
    ;
    private String code;
    private Integer value;

    OrderProductTypeEnum(String code, Integer value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    /**
     * 根据code获取value
     *
     * @param code
     * @return
     */
    public static Integer getEnumValueByCode(String code) {
        if (StringUtil.isNotBlank(code)) {
            for (OrderProductTypeEnum type : OrderProductTypeEnum.values()) {
                if (code.equals(type.getCode())) {
                    return type.getValue();
                }
            }
        }
        return null;
    }
    /**
     * 根据value获取code
     *
     * @param value
     * @return
     */
    public static String getEnumCodeByValue(Integer value) {
        if (value != null) {
            for (OrderProductTypeEnum type : OrderProductTypeEnum.values()) {
                if (value.equals(type.getValue())) {
                    return type.getCode();
                }
            }
        }
        return null;
    }
}
