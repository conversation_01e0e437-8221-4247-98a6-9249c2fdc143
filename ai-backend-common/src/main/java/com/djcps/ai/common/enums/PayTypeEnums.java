package com.djcps.ai.common.enums;

/**
 * @program: djgroupon
 * @description: 第三方服务商
 * @author: qch
 * @create: 2018-06-12 16:05
 */
public enum PayTypeEnums {
    /**
     * （0:现金余额,1:微信,2:支付宝,3:银行卡(都以招行结算),4:壹钱包,
     *  5:东经易付通-台州银行，6:东经易付通-厦门国际 7:承兑余额 10:微信+余额,20:支付宝+余额,
     *  30:招行+余额，40：壹钱包+余额）
     */

    /**
     * 余额
     */
    GROUPONPAY_BALANCE_PAYMENT(0, "现金余额"),
    /**
     * 微信
     */
    GROUPONPAY_WECHAT_PAYMENT(1, "微信"),
    /**
     * 支付宝
     */
    GROUPONPAY_ALIPAY_PAYMENT(2, "支付宝"),
    /**
     * 银行卡
     */
    GROUPONPAY_BANK_CARD_PAYMENT(3, "银行卡"),
    /**
     * 壹钱包
     */
    GROUPONPAY_YQB_PAYMENT(4, "壹钱包"),
    /**
     * 东经易付通-台州银行
     */
    GROUPONPAY_QYX_PAYMENT(5, "东经易付通（台州银行）"),

    /**
     * 东经易付通-厦门国际
     */
    GROUPONPAY_QYX_XM_PAYMENT(6, "东经易付通（厦门国际）"),

    /**
     * 承兑余额
     */
    ACCEPTANCE(7, "承兑余额"),

    /**
     * 微信+余额
     */
    GROUPONPAY_WECHAT_BALANCE(10, "微信+余额"),
    /**
     * 支付宝+余额
     */
    GROUPONPAY_ALIPAY_BALANCE(20, "支付宝+余额"),
    /**
     * 招行+余额
     */
    GROUPONPAY_BANK_BALANCE(30, "招行+余额"),
    /**
     * 壹钱包+余额
     */
    GROUPONPAY_QYX_BALANCE(40, "壹钱包+余额"),

    ;

    private int code;

    private String name;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    PayTypeEnums(int code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据值获取对应名称
     *
     * @param code
     * @return
     */
    public static String nameOf(int code) {
        for (PayTypeEnums g : PayTypeEnums.values()) {
            if (g.getCode() == code) {
                return g.getName();
            }
        }
        return String.valueOf(code);
    }
}

