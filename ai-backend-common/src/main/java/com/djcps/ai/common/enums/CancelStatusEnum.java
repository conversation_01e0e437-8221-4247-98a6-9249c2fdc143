package com.djcps.ai.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/8/17 9:58
 */
public enum CancelStatusEnum {
	/**
	 * 未取消
	 */
	NOT_CANCEL_STATUS("not_cancel"),
	/**
	 * 客户取消
	 */
	CUSTOMER_CANCEL_STATUS("customer_cancel"),
	/**
	 * 定时器取消
	 */
	TIMER_CANCEL_STATUS("timer_cancel"),
	/**
	 * 运营取消
	 */
	OPERATION_CANCEL_STATUS("operation_cancel"),;

	private String code;

	/**
	 * 验证是否在某个节点
	 * 
	 * @param code
	 * @param enums
	 * @return
	 */
	public static boolean validateHave(String code, CancelStatusEnum... enums) {
		if (StringUtils.isNotBlank(code) && enums != null) {
			for (CancelStatusEnum type : enums) {
				if (code.equals(type.getCode())) {
					return true;
				}
			}
		}
		return false;
	}

	CancelStatusEnum(String code) {
		this.code = code;

	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

}
