package com.djcps.ai.common.enums;

import com.djcps.ai.common.string.StringUtil;

/**
 * <AUTHOR>
 * @date 2022/8/15 15:34
 * @description 订单改价标识枚举
 */
public enum OrderPriceChangeEnum {
    /**
     * 正常单
     */
    NORMAL("normal", 1),
    /**
     * 改价单(一口价)
     */
    CHANGE_FIXED_PRICE("change_fixed_price", 2),
    /**
     * 改价单(正常价)
     */
    CHANGGE_NORMAL("changge_normal", 3),
    ;
    private String code;
    private Integer value;

    OrderPriceChangeEnum(String code, Integer value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }


    /**
     * 根据code获取value
     *
     * @param code
     * @return
     */
    public static Integer getEnumValueByCode(String code) {
        if (StringUtil.isNotBlank(code)) {
            for (OrderPriceChangeEnum type : OrderPriceChangeEnum.values()) {
                if (code.equals(type.getCode())) {
                    return type.getValue();
                }
            }
        }
        return null;
    }


    /**
     * 根据value获取code
     *
     * @param value
     * @return
     */
    public static String getEnumCodeByValue(Integer value) {
        if (value != null) {
            for (OrderPriceChangeEnum type : OrderPriceChangeEnum.values()) {
                if (value.equals(type.getValue())) {
                    return type.getCode();
                }
            }
        }
        return null;
    }
}
