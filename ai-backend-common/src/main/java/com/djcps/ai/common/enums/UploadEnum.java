package com.djcps.ai.common.enums;

/**
 * @author:wangxiu
 * @date:2022/8/24
 */
public enum UploadEnum implements MsgIntface {

    /**
     * 文件为空判断
     */
    FILE_NULL(300001, "文件为空!"),
    /**
     * 文件类型校验判断
     */
    FILE_TYPE(300002, "文件类型不正确!"),
    /**
     * 文件sheet数量判断
     */
    SHEET_NULL(300003, "文件sheet为空!"),
    /**
     * 文件异常
     */
    READ_FILE_ERROR(300004, "文件解析异常"),
    /**
     * 当前文件无数据
     */
    DATA_NULL(300005, "当前文件无数据!"),
    /**
     * 请勿导入超出3000行的文件
     */
    OVER_FIVE_HUNDRED(300006, "单次导入最多允许导入3000条数据,请重试!"),
    /**
     * 请上传正确的文件并确保当前sheet名称正确!
     */
    FILE_ERROR(300006, "请上传正确的文件并确保当前sheet名称正确!"),
    /**
     * 请上传正确的文件并确保当前sheet名称正确!
     */
    FILE_SHEET_ERROR(300006, "请按照正确模板进行上传!"),
    /**
     * 操作成功
     */
    OPS_SUCCESS(100000, "操作成功"),
    /**
     * 操作失败
     */
    OPS_ERROR(-100000, "操作失败"),

    OVER_FIVEHUNDRED_ERROR(-100000, "单次导入最多允许导入500条数据,请重试!");

    UploadEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private int code;
    private String msg;

    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getMsg() {
        return this.msg;
    }

    public void setCode(int code) {
        this.code = code;
    }

    @Override
    public UploadEnum setMsg(String msg) {
        this.msg = msg;
        return this;
    }
}
