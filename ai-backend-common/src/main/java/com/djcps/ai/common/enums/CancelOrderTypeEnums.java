package com.djcps.ai.common.enums;

/**
 * <AUTHOR>
 * @data 2019/12/23 11:11
 * @description
 */
public enum CancelOrderTypeEnums {
    /**
     * 用户自行取消、后台人员取消、系统取消
     */
    CANCEL_BY_USERSELF("用户自行取消"),
    CANCEL_BY_OPERATOR("后台人员取消"),
    CANCEL_BY_SYSTEM("系统取消");
    private String type;

    public String getType() {
        return this.type;
    }

    CancelOrderTypeEnums(String type) {
        this.type = type;
    }
}
