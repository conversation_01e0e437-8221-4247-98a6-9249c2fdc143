/*
 * Copyright 2018 huna.com All right reserved. This software is the confidential and proprietary information of huna.com
 * ("Confidential Information"). You shall not disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into with huna.com.
 */
package com.djcps.ai.common.log.logAppender;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 类OssLog.java的实现描述：上传日志
 * 
 * <AUTHOR> 2019年7月8日 下午4:11:12
 */
public class OssLog {

	public static final Logger logger = LoggerFactory.getLogger(OssLog.class);

}
