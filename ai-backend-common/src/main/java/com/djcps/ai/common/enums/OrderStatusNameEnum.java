package com.djcps.ai.common.enums;

/**
 * <AUTHOR> rsw
 * @date 2022/8/18 19:12.
 */
public enum OrderStatusNameEnum {
    //未付款
    NotPay(1, "待付款"),
    //已付款
    Paid(2, "已付款"),
    //已分发
    Distributed(3, "已分发"),
    //部分入库
    PartialBound(21, "部分入库"),
    //已入库
    InBound(4, "已入库"),
    /*已配货*/
    Picking(23, "已配货"),
    /*已提货*/
    PickupGoods(24, "已提货"),
    /*已装车*/
    TruckLoading(25, "已装车"),
    //配送中
    OutBound(5, "配送中"),
    /*已送达*/
    Delivery(32, "已送达"),
    //已签收
    Signed(6, "已签收"),
    //已取消
    Canceled(7, "已取消"),
    //搜索过滤未付款订单专用
    SearchOrder(8, "搜索过滤未付款订单专用"),
    /**
     * 生产中
     */
    IN_PRODUCTION(9, "生产中"),
    //其他状态，不在订单状态之内
    Default(10, "其他状态，不在订单状态之内");

    private String msg;
    private Integer code;
    OrderStatusNameEnum(Integer code, String msg) {
        this.msg = msg;
        this.code = code;
    }
    public static OrderStatusNameEnum getEmumByCode(Integer code) {
        for (OrderStatusNameEnum e : OrderStatusNameEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return Default;
    }
    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
}
