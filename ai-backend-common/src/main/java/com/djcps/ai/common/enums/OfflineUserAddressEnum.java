package com.djcps.ai.common.enums;

/**
 * <AUTHOR> rsw
 * @date 2022/6/8 19:34.
 */
public enum OfflineUserAddressEnum implements MsgInterface {
    /**
     * 参数异常
     */
    OPERATE_SUCCESS("666666", "操作成功"),
    OPERATE_FAIL("-1000", "操作失败"),
    FAIL("666667", "操作失败"),
    NAME_IS_ERROR("-1000", "客户名称应限30个字，前后不能为空格，限数字、字母、汉字、符号"),
    ORDER_SUCCESS("666666", "操作成功"),
    ORDER_REPEAT_ADDRESS("666670", "收货人和联系方式已存在"),
    CONTACT_WAY_IS_ERROR("-10000", "收货人限10个字，前后不能为空格，限数字、字母、汉字、符号"),
    USER_ADDRESS_DETAIL_IS_ERROR("-10000", "地址备注应限30个字"),
    USER_PARAM_ERROR("666674", "运营人员登录信息有误"),
    ADDRESS_EXCEEDED("666668", "一个客户最多只能添加20个收货信息"),
    ADDRESS_IS_NULL("-1000", "请填写省市区街道收货信息"),
    ADDRESS_CODE_IS_NULL("-1000", "地址库省数据缺失，请联系管理员"),
    PARAM_IS_ERROR("-1000", "参数不全"),
    ADDRESS_ALREADY_DELETED("-10000", "该地址已被删除"),
    ADDRESS_IS_DELETED("666672", "该地址已被删除"),
    REPEAT_ADDRESS("666670", "收货人和联系方式已存在"),
    ORDER_ADDRESS_FAILURE("678","该地址已失效，请重新选择"),

    ;
    private String code;
    private String desc;

    OfflineUserAddressEnum(String code, String msg) {
        this.code = code;
        this.desc = msg;
    }

    /**
     * 响应码
     *
     * @return
     */
    @Override
    public String getCode() {
        return this.code;
    }

    /**
     * 响应消息
     *
     * @return
     */
    @Override
    public String getDesc() {
        return this.desc;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public OfflineUserAddressEnum setDesc(String desc) {
        this.desc = desc;
        return this;
    }
}
