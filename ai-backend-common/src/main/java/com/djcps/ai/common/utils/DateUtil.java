package com.djcps.ai.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.Calendar;
import java.util.Date;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by jmb on 2017/9/1.
 *
 * <AUTHOR>
 */
@Slf4j
public class DateUtil extends cn.hutool.core.date.DateUtil {

	/**
	 * 日志规范
	 */
	public static SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

	public static String PATTERN_DATETIME = "yyyy-MM-dd HH:mm:ss";

	public static String PATTERN_LOCAL_DATETIME = "yyyy-MM-dd HH:mm";

	public static String PATTERN__DATETIME = "yyyyMMddHHmmssSSS";

	public static String PATTERN_DATETIME_ff = "yyyy-MM-dd HH:mm:ss.SSS";

	public static String PATTERN_DATE = "yyyy-MM-dd";

	public static String DATE_PATTERN = "yyyyMMdd";

	public static String fmt_y_m_d_h_m_s_sss_num = "yyyyMMddHHmmssSSS";

	public static final String DATE_TIME_2_FORMATTER = "yyyyMMddHHmmss";

	public static final String TIMESTAMP_FORMATTER = "yyyy-MM-dd HH:mm:ss.SSS";
	public static final String TIMESTAMP_FORMATTER_3 = "yyyy-MM-dd HH:mm:ss:SSS";
	public static final String TIMESTAMP_FORMATTER_2 = "yyyy-MM-dd H:m:s:s";

	public static final String DATE_TIME_FORMATTER = "yyyy-MM-dd HH:mm:ss";

	public static final String DATE_FORMATTER = "yyyy-MM-dd";

	public static final String YEAR_MONTH_FORMATTER = "yyyy-MM";

	public static final String YEAR_MONTH_DAY_FORMATTER = "yyyyMMdd";

	public static final String YEAR = "yyyy";

	/**
	 * String 转换成 date
	 *
	 * @return Date
	 */
	public static Date getDateFromStr(String dateStr) {
		try {
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			Date date = simpleDateFormat.parse(dateStr);
			return date;
		} catch (ParseException e) {
			log.error("时间转换出错");
		}
		return null;
	}

	/**
	 * 指定时间的 00：00：00的时间
	 *
	 * @return Date
	 */
	public static Date getStartTime(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		return calendar.getTime();
	}

	/**
	 * 指定时间的 23：59：59的时间
	 *
	 * @return Date
	 */
	public static Date getEndTime(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.HOUR_OF_DAY, 23);
		calendar.set(Calendar.MINUTE, 59);
		calendar.set(Calendar.SECOND, 59);
		calendar.set(Calendar.MILLISECOND, 0);
		return calendar.getTime();
	}

	/**
	 * 指定时间的 00：00：00的时间
	 *
	 * @return Date
	 */
	public static Date getBeforeDownTime(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.DATE, -7);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		return calendar.getTime();
	}

	/**
	 * 拿到当前月份加最近三个月之前的时间
	 *
	 * @return
	 */
	public static long getThreeMonthsEarly() {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(new Date());
		calendar.add(Calendar.MONTH, -3);
		calendar.set(Calendar.DATE, 1);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 000);
		return calendar.getTime().getTime();
	}

	/**
	 * 格式化日期
	 *
	 * @param date   日期
	 * @param format 格式
	 * @return java.lang.String
	 * <AUTHOR>
	 * @createTime 2021/6/24 14:28
	 */
	public static String getFormatDate(Date date, SimpleDateFormat format) {
		return format.format(date);
	}

	/**
	 * 校验搜索时间
	 *
	 * @param start
	 * @param end
	 * @return
	 */
	public static boolean checkSearchDate(String start, String end) throws ParseException {
		// 平台默认搜索时间开始，不可早于该时间点
		String sourceTime = "2017-12-03 00:00:00";
		long sourceLong = format.parse(sourceTime).getTime();
		if (start != null && end != null) {
			long starttime = format.parse(start).getTime();
			long endtime = format.parse(end).getTime();
			return starttime <= endtime && sourceLong <= starttime;
		}
		return true;
	}

	/**
	 * 拿到当前月份加最近三个月之前的日期
	 *
	 * @return
	 */
	public static Date getThreeMonthsEarlyDate() {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(new Date());
		calendar.add(Calendar.MONTH, -3);
		calendar.set(Calendar.DATE, 1);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 000);
		return calendar.getTime();
	}

	/**
	 * 获取近一年的时间
	 *
	 * @return
	 */
	public static Date getBeforYear() {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(new Date());
		calendar.add(Calendar.YEAR, -1);
		calendar.set(Calendar.DAY_OF_YEAR, -1);
		calendar.set(Calendar.DATE, 1);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 000);
		return calendar.getTime();
	}

	/**
	 * 校验开始时间
	 *
	 * @param startTime
	 * @return
	 * @throws ParseException
	 */
	public static boolean checkStartTime(String startTime) throws ParseException {
		long starttime = format.parse(startTime).getTime();
		return DateUtil.getThreeMonthsEarly() <= starttime;
	}

	/**
	 * 时间格式化
	 *
	 * @param time       时间字符串
	 * @param dataFormat 时间格式
	 * @return
	 * @throws ParseException
	 */
	public static Date timeFormat(Date time, String dataFormat) throws ParseException {
		SimpleDateFormat sdf = new SimpleDateFormat(dataFormat);
		return sdf.parse(sdf.format(time));
	}

	/**
	 * 时间格式化
	 *
	 * @param time       时间字符串
	 * @param dataFormat 时间格式
	 * @return
	 * @throws ParseException
	 */
	public static String charFormat(String time, String dataFormat) throws ParseException {
		if (time == null) {
			return null;
		}
		dataFormat = Optional.ofNullable(dataFormat).orElse("yyyy-MM-dd HH:mm:ss");
		SimpleDateFormat sdf = new SimpleDateFormat(dataFormat);
		return sdf.format(sdf.parse(time));
	}

	/**
	 * 时间格式化成字符串
	 *
	 * @param time       时间字符串
	 * @param dataFormat 时间格式
	 * @return
	 */
	public static String timeFormatToStr(Date time, String dataFormat) {
		if (time == null) {
			return null;
		}
		dataFormat = Optional.ofNullable(dataFormat).orElse("yyyy-MM-dd HH:mm:ss");
		SimpleDateFormat sdf = new SimpleDateFormat(dataFormat);
		return sdf.format(time);
	}

	/**
	 * 获取当前日期前90天的日期
	 *
	 * @return
	 */
	public static String getNinetyDays(Date time, int day) {
		Calendar now = Calendar.getInstance();
		now.setTime(time);
		now.add(Calendar.DAY_OF_MONTH, day);
		String endDate = new SimpleDateFormat("yyyy-MM-dd 00:00:00").format(now.getTime());
		return endDate;
	}

	/**
	 * 获取当前日期后几天时间
	 *
	 * @param date
	 * @param i
	 * @return
	 */
	public static Date addDayOfDate(Date date, int i) {
		Calendar c = Calendar.getInstance();
		c.setTime(date);
		c.add(Calendar.DATE, i);
		Date newDate = c.getTime();
		return newDate;
	}

	/**
	 * 要判断是否在当天24h内的时间
	 *
	 * @param testTimeOne
	 * @returntestTimeOne
	 */
	public static boolean isToday(String testTimeOne) {
		// 时间格式化
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date inputJudgeDate = null;
		boolean flag = false;
		// 获取当前系统时间
		long longDate = System.currentTimeMillis();
		Date nowDate = new Date(longDate);
		String format = dateFormat.format(nowDate);
		String subDate = format.substring(0, 10);
		// 定义每天的24h时间范围
		String beginTime = subDate + " 00:00:00";
		String endTime = subDate + " 23:59:59";
		Date paseBeginTime = null;
		Date paseEndTime = null;
		try {
			inputJudgeDate = dateFormat.parse(testTimeOne);
			paseBeginTime = dateFormat.parse(beginTime);
			paseEndTime = dateFormat.parse(endTime);
		} catch (Exception e) {
			log.error("时间格式转换失败： {}", e.getMessage());
		}
		if (inputJudgeDate.after(paseBeginTime) && inputJudgeDate.before(paseEndTime)) {
			flag = true;
		}
		return flag;
	}

	/**
	 * 格式化时间
	 *
	 * @param date date
	 * @return string
	 */
	public static String formatDate(Date date) {
		if (date == null) {
			return "";
		}
		return format.format(date);
	}

	/**
	 * 获取当前日期后几天时间
	 *
	 * @param day 负数为前几天，正数为后几天
	 * @return
	 */
	public static String dayAfter(int day) {
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DATE, day);
		return format.format(calendar.getTime());
	}

	/**
	 * 时间字符串转Date
	 *
	 * @param time
	 * @return
	 * @throws ParseException
	 */
	public static Date strToDate(String time) {
		if (StringUtils.isBlank(time)) {
			return null;
		}
		try {
			return format.parse(time);
		} catch (ParseException e) {
			log.error("时间转换出错");
		}
		return null;
	}

	/**
	 * 返回string的时间戳
	 *
	 * @param mils
	 * @return
	 */
	public static String fomartToNumber(long mils) {
		LocalDateTime time = new Date(mils).toInstant().atOffset(ZoneOffset.of("+8")).toLocalDateTime();
		return formatLocalDateTime(time, PATTERN__DATETIME);
	}

	/**
	 * 获取当天
	 *
	 * @return
	 */
	public static Date getCurrentToday() {
		LocalDate today = LocalDate.now();
		Instant instant = today.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant();
		return Date.from(instant);
	}

	/**
	 * 获取当月第一天
	 *
	 * @return
	 */
	public static Date getCurrentMouth() {
		LocalDate date = LocalDate.now();
		LocalDate firstday = date.with(TemporalAdjusters.firstDayOfMonth());
		Instant instant = firstday.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant();
		return Date.from(instant);
	}

	/**
	 * 偏移天
	 *
	 * @param offset
	 * @return
	 */
	public static Date getoffsetDay(int offset) {
		LocalDate localDate = LocalDate.now().plusDays(offset);
		Instant instant = localDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant();
		return Date.from(instant);
	}

	/**
	 * jdk1.8日期转换
	 *
	 * <AUTHOR>
	 *
	 * @return java.util.Date
	 *
	 * @date 2021/9/13
	 */
	public static Date parseSync8(String dateStr, String pattern) {
		if (StringUtils.isBlank(pattern)) {
			pattern = DATE_PATTERN;
		}
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
		LocalDate date = LocalDate.parse(dateStr, formatter);
		ZoneId zoneId = ZoneId.systemDefault();
		ZonedDateTime zdf = date.atStartOfDay(zoneId);
		return Date.from(zdf.toInstant());
	}

	/**
	 * 日期格式化
	 *
	 * <AUTHOR>
	 *
	 * @return java.lang.String
	 *
	 * @date 2021/9/13
	 */
	public static String formatSync8(Date date, String pattern) {
		if (StringUtils.isBlank(pattern)) {
			pattern = DATE_PATTERN;
		}
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
		ZoneId zoneId = ZoneId.systemDefault();
		Instant instant = date.toInstant();
		LocalDateTime now = instant.atZone(zoneId).toLocalDateTime();
		return now.format(formatter);
	}

	/**
	 * 默认返回时间格式（yyyy-MM-dd) LocalDate
	 *
	 * <AUTHOR>
	 *
	 * @return java.time.LocalDate
	 *
	 * @date 2021/9/13
	 */
	public static LocalDate parseLocalDate(String dateStr, String pattern) {
		if (StringUtils.isBlank(dateStr)) {
			return null;
		}
		if (StringUtils.isBlank(pattern)) {
			pattern = PATTERN_DATE;
		}
		LocalDate localDate = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(pattern));
		return localDate;
	}

	/**
	 * 默认返回时间格式（yyyy-MM-dd HH:mm:ss) LocalDateTime
	 *
	 * <AUTHOR>
	 *
	 * @return java.time.LocalDateTime
	 *
	 * @date 2021/9/13
	 */
	public static LocalDateTime parseLocalDateTime(String dateStr, String pattern) {
		if (StringUtils.isBlank(dateStr)) {
			return null;
		}
		if (StringUtils.isBlank(pattern)) {
			pattern = PATTERN_DATETIME;
		}
		LocalDateTime localDate = LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern(pattern));
		return localDate;
	}

	/**
	 * localDate转换为Date时间
	 *
	 * <AUTHOR>
	 *
	 * @return java.util.Date
	 *
	 * @date 2021/9/13
	 */
	public static Date localDateConvert(LocalDate localDate) {
		LocalDate currentDate = Optional.ofNullable(localDate).orElse(LocalDate.now());
		ZoneId zoneId = ZoneId.systemDefault();
		ZonedDateTime zdf = currentDate.atStartOfDay(zoneId);
		return Date.from(zdf.toInstant());
	}

	/**
	 * localDateTime转换为Date
	 *
	 * <AUTHOR>
	 *
	 * @return java.util.Date
	 *
	 * @date 2021/9/13
	 */
	public static Date localDateTimeConvert(LocalDateTime localDateTime) {
		LocalDateTime currentDate = Optional.ofNullable(localDateTime).orElse(LocalDateTime.now());
		ZoneId zoneId = ZoneId.systemDefault();
		ZonedDateTime zdf = currentDate.atZone(zoneId);
		return Date.from(zdf.toInstant());
	}

	/**
	 * 将LocalDate 格式化
	 *
	 * <AUTHOR>
	 *
	 * @return java.lang.String
	 *
	 * @date 2021/9/13
	 */
	public static String formatLocalDate(LocalDate localDate, String pattern) {
		// 如果设置为空,择获取当前时间
		LocalDate currentDate = Optional.ofNullable(localDate).orElse(LocalDate.now());
		String currentPattern = Optional.ofNullable(pattern).orElse(PATTERN_DATE);
		DateTimeFormatter ofPattern = DateTimeFormatter.ofPattern(currentPattern);
		return ofPattern.format(currentDate);
	}

	/**
	 * 将localDateTime格式化
	 *
	 * <AUTHOR>
	 *
	 * @return java.lang.String
	 *
	 * @date 2021/9/13
	 */
	public static String formatLocalDateTime(LocalDateTime localDateTime, String pattern) {
		// 如果设置为空,择获取当前时间
		LocalDateTime currentDate = Optional.ofNullable(localDateTime).orElse(LocalDateTime.now());
		String currentPattern = Optional.ofNullable(pattern).orElse(PATTERN_DATE);
		DateTimeFormatter ofPattern = DateTimeFormatter.ofPattern(currentPattern);
		return ofPattern.format(currentDate);
	}

	/**
	 * 获取本月最后一天时间
	 *
	 * @param localDate
	 * @return
	 */
	public static LocalDate lastDayLocalDate(LocalDate localDate) {
		LocalDate today = Optional.ofNullable(localDate).orElse(LocalDate.now());
		return today.with(TemporalAdjusters.lastDayOfMonth());
	}

	/**
	 * 加减天数
	 *
	 * <AUTHOR>
	 *
	 * @return java.time.LocalDate
	 *
	 * @date 2021/9/13
	 */
	public static LocalDate plusDays(LocalDate localDate, Integer day) {
		LocalDate today = Optional.ofNullable(localDate).orElse(LocalDate.now());
		return today.plusDays(day);
	}

	/**
	 * 获得当天零时零分零秒(yyyy-MM-dd HH:mm:ss)
	 *
	 * @return
	 */
	public String initDateByDay() {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(new Date());
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		return formatSync8(calendar.getTime(), PATTERN_DATETIME);
	}

	/**
	 * 获得某天最小时间 yyyy-MM-dd 00:00:00
	 *
	 * @param date
	 * @return
	 */
	public static Date getStartOfDay(Date date) {
		LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()),
				ZoneId.systemDefault());
		LocalDateTime startOfDay = localDateTime.with(LocalTime.MIN);
		return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
	}

	/**
	 * 获得某天最小时间 yyyy-MM-dd 23:59:59
	 *
	 * @param date
	 * @return
	 */
	public static Date getMaxOfDay(Date date) {
		LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()),
				ZoneId.systemDefault());
		LocalDateTime startOfDay = localDateTime.with(LocalTime.MAX);
		return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
	}

	/**
	 * 获得某天最小时间 yyyy-MM-dd 00:00:00
	 *
	 * @param date
	 * @param pattern
	 * @return
	 */
	public static Date getStartOfDayByString(String date, String pattern) {
		LocalDateTime localDateTime = parseLocalDateTime(date, pattern);
		LocalDateTime startOfDay = localDateTime.with(LocalTime.MIN);
		return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
	}

	/**
	 * 获得某天最大时间 yyyy-MM-dd 23:59:59
	 *
	 * @param date
	 * @param pattern
	 * @return
	 */
	public static Date getMaxOfDayByString(String date, String pattern) {
		LocalDateTime localDateTime = parseLocalDateTime(date, pattern);
		LocalDateTime startOfDay = localDateTime.with(LocalTime.MAX);
		return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
	}

	/**
	 * 拿到当前月份加最近三个月之前的时间(当前月份的不算进去)
	 *
	 * @return
	 */
	public static Date getThreeMonth() {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(new Date());
		int month = calendar.get(Calendar.MONTH);
		int year = calendar.get(Calendar.YEAR);
		Calendar instance = Calendar.getInstance();
		instance.set(Calendar.YEAR, year);
		instance.set(Calendar.MONTH, month);
		instance.add(Calendar.MONTH, -3);
		instance.set(Calendar.DATE, 0);
		instance.set(Calendar.HOUR_OF_DAY, 0);
		instance.set(Calendar.MINUTE, 0);
		instance.set(Calendar.SECOND, 0);
		instance.set(Calendar.MILLISECOND, 0);
		return instance.getTime();
	}

	/**
	 * 校验搜索时间
	 *
	 * @param start
	 * @param end
	 * @return
	 */
	public static boolean checkSearchDates(String start, String end, String flag) throws ParseException {
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		if (!org.springframework.util.StringUtils.isEmpty(start)
				&& !org.springframework.util.StringUtils.isEmpty(end)) {
			Date starttime = format.parse(start);
			Date endtime = format.parse(end);
			if (null != flag) {
				return starttime.before(endtime);
			} else {
				return starttime.before(endtime) || starttime.equals(endtime);
			}
		}
		return true;
	}

	/**
	 * 校验交期是否大于当天
	 *
	 * @param delivery
	 * @return
	 */
	public static boolean checkDelivery(Date delivery) {

		Calendar calendar = Calendar.getInstance();
		calendar.setTime(delivery);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		Date zero = calendar.getTime();
		Date date = new Date();
		if (date.after(zero)) {
			return false;
		}
		return true;
	}

	/**
	 * 校验交期是否大于当天
	 *
	 * @param delivery
	 * @return
	 */
	public static boolean checkDeliveryTooLater(Date delivery) {
		String time = formatDate("yyyy", new Date(System.currentTimeMillis()));
		Calendar calendar = Calendar.getInstance();
		calendar.clear();
		calendar.set(Calendar.YEAR, Integer.valueOf(time) + 1);
		calendar.roll(Calendar.DAY_OF_YEAR, -1);
		long timeNumber = calendar.getTimeInMillis() + 86400000;
		Date currYearLast = new Date(timeNumber);
		if (delivery.after(currYearLast)) {
			return false;
		}
		return true;
	}

	public static String formatDate(String partten, Date date) {
		if (date == null) {
			return null;
		}
		return getNewFormater(partten).format(date);
	}

	public static SimpleDateFormat getNewFormater(String partten) {
		return new SimpleDateFormat(partten);
	}

	private static final String[] timeFormat = { "HH:mm", "HH:mm:ss", "HH:mm:ss.SSS", "HH:mm:ss:SSS", "H:m:s:s",
			"HH:mm:ss,SSS", "HH时mm分ss秒", "HH时mm分", "HH时" };
	private static final String[] dateFormat = { "yyyy-MM-dd", "yyyy/MM/dd", "yyyy\\MM\\dd", "yyyy.MM.dd",
			"yyyy年MM月dd日", "yyyy年MM月dd号" };

	private static final String[] timeArr = {
			// 17:30
			"^[\\d]{1,2}:[\\d]{1,2}$",
			// 17:30:21
			"^[\\d]{1,2}:[\\d]{1,2}:[\\d]{1,2}$",
			// 17:30:21.123
			"^[\\d]{1,2}:[\\d]{1,2}:[\\d]{1,2}[\\.][\\d]{3}$", "^[\\d]{1,2}:[\\d]{1,2}:[\\d]{1,2}[:][\\d]{3}$",
			"^[\\d]{1,1}:[\\d]{1,1}:[\\d]{1,1}[:][\\d]{1}$",
			// 17:30:21,123
			"^[\\d]{1,2}:[\\d]{1,2}:[\\d]{1,2}[,][\\d]{3}$",
			// 17时30分21秒
			"^[\\d]{1,2}时[\\d]{1,2}分[\\d]{1,2}秒$",
			// 17时30分
			"^[\\d]{1,2}时[\\d]{1,2}分$",
			// 17时
			"^[\\d]{1,2}时$" };

	private static final String[] dateArr = {
			// 2020-06-01
			"^[\\d]{4}[-][\\d]{1,2}[-][\\d]{1,2}$",
			// 2020/06/01
			"^[\\d]{4}[/][\\d]{1,2}[/][\\d]{1,2}$",
			// 2020\06\01
			"^[\\d]{4}\\[\\d]{1,2}\\[\\d]{1,2}$",
			// 2020.06.01
			"^[\\d]{4}[.][\\d]{1,2}[.][\\d]{1,2}$",
			// 2020年06月01日
			"^[\\d]{4}年[\\d]{1,2}月[\\d]{1,2}日$",
			// 2020年06月01号
			"^[\\d]{4}年[\\d]{1,2}月[\\d]{1,2}号$" };

	private static final String[] noBlankArr = {
			// 2020年
			"^[\\d]{4}(年)$",
			// 2020年6月
			"^[\\d]{4}(年)[\\d]{1,2}(月)$",
			// 2020年6月1日
			"^[\\d]{4}(年)[\\d]{1,2}(月)[\\d]{1,2}(日)$",
			// 2020年6月1日17时
			"^[\\d]{4}(年)[\\d]{1,2}(月)[\\d]{1,2}(日)[\\d]{1,2}(时)$",
			// 2020年6月1日17时25分
			"^[\\d]{4}(年)[\\d]{1,2}(月)[\\d]{1,2}(日)[\\d]{1,2}(时)[\\d]{1,2}(分)$",
			// 2020年6月1日17时25分16秒
			"^[\\d]{4}(年)[\\d]{1,2}(月)[\\d]{1,2}(日)[\\d]{1,2}(时)[\\d]{1,2}(分)[\\d]{1,2}(秒)$",
			// 20200601
			"^[\\d]{8}$",
			// 202006010957
			"^[\\d]{12}$",
			// 20200601095720
			"^[\\d]{14}$",
			// 2020-06-01
			"^[\\d]{4}[-][\\d]{1,2}[-][\\d]{1,2}$",
			// 2020/06/01
			"^[\\d]{4}[/][\\d]{1,2}[/][\\d]{1,2}$",
			// 2020\06\01
			"^[\\d]{4}\\[\\d]{1,2}\\[\\d]{1,2}$",
			// 2020.06.01
			"^[\\d]{4}[.][\\d]{1,2}[.][\\d]{1,2}$",
			// 2020年06月01号
			"^[\\d]{4}(年)[\\d]{1,2}(月)[\\d]{1,2}(号)",
			// 2020年06月01号12
			"^[\\d]{4}(年)[\\d]{1,2}(月)[\\d]{1,2}(号)[\\d]{1,2}$",
			// 2020年06月01号12时
			"^[\\d]{4}(年)[\\d]{1,2}(月)[\\d]{1,2}(号)[\\d]{1,2}(时)$",
			// 2020年06月01号12时12分
			"^[\\d]{4}(年)[\\d]{1,2}(月)[\\d]{1,2}(号)[\\d]{1,2}(时)[\\d]{1,2}(分)$",
			// 2020年06月01号12时12分12秒
			"^[\\d]{4}(年)[\\d]{1,2}(月)[\\d]{1,2}(号)[\\d]{1,2}(时)[\\d]{1,2}(分)[\\d]{1,2}(秒)$", };

	private static final String[] noBlankFormat = { "yyyy年", "yyyy年MM月", "yyyy年MM月dd日", "yyyy年MM月dd日HH时",
			"yyyy年MM月dd日HH时mm分", "yyyy年MM月dd日HH时mm分ss秒", "yyyyMMdd", "yyyyMMddHHmm", "yyyyMMddHHmmss", "yyyy-MM-dd",
			"yyyy/MM/dd", "yyyy\\MM\\dd", "yyyy.MM.dd", "yyyy年MM月dd号", "yyyy年MM月dd号HH", "yyyy年MM月dd号HH时",
			"yyyy年MM月dd号HH时mm分", "yyyy年MM月dd号HH时mm分ss秒", };

	/**
	 * @Description: 时间字符串转Date
	 * @Param: 需要转换的时间字符串
	 * @return: Date 转换后的Date类型时间
	 */
	public static Date parseDate(String dateStr) {
		if (dateStr == null || "".equals(dateStr)) {
			return null;
		}
		Pattern r = Pattern.compile("\\s");
		Matcher m = r.matcher(dateStr);
		if (m.find()) {
			String blank = m.group();
			String[] split = dateStr.split(blank);
			if (split.length != 2)
				return null;
			int dateIndex = Integer.MIN_VALUE, timeIndex = Integer.MIN_VALUE;
			for (int i = 0; i < dateArr.length; i++) {
				if (split[0].matches(dateArr[i])) {
					dateIndex = i;
					break;
				}
			}
			for (int i = 0; i < timeArr.length; i++) {
				if (split[1].matches(timeArr[i])) {
					timeIndex = i;
					break;
				}
			}
			String format = "";
			try {
				format = dateFormat[dateIndex] + blank + timeFormat[timeIndex];
			} catch (IndexOutOfBoundsException e) {
				return null;
			}
			return parse(dateStr, format);
		} else {
			for (int i = 0; i < noBlankArr.length; i++) {
				if (dateStr.matches(noBlankArr[i])) {
					return parse(dateStr, noBlankFormat[i]);
				}
			}
		}
		return null;
	}
}
