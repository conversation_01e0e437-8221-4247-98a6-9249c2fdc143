package com.djcps.ai.common.id;

import java.util.HashSet;
import java.util.Set;

public class TestSnow {

	private static Set<Long> keys = new HashSet<>();

	public static void main(String[] args) throws Exception {
		for (int i = 0; i < 10000; i++) {
			TestSnow.GetKey runner = new TestSnow().new GetKey();
			new Thread(runner).start();
		}
		System.out.println(keys);

	}

	class GetKey implements Runnable {
		@Override
		public void run() {
			IdUtils key = IdUtils.getIdUtils();

			IdVo idVo = key.getIdVo();
			Long k = idVo.getNextLongId();
			String v = idVo.getNextStringId();
			// System.out.println(k + "\t" + v);
			if (!keys.contains(k)) {
				keys.add(k);
			} else {
				System.out.println("重复:" + k + "\t" + v);
			}
		}
	}

}
