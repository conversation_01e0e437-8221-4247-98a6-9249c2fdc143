/*
 * Copyright 2018 me.com All right reserved. This software is the
 * confidential and proprietary information of me.com ("Confidential
 * Information"). You shall not disclose such Confidential Information and shall
 * use it only in accordance with the terms of the license agreement you entered
 * into with me.com.
 */
package com.djcps.ai.common.id;

import lombok.Data;

/**
 * 类IdVo.java 的实现描述：主键idVO
 *
 * @author: yeb 2021/9/15 下午5:38
 **/
@Data
public class IdVo {

	private Long nextLongId;

	private String nextStringId;

}
