package com.djcps.ai.common.enums;

/**
 * <AUTHOR>
 * @date 2017/10/11
 * 瓦楞枚举
 */
public enum CorrugatedEnum {
    /**
     * bc瓦
     */
    BC(1, "BC瓦"),
    BE(2, "BE瓦"),
    C(3, "单C瓦"),
    B(4, "单B瓦"),
    E(5, "单E瓦"),
    EBC(6, "EBC瓦"),
    EE(7, "EE瓦"),
    A(8, "单A瓦"),
    D(9, "单D瓦"),
    AA(10, "AA瓦"),
    AB(11, "AB瓦"),
    AC(12, "AC瓦"),
    AE(13, "AE瓦"),
    BB(14, "BB瓦"),
    CC(15, "CC瓦"),
    CE(16, "CE瓦"),
    BAB(17, "BAB瓦"),
    BAA(18, "BAA瓦"),
    CAC(19, "CAC瓦"),
    BAC(20, "BAC瓦"),
    F(21, "单F瓦"),
    BCA(22, "BCA瓦"),
    BCC(23, "BCC瓦"),
    BEE(24, "BEE瓦"),
    CEB(25, "CEB瓦"),
    BCB(26, "BCB瓦"),
    EBA(27, "EBA瓦"),
    DEFAULT(0,null);
    private Integer code;
    private String corrugated;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getCorrugated() {
        return corrugated;
    }

    public void setCorrugated(String corrugated) {
        this.corrugated = corrugated;
    }

    CorrugatedEnum(Integer code, String corrugated) {
        this.code = code;
        this.corrugated = corrugated;
    }

    public static CorrugatedEnum getCorrugatedByCode(Integer code) {
        for (CorrugatedEnum corrugatedEnum : CorrugatedEnum.values()) {
            if (code.equals(corrugatedEnum.getCode())) {
                return corrugatedEnum;
            }
        }
        return null;
    }

    /**
     * 去单，瓦方法
     * @param code
     * @return
     */
    public static String getCorrugatedByCodeNoChart(Integer code) {
        for (CorrugatedEnum corrugatedEnum : CorrugatedEnum.values()) {
            if (code.equals(corrugatedEnum.getCode())) {
                return corrugatedEnum.getCorrugated().replaceAll("单", "").replaceAll("瓦", "");
            }
        }
        return null;
    }

    /**
     * 获取瓦楞型
     * @param code
     * @return
     */
    public static String getValueByCode(Integer code) {
        for (CorrugatedEnum corrugatedEnum : CorrugatedEnum.values()) {
            if (code.equals(corrugatedEnum.getCode())) {
                return corrugatedEnum.getCorrugated();
            }
        }
        return null;
    }

    public static Integer getCorrugatedByValue(String value) {
        for (CorrugatedEnum corrugatedEnum : CorrugatedEnum.values()) {
            if (value.equals(corrugatedEnum.getCorrugated())) {
                return corrugatedEnum.getCode();
            }
        }
        return null;
    }
}
