package com.djcps.ai.common.threadlocal;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * ThreadLocalUser.java的实现描述：线程中存用户信息
 * 
 * <AUTHOR> 2018年12月28日 上午10:21:40
 */
public class ThreadLocalUser {

	private static final Logger logger = LoggerFactory.getLogger(ThreadLocalUser.class);

	/** 用户dubbo接口请求信息本地线程 */
	private static final ThreadLocal<CommonParam> PARAM_THREAD_LOCAL = new ThreadLocal<CommonParam>();

	public static CommonParam getCommonParam() {
		return PARAM_THREAD_LOCAL.get();
	}

	/**
	 * s 获取请求的用户id
	 * 
	 * @return
	 */
	public static String getUserId() {
		CommonParam user = getCommonParam();
		if (user != null) {
			return user.getUserId();
		}
		return null;
	}

    /**
     * s 获取请求的用户id
     *
     * @return
     */
    public static String getToken() {
        CommonParam user = getCommonParam();
        if (user != null) {
            return user.getToken();
        }
        return null;
    }

	/**
	 * 获取请求的版本号
	 * 
	 * @return
	 */
	public static String getVersion() {
		CommonParam request = getCommonParam();
		if (request != null && StringUtils.isNotBlank(request.getVersion())) {
			return request.getVersion().replace(".", "_");
		}
		return null;
	}

	/**
     * 获取区域信息
     */
    public static String getUserCode() {
        CommonParam request = getCommonParam();
        if(request!=null && request.getUserInfo()!=null){
            return request.getUserInfo().getOcode();
        }
        return null;

    }
    /**
     * 获取当前人用户id
     */
    public static UserInfo getUserInfo() {
        CommonParam request = getCommonParam();
        if(request!=null && request.getUserInfo()!=null){
            return request.getUserInfo();
        }
        return null;

    }
    /**
     * 获取区域信息
     */
    public static String getUserCompany() {
        CommonParam request = getCommonParam();
        if(request!=null && request.getUserInfo()!=null){
            return request.getUserInfo().getUcompany();
        }
        return null;
    }

    /**
     * 获取名称信息
     */
    public static String getUserName() {
        CommonParam request = getCommonParam();
        if(request!=null && request.getUserInfo()!=null){
            return request.getUserInfo().getUname();
        }
        return null;
    }


    /**
	 * 设置登陆信息
	 *
	 * @param
	 */
	public static void setCommonParam(CommonParam param) {
		CommonParam param1 = getCommonParam();
		if (param1 != null) {
			PARAM_THREAD_LOCAL.remove();
		}
		PARAM_THREAD_LOCAL.set(param);
	}

	public static void clear() {
		PARAM_THREAD_LOCAL.remove();
	}
}
