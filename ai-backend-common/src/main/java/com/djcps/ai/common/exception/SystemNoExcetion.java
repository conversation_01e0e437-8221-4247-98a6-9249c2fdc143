package com.djcps.ai.common.exception;


/**
 * <AUTHOR>
 * @Description
 * @Date 2022/8/16 11:08
 */
public class SystemNoExcetion extends RuntimeException{
    /**
     * 异常编码
     */
    private int codeNo = -1;
    /**
     * 提示信息
     */
    private String message;

    public SystemNoExcetion() {
    }

    public SystemNoExcetion(int codeNo, String message) {
        super(message);
        this.codeNo = codeNo;
        this.message = message;
    }

    public SystemNoExcetion(SystemNoExcetion codeEnum) {
        super(codeEnum.getMessage());
        this.codeNo = codeEnum.getCodeNo();
        this.message = codeEnum.getMessage();
    }

    public SystemNoExcetion(String message) {
        super(message);
        this.message = message;
    }

    public int getCodeNo() {
        return codeNo;
    }

    public void setCodeNo(int codeNo) {
        this.codeNo = codeNo;
    }

    /**
     * @return the message
     */
    @Override
    public String getMessage() {
        return message;
    }

    /**
     * @param message the message to set
     */
    public void setMessage(String message) {
        this.message = message;
    }
}
