/*
 * Copyright 2022 51zjb.com All right reserved. This software is the
 * confidential and proprietary information of 51zjb.com ("Confidential
 * Information"). You shall not disclose such Confidential Information and shall
 * use it only in accordance with the terms of the license agreement you entered
 * into with 51zjb.com.
 */
package com.djcps.ai.common.bigdecimal;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;

/**
 * 类BigdecimalUtils.java的实现描述：bigdecimal工具类
 * 
 * <AUTHOR> 2022年9月2日 下午3:30:20
 */
public class BigdecimalUtils {

	public static BigDecimal stringToDecimal(String str) {
		if (StringUtils.isNotBlank(str)) {
			return new BigDecimal(str);
		} else {
			return BigDecimal.ZERO;
		}
	}

	public static BigDecimal stringToDecimalNull(String str) {
		if (StringUtils.isNotBlank(str)) {
			return new BigDecimal(str);
		} else {
			return null;
		}
	}

	public static String strByDecimalBNull(BigDecimal decimal) {
		if (decimal != null) {
			DecimalFormat df = new DecimalFormat("#.##");
			return df.format(decimal);
		} else {
			return null;
		}
	}
}
