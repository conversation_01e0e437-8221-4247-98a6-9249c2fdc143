package com.djcps.ai.common.threadlocal;

 import lombok.Data;

 /**
* 类 UserInfo.java 描述: auth 用户信息
*
* @author: yeb 2022/8/25 上午11:50
**/
@Data
public class UserInfo {
    /**
     * deptName : 系统部
     * ufdepartment_id : 101
     * userstatus : 正式员工
     * rname : 系统部经理
     * utype : 0
     * uinduction : 2016-02-22
     * oprovince : 浙江省
     * ocity : 温州市
     * ocity_code : 03
     * uids : 1000231
     * ocode : 3303
     * uwholespell : yeqiange
     * id : 422
     * uphone : 15257705598
     * uip : 空
     * ujob : 经理
     * oaddress : 东经一路1号
     * uname : 叶千阁
     * usex : 1
     * uimage : img0/M00/05/46/ChpbMFlxU5WAQNO4AABJSDH7U6A223.jpg
     * deptId : 86745cc9b54041edb6cbc32f18476109
     * ushort_phone : 665598
     * uposition_name : 系统部经理
     * uemail : <EMAIL>
     * direct_supervisor_id : 258
     * oname : 东经科技
     * ubirthday : 1993-05-07
     * oarea : 瓯海区
     * oprovince_code : 33
     * ucompany : 100
     * ufdepartment : 研发中心
     * faccountid : e4ceaea7-b6ab-11e9-8613-000c299aa77b
     */

    private String deptName;
    private String ufdepartment_id;
    private String userstatus;
    private String rname;
    private String utype;
    private String uinduction;
    private String oprovince;
    private String ocity;
    private String ocity_code;
    private String uids;
    private String ocode;
    private String uwholespell;
    private String id;
    private String uphone;
    private String uip;
    private String ujob;
    private String oaddress;
    private String uname;
    private String usex;
    private String uimage;
    private String deptId;
    private String ushort_phone;
    private String uposition_name;
    private String uemail;
    private String direct_supervisor_id;
    private String oname;
    private String ubirthday;
    private String oarea;
    private String oprovince_code;
    private String ucompany;
    private String ufdepartment;
    private String faccountid;
}
