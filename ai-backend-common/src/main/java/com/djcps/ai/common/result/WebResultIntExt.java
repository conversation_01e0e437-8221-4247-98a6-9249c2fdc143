package com.djcps.ai.common.result;

import com.djcps.ai.common.enums.MsgInterface;
import com.djcps.ai.common.enums.MsgIntface;
import com.djcps.ai.common.enums.SystemCodeEnum;

/**
 * <AUTHOR> rsw
 * @date 2022/8/12 11:11.
 */
public class WebResultIntExt<T> {
	private boolean success;
	private int code = 0; // "0/-1"
	private String msg = "";
	private T data;

	public static <T> WebResultIntExt<T> success() {
		return new WebResultIntExt<>(true);
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static <T> WebResultIntExt<T> success(MsgIntface codeEnum) {
		return new WebResultIntExt(true).code(codeEnum.getCode()).message(codeEnum.getMsg());
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static <T> WebResultIntExt<T> success(String message) {
		return new WebResultIntExt(true).message(message);
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static <T> WebResultIntExt<T> failure() {
		return new WebResultIntExt(false).message("系统异常");
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static <T> WebResultIntExt<T> failure(int errorCode, String message) {
		return new WebResultIntExt(false).code(errorCode).message(message);
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static <T> WebResultIntExt<T> failure(SystemCodeEnum codeEnum) {
		return new WebResultIntExt(false).code(Integer.parseInt(codeEnum.getCode())).message(codeEnum.getDesc());
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static <T> WebResultIntExt<T> failure(MsgIntface codeEnum) {
		return new WebResultIntExt(false).code(codeEnum.getCode()).message(codeEnum.getMsg());
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static <T> WebResultIntExt<T> failure(MsgInterface codeEnum) {
		return new WebResultIntExt(false).code(Integer.parseInt(codeEnum.getCode())).message(codeEnum.getDesc());
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static <T> WebResultIntExt<T> failure(String message) {
		return new WebResultIntExt(false).message(message);
	}

	public WebResultIntExt(boolean success, int code, String message) {
		this.success = success;
		this.code = code;
	}

	public WebResultIntExt(boolean success, int code, String message, T data ) {
		this.success = success;
		this.code = code;
		this.msg = message;
		this.data = data;
	}

	public WebResultIntExt(boolean success) {
		this.success = success;
	}

	public WebResultIntExt() {
	}

	public WebResultIntExt(boolean success, String message) {
		this(success, 0, message);
	}

	public boolean isSuccess() {
		return success;
	}

	public WebResultIntExt<T> setSuccess(boolean success) {
		this.success = success;
		return this;
	}

	public int getCode() {
		return code;
	}

	public WebResultIntExt<T> code(int code) {
		this.code = code;
		return this;
	}

	public String getMsg() {
		return msg;
	}

	public WebResultIntExt<T> message(String message) {
		this.msg = message;
		return this;
	}

	public WebResultIntExt(T data) {
		this.success = true;
		this.data = data;
		// 兼容老订单服务返回的code码
		this.code = 10000;

	}

	/**
	 * @return the data
	 */
	public T getData() {
		return data;
	}

	/**
	 * @param data the data to set
	 */
	public void setData(T data) {
		this.data = data;
	}

}
