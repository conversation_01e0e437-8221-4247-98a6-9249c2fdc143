/*
 * Copyright 2018 me.com All right reserved. This software is the confidential and proprietary information of me.com
 * ("Confidential Information"). You shall not disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into with me.com.
 */
package com.djcps.ai.common.constants;

/**
 * 类RedisKey.java的实现描述：rediskey
 * 
 * <AUTHOR> 2018年12月29日 下午6:52:18
 */
public class RedisKey {

	public static final String ware_house_test_key = "ware:house:test:key";
	public static final String ware_easyv_url_key = "ware:house:easyv:url:key";
	/** 第三次大会数字孪生看板中心数据，hashMap */
	public static final String ware_easyv_thrid_dtwin_center_key = "ware:easyv:thrid:dtwin:center:key";
	/** 库存 */
	public static final String ware_easyv_thrid_dtwin_center_key_stock = "ware:easyv:thrid:dtwin:center:key:stock";
	/** 生产面积 */
	public static final String ware_easyv_thrid_dtwin_center_key_prosum = "ware:easyv:thrid:dtwin:center:key:prosum";
	/** 能耗 */
	public static final String ware_easyv_thrid_dtwin_center_key_power = "ware:easyv:thrid:dtwin:center:key:power";
	/** 落刀转速超过7天清除锁 */
	public static final String ware_easyv_task_Lock_cutting_gt_seven_clean = "ware:iot:task:Lock:cutting:gt:seven:clean";
	/**
	 * 第三方出库单缓存前缀
	 */
	public static final String ONLINE_DELIIVERY_TASK_ORDERS = "OnlineDeliveryTaskOrders";
	/**
	 * 平台订单redis存储的key
	 */
	public static final String ONLINE_TASK_ORDERS = "OnlineTaskOrders";

	/**
	 * 组装
	 * 
	 * @param key
	 * @param obj
	 * @return
	 */
	public static String format(String key, Object... obj) {
		return String.format(key, obj);
	}

}
