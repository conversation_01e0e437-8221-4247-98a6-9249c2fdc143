/*
 * Copyright 2018 huna.com All right reserved. This software is the confidential and proprietary information of huna.com
 * ("Confidential Information"). You shall not disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into with huna.com.
 */
package com.djcps.ai.common.result;

import cn.hutool.core.collection.CollUtil;
import com.djcps.ai.common.enums.MsgInterface;
import com.djcps.ai.common.enums.SystemCodeEnum;
import com.djcps.ai.common.vo.PageResult;

import java.util.List;

/**
 * 类WebResultExt.java 的实现描述：统一返回类
 *
 * @author: yeb 2021/9/15 下午5:45
 **/
public class WebResultExt<T> {

	private boolean success;
	private String code = ""; // "0/-1"
	private String msg = "";
	private T data;
	public  <T> List<T> fetchPageDataList() {
		if (!this.isSuccess()|| data == null ) {
			return CollUtil.newArrayList();
		}
		if (!(data instanceof PageResult)) {
			return (List<T>) data;
		}
		return ((PageResult) data).getList();
	}

	public static <T> WebResultExt<T> success() {
		return new WebResultExt<>(true);
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static <T> WebResultExt<T> success(String message) {
		return new WebResultExt(true).message(message);
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static <T> WebResultExt<T> success(MsgInterface message) {
		return new WebResultExt(true).code(message.getCode()).message(message.getDesc());
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static <T> WebResultExt<T> success(MsgInterface codeEnum, T data) {
		WebResultExt webResultExt = new WebResultExt(false).code(codeEnum.getCode()).message(codeEnum.getDesc());
		webResultExt.setData(data);
		return webResultExt;
	}

	public static <T> WebResultExt<T> successWithData(T data) {
		WebResultExt webResultExt = success();
		webResultExt.setData(data);
		return webResultExt;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static <T> WebResultExt<T> failure() {
		return new WebResultExt(false).message("系统异常");
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static <T> WebResultExt<T> failure(String errorCode, String message) {
		return new WebResultExt(false).code(errorCode).message(message);
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static <T> WebResultExt<T> failure(SystemCodeEnum codeEnum) {
		return new WebResultExt(false).code(codeEnum.getCode()).message(codeEnum.getDesc());
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static <T> WebResultExt<T> failure(MsgInterface codeEnum) {
		return new WebResultExt(false).code(codeEnum.getCode()).message(codeEnum.getDesc());
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static <T> WebResultExt<T> failure(MsgInterface codeEnum, T data) {
		WebResultExt webResultExt = new WebResultExt(false).code(codeEnum.getCode()).message(codeEnum.getDesc());
		webResultExt.setData(data);
		return webResultExt;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static <T> WebResultExt<T> failure(String message) {
		return new WebResultExt(false).message(message);
	}

	public WebResultExt(boolean success, String code, String message) {
		this.success = success;
		this.code = code;
	}

	public WebResultExt(boolean success, String code, String message, T data) {
		this.success = success;
		this.code = code;
		this.msg = message;
		this.data = data;
	}

	public WebResultExt(boolean success) {
		this.success = success;
		this.code = "10000";
	}

	public WebResultExt() {
	}

	public WebResultExt(boolean success, String message) {
		this(success, "", message);
	}

	public boolean isSuccess() {
		return success;
	}

//	public WebResultExt<T> setSuccess(boolean success) {
//		this.success = success;
//		return this;
//	}

	public String getCode() {
		return code;
	}

	public WebResultExt<T> code(String code) {
		this.code = code;
		return this;
	}

	public String getMsg() {
		return msg;
	}

	public WebResultExt<T> message(String message) {
		this.msg = message;
		return this;
	}

	public WebResultExt(T data) {
		this.code = "10001200";
		this.msg = "请求成功！";
		this.success = true;
		this.data = data;
	}

	/**
	 * @return the data
	 */
	public T getData() {
		return data;
	}

	/**
	 * @param data the data to set
	 */
	public void setData(T data) {
		this.data = data;
	}

	public void setSuccess(boolean success) {
		this.success = success;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

}
