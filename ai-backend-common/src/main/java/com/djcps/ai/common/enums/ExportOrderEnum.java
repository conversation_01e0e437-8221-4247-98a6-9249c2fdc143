package com.djcps.ai.common.enums;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/9/30 17:05
 */
public enum ExportOrderEnum {
    /**
     * 未付款
     */
    NOT_PAY("wait_pay", "未付款"),
    /**
     * 已付款
     */
    PAID("already_pay", "已付款"),
    /**
     * 待导入
     */
    DISTRIBUTED("already_distribute", "待导入"),
    /**
     * 已出库
     */
    IN_BOUND("already_storage", "已入库"),
    /**
     * 运输中
     */
    OUT_BOUND("in_transit", "运输中"),
    /**
     * 已完成
     */
    SIGNED("already_sign", "已收货"),
    /**
     * 已取消
     */
    CANCELED("already_cancel", "已取消"),
    /**
     * 生产中
     */
    IN_PRODUCTION("in_production", "生产中"),
    /**
     * 部分入库
     */
    PART_STORED("part_already_storage", "部分入库"),
    /**
     * 已配货
     */
    HAVE_BEEN_PICKING("already_distribution", "已配货"),
    /**
     * 已提货
     */
    PICKED_UP("already_pick", "已出库"),
    /**
     * 完成装车
     */
    LOADED("already_truck", "已装车"),
    /**
     * 已入库
     */
    ALREADY_DELIVERY("already_delivery", "已送达"),
    /**
     * 状态有误
     */
    DEFAULT("ERROR","状态有误");

    public static String getEmumByCode(String code) {
        for (ExportOrderEnum e : ExportOrderEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getMsg();
            }
        }
        return DEFAULT.getMsg();
    }
    private String msg;
    private String code;

    ExportOrderEnum(String code, String msg) {
        this.msg = msg;
        this.code = code;
    }
    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
