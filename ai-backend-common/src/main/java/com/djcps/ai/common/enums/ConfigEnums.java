package com.djcps.ai.common.enums;

/**
 * <AUTHOR> on 2019/8/6.
 */

public enum ConfigEnums {
    /**
     * 订单标记
     */
    label("订单标记", "label", 1, 1),
    pUserName("客户名称", "puser_name", 1, 2),
    childOrderId("平台订单编号", "order_no", 0, 3),
    productionNo("生产订单编号", "production_no", 0, 4),
    transportNo("运单编号", "waybill_number", 0, 5),
    childPaymentTime("支付时间", "payment_time", 0, 6),
    createTime("下单时间", "create_time", 0, 7),
    status("订单状态", "order_node", 0, 8),
    groupGoodName("产品名称", "product_name", 0, 9),
    boxModel("箱型", "box_model", 0, 10),
    amount("只数", "amount_single", 0, 11),
    amountPiece("片数", "amount_piece", 0, 12),
    inStock("入库数量", "in_stock", 0, 13),
    outStock("出库数量", "out_stock", 0, 14),
    boxLength("长", "box_length", 0, 15),
    boxWidth("宽", "box_width", 0, 16),
    boxHeight("高", "box_height", 0, 17),
    series("连做方式", "series", 0, 18),
    materialLength("落料长", "material_length", 0, 19),
    materialWidth("落料宽", "material_width", 0, 20),
    onlineMaterialWidth("生产用落料宽", "onlineMaterialWidth", 0, 21),
    optimalGateWidth("计价用最优门幅", "optimalGateWidth", 0, 22),
    openingNumber("开数", "openingNumber", 0, 23),
    materialName("材料名称", "materialId", 0, 24),
    fluteType("楞型", "fluteType", 0, 25),
    flayer("层数", "layer", 0, 26),
    staveType("压切方式", "staveType", 0, 27),
    hLineFormula("横压公式", "hline_formula", 0, 28),
    vLineFormula("纵压公式", "vline_formula", 0, 29),
    hLine("横压", "ringCrush", 0, 30),
    vLine("纵压", "endCompression", 0, 31),
    vLinePlus("纵压（1位小数）", "vline_plus", 0, 32),
    productArea("面积", "product_area", 0, 33),
    unitPrice("单价", "unit_price", 0, 34),
    payTypeName("支付方式", "pay_type", 0, 35),
    basAmount("原订单金额", "base_amount", 0, 36),
    disCountAmount("优惠券减免", "discount_amount", 0, 37),
    integralToUse("积分抵用100:1", "integral_to_use", 0, 38),
    amountPrice("实付金额", "amountPaid", 0, 39),
    refundAmount("退款金额", "refundAmount", 0, 40),
    returnIntegralToUse("积分抵用退还100:1", "returnIntegralToUse", 0, 41),
    delivery("交期", "delivery_time", 0, 42),
    manufacturerName("供应商", "manufacturerName", 0, 43),
    consignee("收货人", "consignee", 0, 44),
    contactWay("联系方式", "contact_way", 0, 45),
    addressDetail("收货地址", "address_detail", 0, 46),
    fcodeprovince("区域", "town_name", 0, 47),
    addressRemark("地址备注", "addressre_mark", 0, 48),
    pUserId("下单用户ID", "puser_id", 0, 49),
    phone("认证手机号", "phone", 0, 50),
    groupGoodId("产品ID", "product_id", 0, 51),
    acceptancePremium("承兑补差价", "acceptance_premium", 0, 52),
    orderAmount("订单金额", "orderAmount", 0, 53),
    orderRemark("订单备注", "operation_order_remark", 0, 54),
    /*双刀排产增加字段 scheduleProductionCount ，split*/
    scheduleProductionCount("排产数量", "schedule_production_count", 0, 56),
    ;

    private String cnName;
    private String ename;
    private Integer isMust;
    private Integer order;

    ConfigEnums(String cnName, String ename, Integer isMust, Integer order) {
        this.cnName = cnName;
        this.ename = ename;
        this.isMust = isMust;
        this.order = order;
    }

    public String getCnName() {
        return cnName;
    }

    public void setCnName(String cnName) {
        this.cnName = cnName;
    }

    public String getEname() {
        return ename;
    }

    public void setEname(String ename) {
        this.ename = ename;
    }

    public Integer getIsMust() {
        return isMust;
    }

    public void setIsMust(Integer isMust) {
        this.isMust = isMust;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }
}
