package com.djcps.ai.common.enums;

/**
 * <AUTHOR>
 * @date 2022/8/19.
 * 母订单支付状态
 */
public enum PorderStatusEnum {
    /**
     * 待付款
     */
    WAIT_PAY_STATUS("wait_pay"),
    /**
     * 已付款
     */
    ALREADY_PAY_STATUS("already_pay"),
    /**
     * 已取消
     */
    ALREADY_CANCEL_STATUS("already_cancel");
    private String code;

    private PorderStatusEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
