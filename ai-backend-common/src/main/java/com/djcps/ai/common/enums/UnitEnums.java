package com.djcps.ai.common.enums;

/**
 * <AUTHOR>
 * @data 2019/12/19 12:38
 * @description 单位
 */
public enum UnitEnums {
    /**
     * 数量单位-m^2(m²)
     */
    SQUARE_METER("m²"),
    /**
     * 货币单位-人民币
     */
    RMB("RMB"),
    /**
     * 货币单位-积分-产品那边说这个未用到, 积分抵用也算现金用了
     */
    INTEGRAL("分");
    /**
     * 单位名称
     */
    private String unit;

    public String getUnit() {
        return this.unit;
    }

    UnitEnums(String unit) {
        this.unit = unit;
    }
}
