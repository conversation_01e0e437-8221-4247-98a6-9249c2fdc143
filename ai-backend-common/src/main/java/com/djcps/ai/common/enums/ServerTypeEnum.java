package com.djcps.ai.common.enums;

import com.djcps.ai.common.string.StringUtil;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/8/17 19:56
 */
public enum ServerTypeEnum {
    /**
     * 管理端
     */
    manage_serve_type(0,"manage_serve"),
    /**
     * 供应商端
     */
    supplier_serve_type(1,"supplier_serve")
    ;

    private Integer code;

    private String name;

    public Integer getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    ServerTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据值获取对应名称
     *
     * @param code
     * @return
     */
    public static String nameOf(Integer code) {
        if (code!=null) {
            for (ServerTypeEnum type : ServerTypeEnum.values()) {
                if (code.equals(type.getCode())) {
                    return type.getName();
                }
            }
        }
        return null;
    }

    public static Integer getServerType(String name) {
        if (StringUtil.isNotBlank(name)) {
            for (ServerTypeEnum type : ServerTypeEnum.values()) {
                if (name.equals(type.getName())) {
                    return type.getCode();
                }
            }
        }
        return null;
    }
}
