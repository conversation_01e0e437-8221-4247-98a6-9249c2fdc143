package com.djcps.ai.common.threadlocal;

import com.alibaba.ttl.TransmittableThreadLocal;

import java.util.Optional;

public class AgentThreadLocalUtil {
    private static final TransmittableThreadLocal<AgentInfo> CONTEXT_HOLDER = new TransmittableThreadLocal<>();

    public static String getFunctionName() {
        return Optional.ofNullable(getContext())
                .map(AgentInfo::getAiFunctionName)
                .orElse(null);
    }

    public static String getToken() {
        return Optional.ofNullable(getContext())
                .map(AgentInfo::getToken)
                .orElse(null);
    }

    public static void setContext(AgentInfo context) {
        CONTEXT_HOLDER.set(context);
    }

    public static AgentInfo getContext() {
        return CONTEXT_HOLDER.get();
    }

    public static void removeContext() {
        CONTEXT_HOLDER.remove();
    }

    /**
     * 安全地获取上下文，确保返回非空对象
     * @return 如果存在则返回 AgentInfo 实例，否则返回新的空对象
     */
    public static AgentInfo getContextSafely() {
        AgentInfo context = CONTEXT_HOLDER.get();
        return context != null ? context : new AgentInfo();
    }
}