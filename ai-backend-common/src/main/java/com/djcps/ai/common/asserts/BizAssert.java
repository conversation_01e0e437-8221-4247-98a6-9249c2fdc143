/*
 * Copyright 2018 huna.com All right reserved. This software is the confidential and proprietary information of huna.com
 * ("Confidential Information"). You shall not disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into with huna.com.
 */
package com.djcps.ai.common.asserts;

import com.djcps.ai.common.enums.MsgInterface;
import com.djcps.ai.common.enums.MsgIntface;
import com.djcps.ai.common.enums.SystemCodeEnum;
import com.djcps.ai.common.exception.SystemBizExcetion;
import com.djcps.ai.common.exception.SystemNoExcetion;
import com.djcps.ai.common.string.StringUtil;
import org.springframework.util.Assert;

/*
 * 判断是否为空等校验，配合异常类
 *
 * <AUTHOR>
 * @return
 * @date 2021/9/13
 */
public class BizAssert extends Assert {

	/**
	 * 如果为空抛异常
	 *
	 * @param obj
	 * @param message
	 */
	public static void isNull(Object obj, String message) {
		if (obj == null || (obj instanceof String && StringUtil.isBlank(obj + ""))) {
			throw new SystemBizExcetion(message);
		}
	}

	/**
	 * 如果为空抛异常
	 *
	 * @param obj
	 */
	public static void isNull(Object obj, SystemCodeEnum codeEnum) {
		if (obj == null || (obj instanceof String && StringUtil.isBlank(obj + ""))) {
			throw new SystemBizExcetion(codeEnum.getCode(), codeEnum.getDesc());
		}
	}

	/**
	 * 为真则抛出异常
	 *
	 * @param bool
	 * @param message
	 */
	public static void isTrue(boolean bool, String message) {
		if (bool) {
			throw new SystemBizExcetion(message);
		}
	}

	/**
	 * 为真则抛出异常
	 *
	 * @param bool
	 */
	public static void isTrue(boolean bool, SystemCodeEnum codeEnum) {
		if (bool) {
			throw new SystemBizExcetion(codeEnum.getCode(), codeEnum.getDesc());
		}
	}

	/**
	 * 为真则抛出异常
	 *
	 * @param bool
	 * @param codeEnum
	 */
	public static void isTrue(boolean bool, MsgInterface codeEnum) {
		if (bool) {
			throw new SystemBizExcetion(codeEnum.getCode(), codeEnum.getDesc());
		}
	}

	/**
	 * 为真则抛出异常,codeNo
	 *
	 * @param bool
	 * @param codeEnum
	 */
	public static void isTrue(boolean bool, MsgIntface codeEnum) {
		if (bool) {
			throw new SystemNoExcetion(codeEnum.getCode(), codeEnum.getMsg());
		}
	}
}
