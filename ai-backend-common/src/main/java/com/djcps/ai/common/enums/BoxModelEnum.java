package com.djcps.ai.common.enums;

import com.djcps.ai.common.string.StringUtil;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/8/16 15:39
 */
public enum BoxModelEnum {
    /**
     * 普通箱型
     */
    BOX_MODEL_ORDINARY("ordinary",1),
    /**
     * 全包箱型
     */
    BOX_MODEL_ALL_PACKAGE("all_package",2),
    /**
     * 半包箱型
     */
    BOX_MODEL_HALF_PACKAGE("half_package",3),
    /**
     * 有底无盖箱型
     */
    BOX_MODEL_HAVE_BOTTOM_NO_COVER("have_bottom_no_cover",4),
    /**
     * 有盖无底箱型
     */
    BOX_MODEL_HAVE_COVER_NO_BOTTOM("have_cover_no_bottom",5),
    /**
     * 天地盖箱型
     */
    BOX_MODEL_BOTTOM_COVER("bottom_cover",6),
    /**
     * 其他箱型
     */
    BOX_MODEL_ELSE_BOX("else_box",0),
    ;
    private String code;
    private Integer value;

    BoxModelEnum(String code, Integer value) {
        this.code = code;
        this.value = value;
    }
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    /**
     * 根据code获取value
     *
     * @param code
     * @return
     */
    public static Integer getBoxValue(String code) {
        if (StringUtil.isNotBlank(code)) {
            for (BoxModelEnum type : BoxModelEnum.values()) {
                if (code.equals(type.getCode())) {
                    return type.getValue();
                }
            }
        }
        return null;
    }
    /**
     * 根据value获取code
     *
     * @param value
     * @return
     */
    public static String getEnumCodeByValue(Integer value) {
        if (value != null) {
            for (BoxModelEnum type : BoxModelEnum.values()) {
                if (value.equals(type.getValue())) {
                    return type.getCode();
                }
            }
        }
        return null;
    }
}
