package com.djcps.ai.common.enums;


/**
 * Created by <PERSON><PERSON><PERSON> on 2017/7/6.
 *
 * <AUTHOR>
 */
public enum GrouponOrderEnum implements MsgIntface {
    /**
     * 操作成功
     */
    Order_SUCCESS(10000, "操作成功"),

    Order_FALSE(666667, "操作失败"),

    Order_redisLockFalse(666668, "此订单正在被他人操作，请稍后再试"),
    Order_Update_Error(666670, "订单状态修改失败"),

    Order_FALSEPARA(777774, "数据中有不正经的值，请确认"),

    Order_FALSENULL(777775, "失效订单"),

    Order_FALSEPARAM(777776, "缺少必要参数"),

    Order_FALSEORDER(777777, "下单失败"),

    Order_FALSEVERSION(777778, "版本号错误"),

    Order_FALSEPAY(777779, "支付失败"),

    Order_FALSELIST(777780, "暂无可分发的订单"),

    Order_REPEATPAY(777781, "重复支付"),

    Order_FALSECANCEL(777782, "不允许取消未付款、生产中、已签收、已取消的订单"),

    Order_DistributeFalse(777783, "此组订单中有订单正在分发"),

    Order_FALSEDISTRIBUTION(777782, "只允许分发已付款的订单"),

    Order_FALSEDELIVERY(777783, "只允许签收已发货的订单"),

    Order_FALSEMONTHORDER(777784, "当月无订单"),

    Order_FALSEORDERSTATUS(777785, "查询订单状态有误"),

    Order_REPEATPAY1(777787, "该订单已支付"),

    ORDER_INEXISTENCE(777788, "该订单已支付"),

    ORDER_CANCERED(777786, "该订单已取消!"),

    Order_TIMEFORMATFALSE(999997, "查询时间格式不正确"),

    Order_TIMEFALSE2(999998, "只允许查询前十二个月的订单"),

    Order_TIMEFALSE(999996, "只允许查询前三个月的订单"),
    /**
     * 加載失敗
     */
    Order_LOADFALSE(999999, "加載失敗"),
    ORDER_GODIDFALSE(666669, "查询参数异常!"),
    /**
     * 取消母订单下全部订单失败
     */
    Order_CancelError(777788, "取消母订单下全部订单失败"),
    Order_PayError(777789, "仅能修改未付款的订单"),
    ORDER_CANCELFALSE1(777790, "该待支付订单已取消，不能进行取消操作"),
    ORDER_CANCELFALSE2(777791, "该订单已支付，不能进行取消操作"),
    ORDER_CANCELFALSE3(777792, "该支付订单已取消，不能进行取消操作"),

    ORDER_CANCEL(777799, "该订单已取消，不能进行支付"),
    LABEL_ERROR(777798, "信用订单标记退款异常失败"),
    LABEL_SUCCESS(300001, "信用订单标记退款异常成功"),
    ORDER_PARAM_ERROR(300002, "原订单查询异常"),

    /**
     * 订单稽查
     */
    ORDERLABEL_FALSE(777793, "订单标签变更失败"),
    ORDERLABEL_SUCCESS(777794, "订单标签变更成功"),

    /**
     * 优惠券订单匹配
     */
    MATCHING_SUCCESS(777795, "该优惠券匹配到使用的订单"),
    MATCHING_ERROR(777797, "优惠券匹配订单异常"),
    MATCHING_FALSE(777796, "该优惠券沒有匹配到使用的订单"),
    SHOPPING_CARD_ERROR(888888, "购物车订单重复"),
    PRODUCE_ORDER_ERROR(888889, "该订单已生产，不满足生产条件"),
    DELIVERY_ORDER_ERROR(888890, "订单状态不满足更改条件，或状态已发生更改，请核对后再操作"),
    REPEAT_PRODUCTIONNO_ERROR(888891, "生产编号重复"),
    EXIT_ORDER(888894, "输入的订单号无法找到，请重新确认"),
    UPDATE_ORDER(888895, "该订单异常变更失败"),
    NO_PAID_TO_DELIVERY(888896, "当前订单状态为已付款，不满足更改为运输中条件"),
    NOT_PAID_TO_DELIVERY(888896, "当前订单未排产，请先进行排产！"),
    NO_CANCEL_TO_OPERATE(888897, "当前订单已取消，不满足更改订单条件"),
    NO_DELIVER_TO_DELIVERY(888898, "当前订单已在运输中或不为生产中状态，无法更改订单状态"),
    NO_DELIVER_TO_ERROT(888899, "传入的订单号不存在"),
    PRODUCTION_ERROT(800001, "只有生产中或已入库的订单才能入库"),
    PRODUCTION_RETURN(800002, "只有生产中的订单才能回退状态"),
    DELIVERY_ERROR(800003, "当前状态不具备签收条件"),
    DELIVERY_EXCPS(800004, "订单正在售后处理中，不可签收"),
    DELIVERY_TEN(800005, "运单数量已达10个,不能新增"),
    DELIVERY_TWENTY(800005, "运单数量已达20个,不能新增"),
    DELIVERY_FAILE(800006, "订单发货失败"),
    PRODUCTION_FAILE(800007, "订单排产失败"),


    REPEAT_ORDER(888892, "重单"),
    UNIQUE_ORDER(888893, "非重单"),

    GETORDER_ORDER(888894, "查询订单异常"),
    GETORDER_MORDER(888895, "查询母订单订单异常"),
    GETORDER_CORDER(888896, "查询子订单异常"),
    ORDER_NOT_EXIT(888897, "没有可导出账单"),
    REPLENISH_DELIVERY(111111, "原订单状态为待排产状态，无法确认收货"),
    ORIGINAL_CANCELD(111112, "您发起补价的订单已取消，无法进行补价"),
    THREAD_ORDER(111113, "没有找到第三方订单号"),
    ;

    private int code;
    private String msg;

    @Override
    public String toString() {
        return "UserEnum{" +
                "code=" + code +
                ", msg='" + msg + '\'' +
                '}';
    }

    GrouponOrderEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public void setCode(int code) {
        this.code = code;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }

    @Override
    public MsgIntface setMsg(String message) {
        this.msg = message;
        return this;
    }
}
