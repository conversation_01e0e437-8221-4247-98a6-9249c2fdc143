package com.djcps.ai.common.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/7/15 16:28
 * @description：
 * @modified By：
 * @version: $
 */
@Data
public class CorePageBaseInVo {

    /**
     * 页码
     */
    private int pageNum = 0;

    /**
     * 一页行数
     */
    private int pageSize = 10;

    public int getPageNum() {
        if (pageNum <= 0) {
            return 1;
        } else if (pageNum > 100) {
            return 100;
        }
        return pageNum;
    }

    public void setPageNum(int pageNo) {
        this.pageNum = pageNo;
    }
}
