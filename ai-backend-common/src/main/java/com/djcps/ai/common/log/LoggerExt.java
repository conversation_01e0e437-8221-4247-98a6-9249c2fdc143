/*
 * Copyright 2018 huna.com All right reserved. This software is the confidential and proprietary information of huna.com
 * ("Confidential Information"). You shall not disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into with huna.com.
 */
package com.djcps.ai.common.log;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;

/**
 * 类LoggerExt.java 的实现描述：日志工具类
 *
 * @author: yeb 2021/9/15 下午5:44
 **/
public class LoggerExt {

	/**
	 * info日志
	 * 
	 * @param logger
	 * @param massage
	 */
	public static void info(Logger logger, String massage) {
		if (StringUtils.isBlank(massage))
			return;
		if (logger.isInfoEnabled()) {
			logger.info(massage);
		}
	}

	/**
	 * info日志扩展
	 * 
	 * @param logger
	 * @param massage
	 */
	public static void info(Logger logger, String massage, Object... obj) {
		if (StringUtils.isBlank(massage))
			return;
		if (logger.isInfoEnabled()) {
			logger.info(String.format(massage, obj));
		}
	}

	/**
	 * debug日志
	 * 
	 * @param logger
	 * @param massage
	 */
	public static void debug(Logger logger, String massage) {
		if (StringUtils.isBlank(massage))
			return;
		if (logger.isDebugEnabled()) {
			logger.debug(massage);
		}
	}

	/**
	 * debug日志扩展
	 * 
	 * @param logger
	 * @param massage
	 */
	public static void debug(Logger logger, String massage, Object... obj) {
		if (StringUtils.isBlank(massage))
			return;
		if (logger.isDebugEnabled()) {
			logger.debug(String.format(massage, obj));
		}
	}

	/**
	 * error日志封装
	 * 
	 * @param logger
	 * @param massage
	 * @param e
	 */
	public static void error(Logger logger, String massage, Exception e) {
		if (StringUtils.isBlank(massage))
			return;
		logger.error(massage, e);
	}

	public static void error(Logger logger, Exception e) {
		logger.error(e.getMessage(), e);
	}

	public static void error(Logger logger, Exception e, String massage, Object... obj) {
		if (StringUtils.isBlank(massage))
			return;
		logger.error(String.format(massage, obj), e);
	}

	public static void error(Logger logger, String massage, Object... obj) {
		if (StringUtils.isBlank(massage))
			return;
		logger.error(String.format(massage, obj));
	}

	public static void warn(Logger logger, String massage, Exception e) {
		if (StringUtils.isBlank(massage))
			return;
		logger.warn(massage, e);
	}

	public static void warn(Logger logger, String massage, Object... obj) {
		if (StringUtils.isBlank(massage))
			return;
		logger.warn(String.format(massage, obj));
	}

	public static void error(Logger logger, String massage, Exception e, Object... obj) {
		if (StringUtils.isBlank(massage))
			return;
		logger.warn(String.format(massage, obj), e);
	}

}
