package com.djcps.ai.common.enums;

/**
 * <AUTHOR>
 * @date 2019/12/1013:48
 */
public enum StavetypeEnum {

    /**
     * 毛边不压线
     */
    WOOLCARDBOARDNOLINE(1, "毛边不压线");

    private String statetype;
    private Integer code;

    StavetypeEnum(Integer code, String statetype) {
        this.statetype = statetype;
        this.code = code;
    }

    StavetypeEnum() {
    }

    public String getStatetype() {
        return statetype;
    }

    public void setStatetype(String statetype) {
        this.statetype = statetype;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
}
