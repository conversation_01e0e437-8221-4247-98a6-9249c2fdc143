package com.djcps.ai.common.enums;

import com.djcps.ai.common.string.StringUtil;

/**
 * <AUTHOR>
 * @date 2022/8/15 15:10
 * @description 订单稽查-异常状态枚举
 */
public enum OrderCheckExcpStatusEnum {
    /**
     * 正常
     */
    NORMAL("normal", 0),
    /**
     * 异常
     */
    ABNORMAL("abnormal", 1),
    ;
    private String code;
    private Integer value;

    OrderCheckExcpStatusEnum(String code, Integer value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    /**
     * 根据code获取value
     *
     * @param code
     * @return
     */
    public static Integer getEnumValueByCode(String code) {
        if (StringUtil.isNotBlank(code)) {
            for (OrderCheckExcpStatusEnum type : OrderCheckExcpStatusEnum.values()) {
                if (code.equals(type.getCode())) {
                    return type.getValue();
                }
            }
        }
        return null;
    }

    /**
     * 根据value获取code
     *
     * @param value
     * @return
     */
    public static String getEnumCodeByValue(Integer value) {
        if (value != null) {
            for (OrderCheckExcpStatusEnum type : OrderCheckExcpStatusEnum.values()) {
                if (value.equals(type.getValue())) {
                    return type.getCode();
                }
            }
        }
        return null;
    }
}
