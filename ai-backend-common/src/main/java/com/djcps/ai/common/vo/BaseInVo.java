/*
 * Copyright 2022 51zjb.com All right reserved. This software is the
 * confidential and proprietary information of 51zjb.com ("Confidential
 * Information"). You shall not disclose such Confidential Information and shall
 * use it only in accordance with the terms of the license agreement you entered
 * into with 51zjb.com.
 */
package com.djcps.ai.common.vo;

import lombok.Data;

/**
 * 类BaseInVo.java的实现描述：主键id
 * 
 * <AUTHOR> 2022年8月11日 下午7:39:52
 */
@Data
public class BaseInVo {

	private Long id;

}
