package com.djcps.ai.common.enums;

public enum ResponseMediaTypeEnum {
    TEXT("text", "文本"),
    IMAGE("image", "图片"),
    TABLE("table", "表格"),
    CHART("chart", "图表"),
    OTHER("other", "其他"),
    ;
    private String type;
    private String desc;

    ResponseMediaTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static ResponseMediaTypeEnum getByType(String type) {
        for (ResponseMediaTypeEnum value : values()) {
            if (value.getType().equalsIgnoreCase(type)) {
                return value;
            }
        }
        return CHART;
    }
}
