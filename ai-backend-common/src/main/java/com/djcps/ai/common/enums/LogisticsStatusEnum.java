package com.djcps.ai.common.enums;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/29 14:02
 * @description：
 * @modified By：
 * @version: $
 */
public enum LogisticsStatusEnum {

    /**
     * 运输中
     */
    IN_TRANSIT("in_transit", 5),
    /**
     * 已签收
     */
    ALREADY_SIGN("already_sign", 6),
    ;

    private String code;
    private Integer desc;

    private LogisticsStatusEnum(String code, Integer desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Integer getEnumValueByCode(String code) {
        if (code != null) {
            for (OrderNodeStatusEnum type : OrderNodeStatusEnum.values()) {
                if (code.equals(type.getCode())) {
                    return type.getDesc();
                }
            }
        }
        return null;
    }

    public static String getEnumCodeByValue(Integer desc) {
        if (desc != null) {
            for (LogisticsStatusEnum type : LogisticsStatusEnum.values()) {
                if (desc.equals(type.getDesc())) {
                    return type.getCode();
                }
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getDesc() {
        return desc;
    }

    public void setDesc(Integer desc) {
        this.desc = desc;
    }
}
