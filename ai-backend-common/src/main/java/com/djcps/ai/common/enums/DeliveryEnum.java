package com.djcps.ai.common.enums;

/**
 * FileName: NewMsgTemplate
 *
 * <AUTHOR>   rsw
 * @date: 2021/10/1 22:25
 * @description:
 * @version: 1.0
 */
public enum DeliveryEnum implements MsgInterface {
    /**
     * 参数异常
     */
    PARAM_ERROR("000302001", "参数异常！"),
    /**
     * 操作成功
     */
    OPT_SUCCESS("000302002", "签收成功！"),
    /**
     * 出库单号或运单号异常,无法签收
     */
    NUMBER_ERROR("000302003", "出库单号或运单号异常,无法签收！"),
    /**
     * 操作失败
     */
    FAIL("000302004", "操作失败！"),
    ;

    private String code;
    private String desc;

    private DeliveryEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * @return the code
     */
    @Override
    public String getCode() {
        return code;
    }

    /**
     * @param code the code to set
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * @return the desc
     */
    @Override
    public String getDesc() {
        return desc;
    }

    /**
     * @param desc the desc to set
     */
    public void setDesc(String desc) {
        this.desc = desc;
    }
}
