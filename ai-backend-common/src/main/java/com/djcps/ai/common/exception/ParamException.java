package com.djcps.ai.common.exception;

import com.djcps.ai.common.enums.SystemCodeEnum;

/**
 * 类ParamException.java 的实现描述：参数异常,请求接口一般防止异常
 *
 * @author: yeb 2021/9/13 下午5:37
 **/
public class ParamException extends RuntimeException {

	private static final long serialVersionUID = 2111773257318706128L;
	/**
	 * 异常编码
	 */
	private String code = SystemCodeEnum.LACK_PARAM.getCode();
	private String message;

	public ParamException() {
	}

	public ParamException(String code, String message, Throwable cause) {
		super(message, cause);
		this.code = code;
		this.message = message;
	}

	public ParamException(String message) {
		super(message);
		this.message = message;
	}

	public ParamException(SystemCodeEnum codeEnum) {
		super(codeEnum.getDesc());
		this.code = codeEnum.getCode();
		this.message = codeEnum.getDesc();
	}

	public ParamException(Throwable cause) {
		super(cause);
	}

	/**
	 * @return the message
	 */
	@Override
	public String getMessage() {
		return message;
	}

	/**
	 * @param message the message to set
	 */
	public void setMessage(String message) {
		this.message = message;
	}

	/**
	 * @return the code
	 */
	public String getCode() {
		return code;
	}

	/**
	 * @param code the code to set
	 */
	public void setCode(String code) {
		this.code = code;
	}
}
