package com.djcps.ai.common.enums;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/8/17 14:22
 */
public enum OrderStatusEnum {
    /**
     * 待付款
     */
    WAIT_PAY_STATUS("wait_pay"),
    /**
     * 已付款
     */
    ALREADY_PAY_STATUS("already_pay"),
    /**
     * 生产中
     */
    IN_PRODUCTION_STATUS("in_production"),
    /**
     * 运输中
     */
    IN_TRANSIT_STATUS("in_transit"),
    /**
     * 已签收
     */
    ALREADY_SIGN_STATUS("already_sign")
    ;

    private String code;

    private OrderStatusEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
