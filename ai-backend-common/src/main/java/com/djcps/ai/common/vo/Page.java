/*
 * Copyright 2022 51zjb.com All right reserved. This software is the
 * confidential and proprietary information of 51zjb.com ("Confidential
 * Information"). You shall not disclose such Confidential Information and shall
 * use it only in accordance with the terms of the license agreement you entered
 * into with 51zjb.com.
 */
package com.djcps.ai.common.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.List;

/**
 * 类Page.java的实现描述：分页返回数据
 * 
 * <AUTHOR> 2022年7月7日 下午3:56:13
 */
public class Page<T> {

	// 分页查询开始记录位置
	@JsonIgnore
	private int begin;
	// 分页查看下结束位置
	@JsonIgnore
	private int end;
	// 每页显示记录数
	private int rows;
	// 总共页数,老版本需要注意，和total相反
	private int count;
	// 当前页码
	private int page;
	// 查询结果总记录数
	private int total;
	/**
	 * 兼容老的订单服务逻辑返回字段
	 */
	private int totalCount;
	private int totalSize;

	private List<T> list;
	private List<T> content;

	private static final int DEFAULT_LENGTH = 200;

	public Page() {
	}

	/**
	 * 转换对象
	 * 
	 * @param data
	 */
	public void convert(CorePage<T> data) {
		this.setTotal(data.getTotal());
		this.setList(data.getList());
	}

	/**
	 * 构造函数
	 * @param page
	 * @param rows
	 */
	public Page(int page, int rows) {
		this.page = page;
		if (page <= 0) {
			this.page = 1;
		}
		if (rows == 0) {
			this.rows = DEFAULT_LENGTH;
		} else {
			if(rows == 10001 || rows <= 200 || rows == 500 || rows == 1000){
				this.rows = rows;
			} else {
				if (rows > 200) {
					this.rows = DEFAULT_LENGTH;
				}
			}
		}
		int offset = rows * (page - 1);
		this.begin = offset > 0 ? offset : 0;
		this.end = this.begin + this.rows;
		// this.page = (int) Math.floor((this.begin * 1.0d) / this.rows) + 1;
	}

	public static <T> Page<T> initPage(Page<T> page) {
		return new Page<T>(page.getPage(), page.getRows());
	}

	/**
	 *
	 * @param page
	 * @param length
	 * @param total
	 */
	public Page(int page, int length, int total) {
		this(page, length);
		this.total = total;
		this.totalCount = total;
		this.totalSize = total;
	}

	public int getBegin() {
		return this.begin;
	}

	public int getEnd() {
		return end;
	}

	public void setEnd(int end) {
		this.end = end;
	}

	public void setBegin(int begin) {
		this.begin = begin;
		if (this.rows != 0) {
			this.page = (int) Math.floor((this.begin * 1.0d) / this.rows) + 1;
		}
	}

	public void setRows(int rows) {
		this.rows = rows;
		if (this.begin != 0) {
			this.page = (int) Math.floor((this.begin * 1.0d) / this.rows) + 1;
		}
	}

	public int getCount() {
		if (count == 0) {
			return 1;
		}
		return count;
	}

	public void setTotal(int total) {
		this.total = total;
		this.totalCount = total;
		this.totalSize = total;
		this.count = (int) Math.floor((this.total * 1.0d) / this.rows);
		if (this.total % this.rows != 0) {
			this.count++;
		}
	}

	public int getTotal() {
		return total;
	}
	public int getTotalSize() {
		return total;
	}
	public void setCount(int count) {
		this.count = count;
	}

	public int getPage() {
		return page;
	}

	public void setPage(int page) {
		int offset = rows * (page - 1);
		this.begin = offset > 0 ? offset : 0;
		this.end = this.begin + this.rows;
		this.page = page;
	}

	public int getRows() {
		return rows;
	}

	public List<T> getList() {
		return list;
	}
	public List<T> getContent() {
		return content;
	}
	public void setList(List<T> list) {
		this.list = list;
	}

	public void setContent(List<T> content) {
		this.content = content;
	}
}
