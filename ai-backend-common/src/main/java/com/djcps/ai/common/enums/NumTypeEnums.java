package com.djcps.ai.common.enums;

/**
 * 状态枚举
 *
 * @author: 张明辉
 * @Date: 2020/1/6
 */
public enum NumTypeEnums {

	/**
	 * 魔法值-2
	 */
	MINUS_TWO(-2),

	/**
	 * 魔法值-1
	 */
	MINUS_ONE(-1),
	/**
	 * 魔法值0
	 */
	ZERO(0),
	/**
	 * 魔法值1
	 */
	ONE(1),
	/**
	 * 魔法值2
	 */
	TWO(2),
	/**
	 * 魔法值3
	 */
	THREE(3),
	/**
	 * 魔法值4
	 */
	FOUR(4),
	/**
	 * 魔法值5
	 */
	FIVE(5),
	/**
	 * 魔法值6
	 */
	SIX(6),
	/**
	 * 魔法值7
	 */
	SEVEN(7),
	/**
	 * 魔法值8
	 */
	EIGHT(8),
	/**
	 * 魔法值9
	 */
	NINE(9),

	/**
	 * 魔法值10
	 */
	TEN(10),

	/**
	 * 魔法值11
	 */
	ELEVEN(11),

	/**
	 * 魔法值12
	 */
	TWELVE(12),

	/**
	 * 魔法值13
	 */
	THIRTENN(13),

	/**
	 * 魔法值14
	 */
	FOURTEEN(14),

	/**
	 * 魔法值15
	 */
	FIFTEEN(15),

	/**
	 * 魔法值16
	 */
	SIXTEEN(16),
	/**
	 * 魔法值20
	 */
	TWENTY(20),
	/**
	 * 魔法值30
	 */
	THIRTY(30),
	/**
	 * 魔法值40
	 */
	FORTY(40),
	/**
	 * 魔法值60
	 */
	SIXTY(60),
	/**
	 * 魔法值80
	 */
	EIGHTY(80),
	/**
	 * 魔法值90
	 */
	NINETY(90),
	/**
	 * 魔法值88
	 */
	EIGHTY_EIGHT(88),
	/**
	 * 魔法值100
	 */
	HUNDRED(100),
	/**
	 * 魔法值70
	 */
	SEVENTY(70),
	/**
	 * 魔法值50
	 */
	FIFTY(50),
	/**
	 * 魔法值29
	 */
	TWENTY_NINE(29),
	/**
	 * 魔法值18
	 */
	EIGHTEEN(18),
	/**
	 * 魔法值3000
	 */
	THREET(3000),

	/**
	 * 魔法值3600
	 */
	THREETSIX(3600),

	/**
	 * 魔法值8000
	 */
	EIGHTTHOUSAND(8000),
	/**
	 * 魔法值10000
	 */
	TENT(10000),
	/**
	 * 魔法值1000000
	 */
	MILLION(1000000),
	/**
	 * 魔法值-100
	 */
	NEGATIVE_HUNDRED(-100),;

	private final Integer value;

	NumTypeEnums(Integer value) {
		this.value = value;
	}

	public Integer getValue() {
		return value;
	}
}
