package com.djcps.ai.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 
 * 类CarStateEnum.java的实现描述：车辆状态枚举
 * 
 * <AUTHOR> 2021年10月19日 下午1:40:32
 */
public enum OrderNodeStatusEnum {
	/**
	 * 待付款
	 */
	WAIT_PAY_STATUS("wait_pay", 1),
	/**
	 * 已付款
	 */
	ALREADY_PAY_STATUS("already_pay", 2),
	/**
	 * 已分发
	 */
	ALREADY_DISTRIBUTE_STATUS("already_distribute", 3),
	/**
	 * 已入库
	 */
	ALREADY_STORAGE_STATUS("already_storage", 4),
	/**
	 * 运输中
	 */
	IN_TRANSIT_STATUS("in_transit", 5),
	/**
	 * 已签收
	 */
	ALREADY_SIGN_STATUS("already_sign", 6),
	/**
	 * 已取消
	 */
	ALREADY_CANCEL_STATUS("already_cancel", 7),
	/**
	 * 生产中
	 */
	IN_PRODUCTION_STATUS("in_production", 9),
	/**
	 * 部分入库
	 */
	PART_ALREADY_STORAGE_STATUS("part_already_storage", 21),
	/**
	 * 已配货
	 */
	ALREADY_DISTRIBUTION_STATUS("already_distribution", 23),
	/**
	 * 已提货
	 */
	ALREADY_PICK_STATUS("already_pick", 24),
	/**
	 * 已装车
	 */
	ALREADY_TRUCK_STATUS("already_truck", 25),
	/**
	 * 已送达
	 */
	ALREADY_DELIVERY_STATUS("already_delivery", 32),

	/**
	 * 客户已删除
	 */
	USER_DELETE("user_delete", 100),;

	private String code;
	private Integer desc;

	private OrderNodeStatusEnum(String code, Integer desc) {
		this.code = code;
		this.desc = desc;
	}

	public static Integer getEnumValueByCode(String code) {
		if (code != null) {
			for (OrderNodeStatusEnum type : OrderNodeStatusEnum.values()) {
				if (code.equals(type.getCode())) {
					return type.getDesc();
				}
			}
		}
		return null;
	}

	public static String getEnumCodeByValue(Integer desc) {
		if (desc != null) {
			for (OrderNodeStatusEnum type : OrderNodeStatusEnum.values()) {
				if (desc.equals(type.getDesc())) {
					return type.getCode();
				}
			}
		}
		return null;
	}

	/**
	 * 验证是否在某个节点
	 * 
	 * @param code
	 * @param enums
	 * @return
	 */
	public static boolean validateCode(String code, OrderNodeStatusEnum... enums) {
		if (StringUtils.isNotBlank(code) && enums != null) {
			for (OrderNodeStatusEnum type : enums) {
				if (code.equals(type.getCode())) {
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * 验证是否在某个节点
	 * 
	 * @param code
	 * @param enums
	 * @return
	 */
	public static boolean validateDesc(String desc, OrderNodeStatusEnum... enums) {
		if (StringUtils.isNotBlank(desc) && enums != null) {
			Integer number = Integer.valueOf(desc);
			for (OrderNodeStatusEnum type : enums) {
				if (type.getDesc().equals(number)) {
					return true;
				}
			}
		}
		return false;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public Integer getDesc() {
		return desc;
	}

	public void setDesc(Integer desc) {
		this.desc = desc;
	}
}
