/*
 * Copyright 2018 me.com All right reserved. This software is the confidential and proprietary information of me.com
 * ("Confidential Information"). You shall not disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into with me.com.
 */
package com.djcps.ai.common.threadlocal;

import lombok.Data;

/* 
 * 常用参数
 * <AUTHOR>
 * @date 2021/9/7
 */
@Data
public class CommonParam {

	private Long spendtime;
	private String url;
	private String userId;
	private String authorization;
	private String version;
	private String lat;
	private String lon;
	private String device;
	private String ip;
	private Long starttime;
	private Long endtime;
	private String message;
	private String source;
	private String system;
	private String fphone;
	private String params;
	private String token;

	private UserInfo userInfo;

	/**
	 * @return the spendtime
	 */
	public Long getSpendtime() {
		if (starttime != null && endtime != null) {
			return endtime - starttime;
		}
		return spendtime;
	}

	/**
	 * @param spendtime the spendtime to set
	 */
	public void setSpendtime(Long spendtime) {
		this.spendtime = spendtime;
	}

}
