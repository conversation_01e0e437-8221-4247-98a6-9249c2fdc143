package com.djcps.ai.common.enums;

/**
 * 普通箱型
 *
 * @author:wangxiu
 * @date:2022/8/23
 */
public enum BoxTypeIntEnum {
    /**
     * 普通箱型
     */
    PTXX(1, "普通箱型"),
    QBXX(2, "全包箱型"),
    BBXX(3, "半包箱型"),
    YDWGXX(4, "有底无盖箱型"),
    YGWDXX(5, "有盖无底箱型"),
    TDGXX(6, "天地盖箱型"),
    DEFAULT(7, "其他箱型");
    private int code;
    private String name;

    BoxTypeIntEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public static String getBoxTypeEnumByCode(int code) {
        for (BoxTypeIntEnum data : BoxTypeIntEnum.values()) {
            if (data.getCode() == code) {
                return data.getName();
            }
        }
        return DEFAULT.getName();
    }

    public static int getBoxCodeEnumByName(String name) {
        for (BoxTypeIntEnum data : BoxTypeIntEnum.values()) {
            if (data.getName().equals(name)) {
                return data.getCode();
            }
        }
        return DEFAULT.getCode();
    }
}
