package com.djcps.ai.common.enums;

/**
 * <AUTHOR>
 * @date 2017/12/4
 */
public enum OperateRecordEnum {

    //提交了此笔订单
    SAVEORDER("提交订单"),
    //更改交期
    UPDATE_DELIVERY("更改交期"),
    //客户取消了此笔订单
    CANCELORDERS("客户取消了此笔订单"),
    //取消了此笔订单
    CANCERORDER("客户取消订单"),
    //支付了此笔订单
    PAYORDER("支付订单"),
    //标记重复订单
    REPEATORDER("标记重复订单"),
    //回传出库状态
    TRANSPORTING("回传出库状态"),
    //签收了此笔订单
    DELIVERYORDER("客户签收订单"),
    //修改订单
    CHANGEORDER("修改订单"),
    //系统取消订单
    DELIVERYORDEROneHOUR("系统取消订单"),
    EAS_ADD_OFFLINE_ORDER("EAS系统导入订单"),
    ADD_OFFLINE_ORDER("新增订单"),
    CANCEL_REPEAT_TYPE("取消重复标记"),
    EDIT_ORDER_REMARK("订单备注"),
    AUDIT_ORDER("审核订单"),
    CLOSE_OFFLINE_ORDER("关闭订单"),
    OUT_OFFLINE_ORDER("导出订单"),
    THE_CUSTOMER_HIMSELF("客户本人"),
    MYSELF("本人"),
    AUTOMATIC_RECEIVING("已送达7天后自动签收"),
    IMPORT_OFFLINE_ORDER("导入订单"),
    REPEAT_ORDER("标记重复订单"),
    /**
     * 第三方对接推送（confirmDistributionOrderNew）和对接排查推送（autoAssociatedProductionNo）
     */
    AUTO_ASSOCIATED_PRODUCTION_NUMBER("推送生产编号"),
    //客户签收出库单
    DELIVERY_OUTBOUND_ORDER("客户签收出库单"),
    /**
     * mq:message.service.oms.insert.operaterecord.test写入，只有对接排产系统有，标准生产开始
     */
    INTO_PRODUCTION("汇入生产"),
    /**
     * mq:message.service.oms.insert.operaterecord.test写入，只有对接排产系统有，标准生产完结
     */
    ORDER_COMPLETED_OFFLINE("订单完工下线"),
    /**
     * mq:message.service.oms.insert.operaterecord.test写入，只有对接排产系统有
     */
    ORDER_IS_PUT_IN_STORAGE("订单入库"),
    ;

    private String record;

    OperateRecordEnum(String record) {
        this.record = record;
    }

    public String getRecord() {
        return record;
    }

    public void setRecord(String record) {
        this.record = record;
    }
}
