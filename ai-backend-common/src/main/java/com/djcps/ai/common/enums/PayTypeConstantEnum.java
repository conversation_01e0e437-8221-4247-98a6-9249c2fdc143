package com.djcps.ai.common.enums;

import com.djcps.ai.common.string.StringUtil;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/8/12 16:54
 */
public enum PayTypeConstantEnum {
    /**
     * 余额（数据库存balance）
     */
    PAY_TYPE_BALANCE("balance", 0),
    /**
     * 微信(wechat)
     */
    PAY_TYPE_WECHAT("wechat", 1),
    /**
     * 支付宝（alipay）
     */
    PAY_TYPE_ALIPAY("alipay", 2),
    /**
     * 银行(bank)
     */
    PAY_TYPE_BANK("bank", 3),
    /**
     * 壹钱包(wallet)
     */
    PAY_TYPE_WALLET("wallet", 4),
    /**
     * 企业系-台州(taizhou_bank)
     */
    PAY_TYPE_TAIZHOU_BANK("taizhou_bank", 5),
    /**
     * 企业系-厦门(xiamen_bank)
     */
    PAY_TYPE_XIAMEN_BANK("xiamen_bank", 6),
    /**
     * 承兑支付(accept)
     */
    PAY_TYPE_ACCEPT("accept", 7),
    /**
     * 微信+余额
     */
    PAY_TYPE_WECHAT_ADD_BALANCE("wechat_add_balance", 10),
    /**
     * 支付宝+余额
     */
    PAY_TYPE_ALIPAY_ADD_BALANCE("alipay_add_balance", 20),
    /**
     * 银行卡+余额
     */
    PAY_TYPE_BLANK_ADD_BALANCE("blank_add_balance", 30),
    /**
     * 壹钱包+余额
     */
    PAY_TYPE_WALLET_ADD_BALANCE("wallet_add_balance", 40);;

    private String code;
    private Integer value;

    PayTypeConstantEnum(String code, Integer value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    /**
     * 根据code获取value
     *
     * @param code
     * @return
     */
    public static Integer getEnumValueByCode(String code) {
        if (StringUtil.isNotBlank(code)) {
            for (PayTypeConstantEnum type : PayTypeConstantEnum.values()) {
                if (code.equals(type.getCode())) {
                    return type.getValue();
                }
            }
        }
        return null;
    }

    /**
     * 根据code获取value
     *
     * @param value
     * @return
     */
    public static String getEnumValueByValue(Integer value) {
        if (value != null) {
            for (PayTypeConstantEnum type : PayTypeConstantEnum.values()) {
                if (value.equals(type.getValue())) {
                    return type.getCode();
                }
            }
        }
        return null;
    }
}
