package com.djcps.ai.common.enums;

/**
 * <AUTHOR> rsw
 * @date 2022/8/23 20:12. 订单类型枚举
 */
public enum OrderTypeEnum {
	/**
	 * 平台纸板订单
	 */
	GROUP_ORDER("group_order"),

	/**
	 * 线上订单
	 */
	ONLINE_ORDER("online_order"),

	/**
	 * 线下订单
	 */
	OFFLINE_ORDER("offline_order"),

	;

	private String code;

	private OrderTypeEnum(String code) {
		this.code = code;
	}

	/**
	 * 根据code获取订单类型
	 * 
	 * @param code
	 * @return
	 */
	public static OrderTypeEnum getEmumByCode(Integer code) {
		for (OrderTypeEnum e : OrderTypeEnum.values()) {
			if (e.getCode().equals(code)) {
				return e;
			}
		}
		return null;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}
}
