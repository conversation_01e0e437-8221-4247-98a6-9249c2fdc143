package com.djcps.ai.common.enums;

/**
 * <AUTHOR> @date 2017/12/4
 */
public enum CustomerOrManEnum {
    /**
     * 客户操作
     */
    Customer("客户操作", 2),
    Manager("平台管理员", 1),
    System("系统", 3),
    ORDER_CONTROLLER("订单中心", 4),
    ORDER_MANAGE("订单管理", 5),
    PRODUCTION_MANAGEMENT("生产管理", 6);
    private String operator;
    private int code;

    CustomerOrManEnum(String operator, int code) {
        this.operator = operator;
        this.code = code;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }
}
