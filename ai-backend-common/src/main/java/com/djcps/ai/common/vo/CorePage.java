/*
 * Copyright 2022 51zjb.com All right reserved. This software is the
 * confidential and proprietary information of 51zjb.com ("Confidential
 * Information"). You shall not disclose such Confidential Information and shall
 * use it only in accordance with the terms of the license agreement you entered
 * into with 51zjb.com.
 */
package com.djcps.ai.common.vo;

import lombok.Data;

import java.util.List;

/**
 * 类CorePage.java的实现描述：core层对接使用只有总数和集合的返参
 * 
 * <AUTHOR> 2022年7月15日 下午12:31:22
 */
@Data
public class CorePage<T> {

	// 查询结果总记录数
	private int total;

	private List<T> list;

	public CorePage() {
	}
	public CorePage(int total,List<T> list){
		this.total = total;
		this.list = list;
	}
}
