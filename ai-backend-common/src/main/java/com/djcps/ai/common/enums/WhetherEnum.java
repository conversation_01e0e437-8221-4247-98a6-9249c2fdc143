package com.djcps.ai.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 类Wheather的实现描述：TODO 类实现描述
 *
 * <AUTHOR> 2021/9/13 11:38
 */
public enum WhetherEnum {

	是("y", "是"), 否("n", "否"),

	;

	private String code;
	private String desc;

	private WhetherEnum(String code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	public static WhetherEnum getWhetherEnumByCode(String code) {
		if (StringUtils.isNotBlank(code)) {
			for (WhetherEnum type : WhetherEnum.values()) {
				if (code.equals(type.getCode())) {
					return type;
				}
			}
		}
		return null;
	}

	/**
	 * @return the code
	 */
	public String getCode() {
		return code;
	}

	/**
	 * @param code the code to set
	 */
	public void setCode(String code) {
		this.code = code;
	}

	/**
	 * @return the desc
	 */
	public String getDesc() {
		return desc;
	}

	/**
	 * @param desc the desc to set
	 */
	public void setDesc(String desc) {
		this.desc = desc;
	}
}
