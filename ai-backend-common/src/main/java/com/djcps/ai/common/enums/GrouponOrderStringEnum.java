package com.djcps.ai.common.enums;


/**
 * Created by <PERSON><PERSON><PERSON> on 2017/7/6.
 *
 * <AUTHOR>
 */
public enum GrouponOrderStringEnum implements MsgInterface {
    /**
     *
     * 操作成功
     */
    ORDER_SUCCESS("10000", "操作成功"),

    ORDER_FALSE("666667", "操作失败"),
    ;


    private String code;
    private String desc;



    GrouponOrderStringEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return null;
    }


}
