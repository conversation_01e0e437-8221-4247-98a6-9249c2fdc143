/*
PageBaseInVo * Copyright 2018 me.com All right reserved. This software is the confidential and proprietary information of me.com
 * ("Confidential Information"). You shall not disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into with me.com.
 */
package com.djcps.ai.common.exception;

import com.djcps.ai.common.enums.SystemCodeEnum;

/**
 * 类SystemBizExcetion.java 的实现描述：异常类
 *
 * @author: yeb 2021/9/13 下午5:37
 **/
public class SystemBizExcetion extends RuntimeException {

	private static final long serialVersionUID = 5964759405889919141L;
	/**
	 * 异常编码
	 */
	private String code = "-1";
	/**
	 * 提示信息
	 */
	private String message;

	public SystemBizExcetion() {
	}

	public SystemBizExcetion(String code, String message) {
		super(message);
		this.code = code;
		this.message = message;
	}

	public SystemBizExcetion(SystemCodeEnum codeEnum) {
		super(codeEnum.getDesc());
		this.code = codeEnum.getCode();
		this.message = codeEnum.getDesc();
	}

	public SystemBizExcetion(String message) {
		super(message);
		this.message = message;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	/**
	 * @return the message
	 */
	@Override
	public String getMessage() {
		return message;
	}

	/**
	 * @param message the message to set
	 */
	public void setMessage(String message) {
		this.message = message;
	}
}
