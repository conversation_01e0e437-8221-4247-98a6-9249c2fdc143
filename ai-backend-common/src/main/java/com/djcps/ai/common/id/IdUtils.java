/*
 * Copyright 2019 me.com All right reserved. This software is the confidential and proprietary information of me.com
 * ("Confidential Information"). You shall not disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into with me.com.
 */
package com.djcps.ai.common.id;

import com.djcps.ai.common.log.LoggerExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetAddress;

import static com.djcps.ai.common.utils.DateUtil.fomartToNumber;

/**
 * 类IdUtils.java 的实现描述：id唯一生成
 *
 * @author: yeb 2021/9/15 下午5:38
 **/
public class IdUtils {

	private static volatile IdUtils idUtils = null;

	public static IdUtils getIdUtils() {
		if (idUtils == null) {
			synchronized (IdUtils.class) {
				if (idUtils == null) {
					idUtils = new IdUtils();
				}
			}
		}
		return idUtils;
	}

	private static Logger logger = LoggerFactory.getLogger(IdUtils.class);

	// ==============================Fields===========================================
	/** 开始时间截 (2021-09-23) */
	private final long twepoch = 1632326400000L;

	/** 机器id所占的位数 */
	private static final long workerIdBits = 8L;

	/** 数据标识id所占的位数 */
	private static final long datacenterIdBits = 8L;

	/** 支持的最大机器id，结果是31--63 (这个移位算法可以很快的计算出几位二进制数所能表示的最大十进制数) */
	private static final long maxWorkerId = -1L ^ (-1L << workerIdBits);

	/** 支持的最大数据标识id，结果是31 */
	private static final long maxDatacenterId = -1L ^ (-1L << datacenterIdBits);

	/** 序列在id中占的位数 */
	private final long sequenceBits = 6L;

	/** 机器ID向左移12位--6 */
	private final long workerIdShift = sequenceBits;

	/** 数据标识id向左移17位(12+5)--(6+8) */
	private final long datacenterIdShift = sequenceBits + workerIdBits;

	/** 时间截向左移22位(5+5+12)--(8+8+6) */
	private final long timestampLeftShift = sequenceBits + workerIdBits + datacenterIdBits;

	/** 生成序列的掩码，这里为63 (0b111111111111=0xfff=4095) */
	private final long sequenceMask = -1L ^ (-1L << sequenceBits);

	/** 工作机器ID(0~255) */
	private static long workerId;

	/** 数据中心ID(0~255) */
	private static long datacenterId;

	/** 毫秒内序列(0~63) */
	private long sequence = 1L;

	/** 上次生成ID的时间截 */
	private long lastTimestamp = -1L;

	/**
	 * 获取Long
	 * 
	 * @return
	 */
	public Long getNextLongId() {
		IdVo idVo = this.nextId();
		return idVo.getNextLongId();
	}

	/**
	 * 获取id对象
	 * 
	 * @return
	 */
	public IdVo getIdVo() {
		return this.nextId();
	}

	/**
	 * 获取
	 * 
	 * @return
	 */
	public String getNextStringId() {
		IdVo idVo = this.nextId();
		return idVo.getNextStringId();
	}

	static {
		try {
			InetAddress addr = InetAddress.getLocalHost();
			String ip = addr.getHostAddress().toString();
			// workerId = Long.valueOf(ip.replace(".", "")) % 31;
			// 扩展到0-255，
			String[] ips = ip.split("\\.");
			datacenterId = Long.valueOf(ips[2]);
			workerId = Long.valueOf(ips[3]);
			LoggerExt.info(logger, "------------------------------------------");
			LoggerExt.info(logger, "-----------------初始化datacenterId:%s--------------", datacenterId);
			LoggerExt.info(logger, "-----------------初始化workerId:%s--------------", workerId);
			LoggerExt.info(logger, "------------------------------------------");
			if (workerId > maxWorkerId || workerId < 0) {
				throw new IllegalArgumentException(
						String.format("worker Id can't be greater than %d or less than 0", maxWorkerId));
			}
			if (datacenterId > maxDatacenterId || datacenterId < 0) {
				throw new IllegalArgumentException(
						String.format("datacenter Id can't be greater than %d  or less than 0", 9));
			}
		} catch (Exception e) {
			LoggerExt.error(logger, "{result:%s}", "ip获取异常，唯一id无法生成，系统强制关闭", e);
			throw new RuntimeException("id create exception");
		}
	}

	// ==============================Constructors=====================================
	/**
	 * 构造函数
	 *
	 */
	private IdUtils() {

	}

	// ==============================Methods==========================================
	/**
	 * 获得下一个ID (该方法是线程安全的)
	 * 
	 * @return IdVo
	 */
	public synchronized IdVo nextId() {
		long timestamp = timeGen();
		// 如果当前时间小于上一次ID生成的时间戳，说明系统时钟回退过这个时候应当抛出异常
		if (timestamp < lastTimestamp) {
			throw new RuntimeException(String.format(
					"Clock moved backwards.  Refusing to generate id for %d milliseconds", lastTimestamp - timestamp));
		}

		// 如果是同一时间生成的，则进行毫秒内序列
		if (lastTimestamp == timestamp) {
			sequence = (sequence + 1) & sequenceMask;
			// 毫秒内序列溢出
			if (sequence == 1) {
				// 阻塞到下一个毫秒,获得新的时间戳
				timestamp = tilNextMillis(lastTimestamp);
			}
		}
		// 时间戳改变，毫秒内序列重置
		else {
			sequence = 1L;
		}

		// 上次生成ID的时间截
		lastTimestamp = timestamp;

		IdVo idVo = new IdVo();
		idVo.setNextLongId(((timestamp - twepoch) << timestampLeftShift) | (datacenterId << datacenterIdShift)
				| (workerId << workerIdShift) | sequence);
		idVo.setNextStringId(new StringBuffer(fomartToNumber(timestamp)).append(datacenterId).append(workerId)
				.append(String.format("%04d", this.sequence)).toString());
		// 移位并通过或运算拼到一起组成64位的ID
		return idVo;
	}

	/**
	 * 阻塞到下一个毫秒，直到获得新的时间戳
	 * 
	 * @param lastTimestamp 上次生成ID的时间截
	 * @return 当前时间戳
	 */
	protected long tilNextMillis(long lastTimestamp) {
		long timestamp = timeGen();
		while (timestamp <= lastTimestamp) {
			timestamp = timeGen();
		}
		return timestamp;
	}

	/**
	 * 返回以毫秒为单位的当前时间
	 * 
	 * @return 当前时间(毫秒)
	 */
	protected long timeGen() {
		return System.currentTimeMillis();
	}

	// ==============================Test=============================================
	/** 测试 */
	public static void main(String[] args) {
		// System.out.println(System.currentTimeMillis());
		IdUtils idWorker = new IdUtils();
		for (int i = 0; i < 512; i++) {
			IdVo id = idWorker.nextId();
			System.out.println(id.getNextLongId() + "\t" + id.getNextStringId());
//            System.out.println(id.getNextLongId().toString().length());
//            System.out.println(id.getNextStringId());
		}
	}

}
